{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "emitDeclarationOnly": false, "noEmit": false}, "include": ["src/core/**/*", "src/lib/agent-helpers.ts", "src/lib/tool-helpers.ts", "src/tools/types.ts", "src/mcp/types.ts", "src/index.ts"], "exclude": ["src/**/*.test.ts", "src/**/*.spec.ts", "tests/**/*", "examples/**/*", "docs/**/*", "node_modules", "dist"]}