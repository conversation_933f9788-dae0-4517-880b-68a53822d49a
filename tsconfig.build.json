{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "emitDeclarationOnly": false, "noEmit": false}, "include": ["src/**/*"], "exclude": ["src/**/*.test.ts", "src/**/*.spec.ts", "tests/**/*", "examples/**/*", "docs/**/*", "node_modules", "dist", "src/tools/**/*", "src/orchestration/**/*"]}