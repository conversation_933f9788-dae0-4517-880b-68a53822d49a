/**
 * Code Analysis MCP Tool
 * 
 * Provides comprehensive code analysis capabilities including syntax checking,
 * complexity analysis, security scanning, and code quality metrics.
 */

import { z } from 'zod';
import { BaseTool } from '../base.js';
import { 
  MCPTool, 
  MCPToolConfig, 
  MCPToolContext, 
  MCPToolExecutionResult,
  MCPCapabilities,
  MCPToolCategory
} from './types.js';

const CodeAnalysisInputSchema = z.object({
  code: z.string().describe('Source code to analyze'),
  language: z.string().optional().describe('Programming language (auto-detected if not provided)'),
  analysisType: z.enum(['syntax', 'complexity', 'security', 'quality', 'dependencies', 'all']).default('all').describe('Type of analysis to perform'),
  options: z.object({
    includeMetrics: z.boolean().default(true).describe('Include detailed metrics'),
    includeSuggestions: z.boolean().default(true).describe('Include improvement suggestions'),
    securityLevel: z.enum(['basic', 'standard', 'strict']).default('standard').describe('Security analysis level'),
    complexityThreshold: z.number().default(10).describe('Complexity threshold for warnings')
  }).optional()
});

const CodeAnalysisOutputSchema = z.object({
  language: z.string(),
  analysisType: z.string(),
  results: z.object({
    syntax: z.object({
      valid: z.boolean(),
      errors: z.array(z.object({
        line: z.number(),
        column: z.number(),
        message: z.string(),
        severity: z.enum(['error', 'warning', 'info'])
      })),
      warnings: z.array(z.object({
        line: z.number(),
        column: z.number(),
        message: z.string(),
        rule: z.string().optional()
      }))
    }).optional(),
    complexity: z.object({
      cyclomaticComplexity: z.number(),
      cognitiveComplexity: z.number(),
      linesOfCode: z.number(),
      functions: z.array(z.object({
        name: z.string(),
        complexity: z.number(),
        startLine: z.number(),
        endLine: z.number()
      }))
    }).optional(),
    security: z.object({
      vulnerabilities: z.array(z.object({
        type: z.string(),
        severity: z.enum(['low', 'medium', 'high', 'critical']),
        line: z.number().optional(),
        description: z.string(),
        recommendation: z.string()
      })),
      score: z.number().min(0).max(100)
    }).optional(),
    quality: z.object({
      score: z.number().min(0).max(100),
      metrics: z.object({
        maintainabilityIndex: z.number(),
        technicalDebt: z.string(),
        codeSmells: z.number(),
        duplicatedLines: z.number(),
        testCoverage: z.number().optional()
      }),
      suggestions: z.array(z.string())
    }).optional(),
    dependencies: z.object({
      imports: z.array(z.string()),
      exports: z.array(z.string()),
      external: z.array(z.object({
        name: z.string(),
        version: z.string().optional(),
        type: z.enum(['npm', 'pip', 'gem', 'maven', 'nuget', 'other'])
      }))
    }).optional()
  }),
  summary: z.object({
    overallScore: z.number().min(0).max(100),
    issues: z.number(),
    recommendations: z.array(z.string()),
    estimatedFixTime: z.string()
  })
});

type CodeAnalysisInput = z.infer<typeof CodeAnalysisInputSchema>;
type CodeAnalysisOutput = z.infer<typeof CodeAnalysisOutputSchema>;

export class CodeAnalysisTool extends BaseTool<CodeAnalysisInput, CodeAnalysisOutput> implements MCPTool<CodeAnalysisInput, CodeAnalysisOutput> {
  
  constructor() {
    const config: MCPToolConfig = {
      name: 'code_analysis',
      title: 'Code Analysis',
      description: 'Comprehensive code analysis including syntax, complexity, security, and quality metrics',
      category: MCPToolCategory.CODE,
      tags: ['code', 'analysis', 'quality', 'security', 'complexity'],
      version: '1.0.0',
      author: 'AG3NTIC Framework',
      capabilities: {
        callTool: true,
        logging: true
      },
      examples: [
        {
          description: 'Analyze JavaScript code quality',
          input: {
            code: 'function calculateTotal(items) { let total = 0; for(let i = 0; i < items.length; i++) { total += items[i].price; } return total; }',
            language: 'javascript',
            analysisType: 'quality'
          }
        }
      ]
    };

    super(config, CodeAnalysisInputSchema, CodeAnalysisOutputSchema);
  }

  getCapabilities(): MCPCapabilities {
    return {
      callTool: true,
      logging: true,
      listTools: true
    };
  }

  async execute(input: CodeAnalysisInput, context?: MCPToolContext): Promise<MCPToolExecutionResult> {
    try {
      const language = input.language || this.detectLanguage(input.code);
      const results = await this.performAnalysis(input, language);
      
      const result: CodeAnalysisOutput = {
        language,
        analysisType: input.analysisType,
        results,
        summary: this.generateSummary(results)
      };

      return {
        success: true,
        data: result,
        content: [
          {
            type: 'text',
            text: this.formatAnalysisResult(result)
          }
        ]
      };

    } catch (error) {
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Code analysis failed'
      );
    }
  }

  private detectLanguage(code: string): string {
    // Simple language detection based on syntax patterns
    if (code.includes('function') && code.includes('{')) return 'javascript';
    if (code.includes('def ') && code.includes(':')) return 'python';
    if (code.includes('public class') || code.includes('import java')) return 'java';
    if (code.includes('#include') || code.includes('int main')) return 'c++';
    if (code.includes('fn ') && code.includes('->')) return 'rust';
    return 'unknown';
  }

  private async performAnalysis(input: CodeAnalysisInput, language: string): Promise<any> {
    const results: any = {};

    if (input.analysisType === 'all' || input.analysisType === 'syntax') {
      results.syntax = this.analyzeSyntax(input.code, language);
    }

    if (input.analysisType === 'all' || input.analysisType === 'complexity') {
      results.complexity = this.analyzeComplexity(input.code, language);
    }

    if (input.analysisType === 'all' || input.analysisType === 'security') {
      results.security = this.analyzeSecurity(input.code, language, input.options?.securityLevel);
    }

    if (input.analysisType === 'all' || input.analysisType === 'quality') {
      results.quality = this.analyzeQuality(input.code, language);
    }

    if (input.analysisType === 'all' || input.analysisType === 'dependencies') {
      results.dependencies = this.analyzeDependencies(input.code, language);
    }

    return results;
  }

  private analyzeSyntax(code: string, language: string): any {
    // Mock syntax analysis
    const lines = code.split('\n');
    const errors: any[] = [];
    const warnings: any[] = [];

    // Simple syntax checks
    lines.forEach((line, index) => {
      if (language === 'javascript') {
        if (line.includes('var ')) {
          warnings.push({
            line: index + 1,
            column: line.indexOf('var') + 1,
            message: 'Consider using let or const instead of var',
            rule: 'no-var'
          });
        }
        if (line.includes('==') && !line.includes('===')) {
          warnings.push({
            line: index + 1,
            column: line.indexOf('==') + 1,
            message: 'Use strict equality (===) instead of loose equality (==)',
            rule: 'eqeqeq'
          });
        }
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  private analyzeComplexity(code: string, language: string): any {
    const lines = code.split('\n').filter(line => line.trim().length > 0);
    const linesOfCode = lines.length;
    
    // Count control structures for cyclomatic complexity
    const controlStructures = (code.match(/\b(if|else|while|for|switch|case|catch|&&|\|\|)\b/g) || []).length;
    const cyclomaticComplexity = controlStructures + 1;
    
    // Mock cognitive complexity (simplified)
    const cognitiveComplexity = Math.floor(cyclomaticComplexity * 1.2);

    // Extract functions (simplified)
    const functionMatches = code.match(/function\s+(\w+)|(\w+)\s*=\s*function|(\w+)\s*=>\s*{/g) || [];
    const functions = functionMatches.map((match, index) => ({
      name: `function_${index + 1}`,
      complexity: Math.floor(Math.random() * 10) + 1,
      startLine: index * 5 + 1,
      endLine: index * 5 + 10
    }));

    return {
      cyclomaticComplexity,
      cognitiveComplexity,
      linesOfCode,
      functions
    };
  }

  private analyzeSecurity(code: string, language: string, level: string = 'standard'): any {
    const vulnerabilities: any[] = [];
    let score = 100;

    // Common security patterns
    const securityPatterns = [
      { pattern: /eval\s*\(/, type: 'Code Injection', severity: 'high' as const, description: 'Use of eval() can lead to code injection' },
      { pattern: /innerHTML\s*=/, type: 'XSS', severity: 'medium' as const, description: 'Direct innerHTML assignment can lead to XSS' },
      { pattern: /document\.write\s*\(/, type: 'XSS', severity: 'medium' as const, description: 'document.write can be exploited for XSS' },
      { pattern: /password\s*=\s*["'][^"']+["']/, type: 'Hardcoded Credentials', severity: 'critical' as const, description: 'Hardcoded passwords detected' }
    ];

    const lines = code.split('\n');
    securityPatterns.forEach(({ pattern, type, severity, description }) => {
      lines.forEach((line, index) => {
        if (pattern.test(line)) {
          vulnerabilities.push({
            type,
            severity,
            line: index + 1,
            description,
            recommendation: `Consider using safer alternatives for ${type.toLowerCase()}`
          });
          
          // Reduce score based on severity
          const severityPenalty = { low: 5, medium: 15, high: 25, critical: 40 };
          score -= severityPenalty[severity];
        }
      });
    });

    return {
      vulnerabilities,
      score: Math.max(0, score)
    };
  }

  private analyzeQuality(code: string, language: string): any {
    const lines = code.split('\n');
    const linesOfCode = lines.filter(line => line.trim().length > 0).length;
    
    // Calculate basic quality metrics
    const avgLineLength = lines.reduce((sum, line) => sum + line.length, 0) / lines.length;
    const longLines = lines.filter(line => line.length > 120).length;
    const emptyLines = lines.filter(line => line.trim().length === 0).length;
    
    // Mock quality calculations
    let maintainabilityIndex = 100;
    maintainabilityIndex -= longLines * 2; // Penalty for long lines
    maintainabilityIndex -= (avgLineLength > 80 ? 10 : 0); // Penalty for high average line length
    maintainabilityIndex = Math.max(0, Math.min(100, maintainabilityIndex));
    
    const codeSmells = longLines + (avgLineLength > 100 ? 1 : 0);
    const duplicatedLines = Math.floor(linesOfCode * 0.05); // Mock 5% duplication
    
    const suggestions = [];
    if (longLines > 0) suggestions.push('Break down long lines for better readability');
    if (avgLineLength > 80) suggestions.push('Consider reducing average line length');
    if (codeSmells > 3) suggestions.push('Address code smells to improve maintainability');
    
    const score = Math.floor((maintainabilityIndex + (100 - codeSmells * 10)) / 2);
    
    return {
      score: Math.max(0, Math.min(100, score)),
      metrics: {
        maintainabilityIndex,
        technicalDebt: `${Math.floor(codeSmells * 0.5)}h`,
        codeSmells,
        duplicatedLines,
        testCoverage: Math.floor(Math.random() * 40) + 60 // Mock coverage
      },
      suggestions
    };
  }

  private analyzeDependencies(code: string, language: string): any {
    const imports: string[] = [];
    const exports: string[] = [];
    const external: any[] = [];

    // Extract imports/requires based on language
    const lines = code.split('\n');
    lines.forEach(line => {
      if (language === 'javascript') {
        const importMatch = line.match(/import\s+.*\s+from\s+['"]([^'"]+)['"]/);
        const requireMatch = line.match(/require\s*\(\s*['"]([^'"]+)['"]\s*\)/);
        const exportMatch = line.match(/export\s+(?:default\s+)?(\w+)/);
        
        if (importMatch) {
          imports.push(importMatch[1]);
          if (!importMatch[1].startsWith('.')) {
            external.push({
              name: importMatch[1],
              type: 'npm' as const
            });
          }
        }
        if (requireMatch) {
          imports.push(requireMatch[1]);
          if (!requireMatch[1].startsWith('.')) {
            external.push({
              name: requireMatch[1],
              type: 'npm' as const
            });
          }
        }
        if (exportMatch) {
          exports.push(exportMatch[1]);
        }
      }
    });

    return {
      imports,
      exports,
      external
    };
  }

  private generateSummary(results: any): any {
    let overallScore = 100;
    let issues = 0;
    const recommendations: string[] = [];

    // Calculate overall score and issues
    if (results.syntax) {
      issues += results.syntax.errors.length + results.syntax.warnings.length;
      if (results.syntax.errors.length > 0) overallScore -= 20;
      if (results.syntax.warnings.length > 0) overallScore -= results.syntax.warnings.length * 2;
    }

    if (results.security) {
      issues += results.security.vulnerabilities.length;
      overallScore = Math.min(overallScore, results.security.score);
    }

    if (results.quality) {
      overallScore = Math.min(overallScore, results.quality.score);
      recommendations.push(...results.quality.suggestions);
    }

    if (results.complexity) {
      if (results.complexity.cyclomaticComplexity > 10) {
        recommendations.push('Consider breaking down complex functions');
        overallScore -= 10;
      }
    }

    // Estimate fix time
    const estimatedHours = Math.ceil(issues * 0.5);
    const estimatedFixTime = estimatedHours > 0 ? `${estimatedHours}h` : '< 1h';

    return {
      overallScore: Math.max(0, Math.min(100, overallScore)),
      issues,
      recommendations: [...new Set(recommendations)].slice(0, 5),
      estimatedFixTime
    };
  }

  private formatAnalysisResult(result: CodeAnalysisOutput): string {
    let output = `# Code Analysis Report\n\n`;
    output += `**Language:** ${result.language}\n`;
    output += `**Analysis Type:** ${result.analysisType}\n`;
    output += `**Overall Score:** ${result.summary.overallScore}/100\n`;
    output += `**Issues Found:** ${result.summary.issues}\n`;
    output += `**Estimated Fix Time:** ${result.summary.estimatedFixTime}\n\n`;

    if (result.results.syntax) {
      output += `## Syntax Analysis\n`;
      output += `**Valid:** ${result.results.syntax.valid}\n`;
      output += `**Errors:** ${result.results.syntax.errors.length}\n`;
      output += `**Warnings:** ${result.results.syntax.warnings.length}\n\n`;
    }

    if (result.results.complexity) {
      output += `## Complexity Analysis\n`;
      output += `**Cyclomatic Complexity:** ${result.results.complexity.cyclomaticComplexity}\n`;
      output += `**Cognitive Complexity:** ${result.results.complexity.cognitiveComplexity}\n`;
      output += `**Lines of Code:** ${result.results.complexity.linesOfCode}\n\n`;
    }

    if (result.results.security) {
      output += `## Security Analysis\n`;
      output += `**Security Score:** ${result.results.security.score}/100\n`;
      output += `**Vulnerabilities:** ${result.results.security.vulnerabilities.length}\n\n`;
    }

    if (result.results.quality) {
      output += `## Quality Analysis\n`;
      output += `**Quality Score:** ${result.results.quality.score}/100\n`;
      output += `**Maintainability Index:** ${result.results.quality.metrics.maintainabilityIndex}\n`;
      output += `**Technical Debt:** ${result.results.quality.metrics.technicalDebt}\n\n`;
    }

    if (result.summary.recommendations.length > 0) {
      output += `## Recommendations\n\n`;
      result.summary.recommendations.forEach(rec => {
        output += `- ${rec}\n`;
      });
    }

    return output;
  }
}
