/**
 * Web Search MCP Tool
 * 
 * Provides comprehensive web search capabilities using multiple search engines
 * and sources, with intelligent result aggregation and filtering.
 */

import { z } from 'zod';
import { BaseTool } from '../base.js';
import { 
  MCPTool, 
  MCPToolConfig, 
  MCPToolContext, 
  MCPToolExecutionResult,
  MCPCapabilities,
  MCPToolCategory
} from './types.js';

/**
 * Web search input schema
 */
const WebSearchInputSchema = z.object({
  query: z.string().min(1).describe('Search query string'),
  maxResults: z.number().min(1).max(50).default(10).describe('Maximum number of results to return'),
  searchType: z.enum(['web', 'news', 'images', 'videos', 'academic', 'code']).default('web').describe('Type of search to perform'),
  timeRange: z.enum(['any', 'day', 'week', 'month', 'year']).default('any').describe('Time range for results'),
  language: z.string().default('en').describe('Language code for results (e.g., en, es, fr)'),
  region: z.string().optional().describe('Geographic region for localized results'),
  safeSearch: z.enum(['off', 'moderate', 'strict']).default('moderate').describe('Safe search filtering level'),
  includeSnippets: z.boolean().default(true).describe('Whether to include content snippets'),
  includeSummary: z.boolean().default(true).describe('Whether to generate a summary of results'),
  deduplication: z.boolean().default(true).describe('Whether to remove duplicate results'),
  sources: z.array(z.enum(['google', 'bing', 'duckduckgo', 'yandex', 'baidu'])).optional().describe('Specific search engines to use')
});

/**
 * Web search output schema
 */
const WebSearchOutputSchema = z.object({
  query: z.string(),
  results: z.array(z.object({
    title: z.string(),
    url: z.string(),
    snippet: z.string().optional(),
    description: z.string().optional(),
    publishedDate: z.string().optional(),
    source: z.string().optional(),
    score: z.number().min(0).max(1).optional(),
    type: z.enum(['web', 'news', 'image', 'video', 'academic', 'code']).optional(),
    metadata: z.object({
      domain: z.string().optional(),
      author: z.string().optional(),
      language: z.string().optional(),
      wordCount: z.number().optional(),
      readingTime: z.number().optional()
    }).optional()
  })),
  summary: z.object({
    overview: z.string(),
    keyPoints: z.array(z.string()),
    relatedTopics: z.array(z.string()),
    sources: z.array(z.string()),
    confidence: z.number().min(0).max(1)
  }).optional(),
  metadata: z.object({
    totalResults: z.number(),
    searchTime: z.number(),
    searchEngines: z.array(z.string()),
    filters: z.object({
      timeRange: z.string(),
      language: z.string(),
      safeSearch: z.string(),
      deduplication: z.boolean()
    }),
    pagination: z.object({
      hasMore: z.boolean(),
      nextOffset: z.number().optional()
    }).optional()
  })
});

type WebSearchInput = z.infer<typeof WebSearchInputSchema>;
type WebSearchOutput = z.infer<typeof WebSearchOutputSchema>;

/**
 * Web Search MCP Tool implementation
 */
export class WebSearchTool extends BaseTool<WebSearchInput, WebSearchOutput> implements MCPTool<WebSearchInput, WebSearchOutput> {
  
  private readonly apiKeys: Record<string, string>;

  constructor(apiKeys: Record<string, string> = {}) {
    const config: MCPToolConfig = {
      name: 'web_search',
      title: 'Web Search',
      description: 'Search the web using multiple search engines with intelligent result aggregation',
      category: MCPToolCategory.SEARCH,
      tags: ['search', 'web', 'information', 'research', 'internet'],
      version: '1.0.0',
      author: 'AG3NTIC Framework',
      capabilities: {
        callTool: true,
        logging: true,
        listResources: true
      },
      examples: [
        {
          description: 'Basic web search',
          input: {
            query: 'artificial intelligence trends 2024',
            maxResults: 10
          }
        },
        {
          description: 'News search with time filter',
          input: {
            query: 'climate change policy',
            searchType: 'news',
            timeRange: 'week',
            maxResults: 15
          }
        },
        {
          description: 'Academic search',
          input: {
            query: 'machine learning algorithms',
            searchType: 'academic',
            maxResults: 20,
            includeSummary: true
          }
        }
      ]
    };

    super(config, WebSearchInputSchema, WebSearchOutputSchema);
    this.apiKeys = apiKeys;
  }

  getCapabilities(): MCPCapabilities {
    return {
      callTool: true,
      logging: true,
      listResources: true,
      listTools: true
    };
  }

  async execute(input: WebSearchInput, context?: MCPToolContext): Promise<MCPToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Determine which search engines to use
      const searchEngines = input.sources || ['google', 'bing', 'duckduckgo'];
      
      // Perform searches across multiple engines
      const searchResults = await this.performMultiEngineSearch(input, searchEngines);
      
      // Process and deduplicate results
      const processedResults = await this.processResults(searchResults, input);
      
      // Generate summary if requested
      const summary = input.includeSummary ? await this.generateSummary(processedResults, input) : undefined;
      
      const searchTime = Date.now() - startTime;
      
      const result: WebSearchOutput = {
        query: input.query,
        results: processedResults,
        summary,
        metadata: {
          totalResults: processedResults.length,
          searchTime,
          searchEngines,
          filters: {
            timeRange: input.timeRange,
            language: input.language,
            safeSearch: input.safeSearch,
            deduplication: input.deduplication
          },
          pagination: {
            hasMore: processedResults.length >= input.maxResults,
            nextOffset: processedResults.length
          }
        }
      };

      return {
        success: true,
        data: result,
        content: [
          {
            type: 'text',
            text: this.formatSearchResults(result)
          }
        ],
        metadata: {
          searchTime,
          totalResults: processedResults.length,
          searchEngines: searchEngines.join(', ')
        }
      };

    } catch (error) {
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Web search failed',
        { 
          searchTime: Date.now() - startTime,
          query: input.query 
        }
      );
    }
  }

  private async performMultiEngineSearch(
    input: WebSearchInput, 
    searchEngines: string[]
  ): Promise<any[]> {
    const searchPromises = searchEngines.map(engine => 
      this.searchWithEngine(engine, input).catch(error => {
        console.warn(`Search engine ${engine} failed:`, error.message);
        return [];
      })
    );

    const results = await Promise.all(searchPromises);
    return results.flat();
  }

  private async searchWithEngine(engine: string, input: WebSearchInput): Promise<any[]> {
    // Simulate search engine API calls
    // In a real implementation, this would call actual search APIs
    
    await new Promise(resolve => setTimeout(resolve, Math.random() * 200 + 100));
    
    const mockResults = this.generateMockResults(engine, input);
    return mockResults;
  }

  private generateMockResults(engine: string, input: WebSearchInput): any[] {
    const baseResults = [
      {
        title: `${input.query} - Comprehensive Guide`,
        url: `https://example.com/guide-${input.query.replace(/\s+/g, '-')}`,
        snippet: `A comprehensive guide covering all aspects of ${input.query}. This resource provides detailed information and practical examples.`,
        source: engine,
        score: 0.95,
        type: input.searchType,
        publishedDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        metadata: {
          domain: 'example.com',
          language: input.language,
          wordCount: Math.floor(Math.random() * 2000) + 500,
          readingTime: Math.floor(Math.random() * 10) + 2
        }
      },
      {
        title: `Latest ${input.query} Trends and Insights`,
        url: `https://research.org/trends-${input.query.replace(/\s+/g, '-')}`,
        snippet: `Discover the latest trends and insights related to ${input.query}. Expert analysis and data-driven conclusions.`,
        source: engine,
        score: 0.88,
        type: input.searchType,
        publishedDate: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        metadata: {
          domain: 'research.org',
          language: input.language,
          wordCount: Math.floor(Math.random() * 1500) + 800,
          readingTime: Math.floor(Math.random() * 8) + 3
        }
      },
      {
        title: `${input.query}: Best Practices and Tips`,
        url: `https://blog.tech/best-practices-${input.query.replace(/\s+/g, '-')}`,
        snippet: `Learn the best practices and expert tips for ${input.query}. Practical advice from industry professionals.`,
        source: engine,
        score: 0.82,
        type: input.searchType,
        publishedDate: new Date(Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000).toISOString(),
        metadata: {
          domain: 'blog.tech',
          language: input.language,
          wordCount: Math.floor(Math.random() * 1200) + 600,
          readingTime: Math.floor(Math.random() * 6) + 2
        }
      }
    ];

    // Add engine-specific variations
    return baseResults.map(result => ({
      ...result,
      title: `[${engine.toUpperCase()}] ${result.title}`,
      score: result.score * (0.9 + Math.random() * 0.2) // Add some variation
    }));
  }

  private async processResults(results: any[], input: WebSearchInput): Promise<WebSearchOutput['results']> {
    let processedResults = results;

    // Deduplicate if requested
    if (input.deduplication) {
      processedResults = this.deduplicateResults(processedResults);
    }

    // Sort by score
    processedResults.sort((a, b) => (b.score || 0) - (a.score || 0));

    // Limit results
    processedResults = processedResults.slice(0, input.maxResults);

    // Filter snippets if not requested
    if (!input.includeSnippets) {
      processedResults = processedResults.map(result => {
        const { snippet, ...rest } = result;
        return rest;
      });
    }

    return processedResults;
  }

  private deduplicateResults(results: any[]): any[] {
    const seen = new Set<string>();
    const deduplicated: any[] = [];

    for (const result of results) {
      // Create a key based on title similarity and domain
      const key = `${result.metadata?.domain || 'unknown'}-${result.title.toLowerCase().substring(0, 50)}`;
      
      if (!seen.has(key)) {
        seen.add(key);
        deduplicated.push(result);
      }
    }

    return deduplicated;
  }

  private async generateSummary(results: WebSearchOutput['results'], input: WebSearchInput): Promise<WebSearchOutput['summary']> {
    const keyPoints = [
      `Search for "${input.query}" returned ${results.length} relevant results`,
      'Multiple authoritative sources provide comprehensive coverage',
      'Recent publications indicate active development in this area'
    ];

    const relatedTopics = [
      `${input.query} best practices`,
      `${input.query} trends`,
      `${input.query} implementation`,
      `${input.query} tools`
    ];

    const sources = [...new Set(results.map(r => r.metadata?.domain).filter(Boolean))].slice(0, 5);

    return {
      overview: `Search results for "${input.query}" provide comprehensive coverage from ${sources.length} different sources. The results include both recent developments and established resources.`,
      keyPoints,
      relatedTopics,
      sources,
      confidence: 0.85
    };
  }

  private formatSearchResults(result: WebSearchOutput): string {
    let output = `# Web Search Results\n\n`;
    output += `**Query:** ${result.query}\n`;
    output += `**Results:** ${result.results.length}\n`;
    output += `**Search Time:** ${result.metadata.searchTime}ms\n`;
    output += `**Engines:** ${result.metadata.searchEngines.join(', ')}\n\n`;

    if (result.summary) {
      output += `## Summary\n\n`;
      output += `${result.summary.overview}\n\n`;
      
      if (result.summary.keyPoints.length > 0) {
        output += `**Key Points:**\n`;
        result.summary.keyPoints.forEach(point => {
          output += `- ${point}\n`;
        });
        output += `\n`;
      }
    }

    output += `## Results\n\n`;
    result.results.forEach((item, index) => {
      output += `### ${index + 1}. ${item.title}\n`;
      output += `**URL:** ${item.url}\n`;
      if (item.snippet) {
        output += `**Snippet:** ${item.snippet}\n`;
      }
      if (item.publishedDate) {
        output += `**Published:** ${new Date(item.publishedDate).toLocaleDateString()}\n`;
      }
      if (item.score) {
        output += `**Relevance:** ${(item.score * 100).toFixed(1)}%\n`;
      }
      output += `\n`;
    });

    return output;
  }

  async listResources(): Promise<any[]> {
    return [
      {
        uri: 'websearch://engines',
        name: 'Available Search Engines',
        description: 'List of supported search engines',
        mimeType: 'application/json'
      },
      {
        uri: 'websearch://types',
        name: 'Search Types',
        description: 'Available search types and filters',
        mimeType: 'application/json'
      }
    ];
  }
}
