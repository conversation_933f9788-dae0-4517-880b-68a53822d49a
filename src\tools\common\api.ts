import { z } from 'zod';
import { BaseTool } from '../base.js';
import { ToolConfig, ToolContext, ToolExecutionResult, ToolCategory } from '../types.js';

/**
 * REST API call tool input schema
 */
const RestApiCallInputSchema = z.object({
  url: z.string().url('Must be a valid URL'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).default('GET'),
  headers: z.record(z.string()).optional(),
  body: z.union([z.string(), z.object({}).passthrough()]).optional(),
  timeout: z.number().min(1000).max(60000).optional().default(10000),
  followRedirects: z.boolean().optional().default(true),
  validateSSL: z.boolean().optional().default(true)
});

type RestApiCallInput = z.infer<typeof RestApiCallInputSchema>;

/**
 * REST API call tool
 */
export class RestApiCallTool extends BaseTool<RestApiCallInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'rest_api_call',
      title: 'REST API Call',
      description: 'Make REST API calls to external services',
      category: ToolCategory.API,
      tags: ['api', 'rest', 'http', 'web service'],
      examples: [
        {
          description: 'GET request to fetch data',
          input: { url: 'https://api.example.com/users', method: 'GET' }
        },
        {
          description: 'POST request with JSON body',
          input: {
            url: 'https://api.example.com/users',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: { name: 'John Doe', email: '<EMAIL>' }
          }
        }
      ]
    };

    super(config, RestApiCallInputSchema, undefined);
  }

  async execute(input: RestApiCallInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), input.timeout);

      const requestInit: RequestInit = {
        method: input.method,
        headers: input.headers,
        signal: controller.signal,
        redirect: input.followRedirects ? 'follow' : 'manual'
      };

      if (input.body && (input.method === 'POST' || input.method === 'PUT' || input.method === 'PATCH')) {
        if (typeof input.body === 'string') {
          requestInit.body = input.body;
        } else {
          requestInit.body = JSON.stringify(input.body);
          if (!input.headers?.['Content-Type']) {
            requestInit.headers = {
              ...requestInit.headers,
              'Content-Type': 'application/json'
            };
          }
        }
      }

      const response = await fetch(input.url, requestInit);
      clearTimeout(timeoutId);

      const contentType = response.headers.get('content-type') || '';
      let responseData: any;

      if (contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      const result = {
        url: input.url,
        method: input.method,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers as any),
        data: responseData,
        contentType,
        success: response.ok
      };

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `API Call: ${input.method} ${input.url}\nStatus: ${response.status} ${response.statusText}\nResponse: ${JSON.stringify(responseData, null, 2)}`
        }
      ]);

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return this.createErrorResult(`Request timed out after ${input.timeout}ms`);
      }
      
      return this.createErrorResult(
        `API call failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}

/**
 * GraphQL query tool input schema
 */
const GraphQLQueryInputSchema = z.object({
  endpoint: z.string().url('Must be a valid URL'),
  query: z.string().min(1, 'GraphQL query is required'),
  variables: z.record(z.any()).optional(),
  headers: z.record(z.string()).optional(),
  timeout: z.number().min(1000).max(60000).optional().default(10000)
});

type GraphQLQueryInput = z.infer<typeof GraphQLQueryInputSchema>;

/**
 * GraphQL query tool
 */
export class GraphQLQueryTool extends BaseTool<GraphQLQueryInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'graphql_query',
      title: 'GraphQL Query',
      description: 'Execute GraphQL queries against GraphQL endpoints',
      category: ToolCategory.API,
      tags: ['graphql', 'api', 'query'],
      examples: [
        {
          description: 'Simple GraphQL query',
          input: {
            endpoint: 'https://api.example.com/graphql',
            query: 'query { users { id name email } }'
          }
        },
        {
          description: 'GraphQL query with variables',
          input: {
            endpoint: 'https://api.example.com/graphql',
            query: 'query GetUser($id: ID!) { user(id: $id) { id name email } }',
            variables: { id: '123' }
          }
        }
      ]
    };

    super(config, GraphQLQueryInputSchema, undefined);
  }

  async execute(input: GraphQLQueryInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), input.timeout);

      const requestBody = {
        query: input.query,
        variables: input.variables
      };

      const response = await fetch(input.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...input.headers
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const responseData = await response.json();

      const result = {
        endpoint: input.endpoint,
        query: input.query,
        variables: input.variables,
        status: response.status,
        data: responseData.data,
        errors: responseData.errors,
        success: response.ok && !responseData.errors
      };

      if (responseData.errors) {
        return this.createErrorResult(
          `GraphQL errors: ${responseData.errors.map((e: any) => e.message).join(', ')}`,
          result
        );
      }

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `GraphQL Query executed successfully\nData: ${JSON.stringify(responseData.data, null, 2)}`
        }
      ]);

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return this.createErrorResult(`Request timed out after ${input.timeout}ms`);
      }
      
      return this.createErrorResult(
        `GraphQL query failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}

/**
 * Webhook sender tool input schema
 */
const WebhookSenderInputSchema = z.object({
  url: z.string().url('Must be a valid webhook URL'),
  payload: z.record(z.any()),
  headers: z.record(z.string()).optional(),
  secret: z.string().optional(),
  timeout: z.number().min(1000).max(30000).optional().default(10000),
  retries: z.number().min(0).max(5).optional().default(0)
});

type WebhookSenderInput = z.infer<typeof WebhookSenderInputSchema>;

/**
 * Webhook sender tool
 */
export class WebhookSenderTool extends BaseTool<WebhookSenderInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'webhook_sender',
      title: 'Webhook Sender',
      description: 'Send webhook notifications to external services',
      category: ToolCategory.API,
      tags: ['webhook', 'notification', 'http'],
      examples: [
        {
          description: 'Send simple webhook',
          input: {
            url: 'https://hooks.example.com/webhook',
            payload: { event: 'user_created', user_id: '123' }
          }
        },
        {
          description: 'Send webhook with signature',
          input: {
            url: 'https://hooks.example.com/webhook',
            payload: { event: 'order_completed', order_id: '456' },
            secret: 'webhook_secret_key'
          }
        }
      ]
    };

    super(config, WebhookSenderInputSchema, undefined);
  }

  async execute(input: WebhookSenderInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= input.retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), input.timeout);

        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'User-Agent': 'AG3NTIC-Webhook/1.0',
          ...input.headers
        };

        // Add signature if secret is provided
        if (input.secret) {
          const payload = JSON.stringify(input.payload);
          // In a real implementation, you'd use crypto to create HMAC signature
          headers['X-Webhook-Signature'] = `sha256=${input.secret}`;
        }

        const response = await fetch(input.url, {
          method: 'POST',
          headers,
          body: JSON.stringify(input.payload),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        const result = {
          url: input.url,
          payload: input.payload,
          status: response.status,
          statusText: response.statusText,
          attempt: attempt + 1,
          success: response.ok
        };

        if (response.ok) {
          return this.createSuccessResult(result, [
            {
              type: 'text',
              text: `Webhook sent successfully to ${input.url}\nStatus: ${response.status}\nAttempt: ${attempt + 1}/${input.retries + 1}`
            }
          ]);
        } else {
          lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (error instanceof Error && error.name === 'AbortError') {
          lastError = new Error(`Request timed out after ${input.timeout}ms`);
        }
      }

      // Wait before retry (exponential backoff)
      if (attempt < input.retries) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    return this.createErrorResult(
      `Webhook failed after ${input.retries + 1} attempts: ${lastError?.message || 'Unknown error'}`
    );
  }
}
