#!/usr/bin/env node

/**
 * Package preparation script for AG3NTIC
 * 
 * Validates package configuration and prepares for publishing
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Preparing AG3NTIC package for publishing...\n');

// Validation checks
const checks = [
  validatePackageJson,
  validateTypeScriptConfig,
  validateSourceFiles,
  validateTests,
  validateDocumentation,
  validateBuild,
  validateExports
];

let allPassed = true;

for (const check of checks) {
  try {
    check();
  } catch (error) {
    console.error(`❌ ${error.message}`);
    allPassed = false;
  }
}

if (allPassed) {
  console.log('\n✅ All checks passed! Package is ready for publishing.');
  console.log('\nNext steps:');
  console.log('1. npm version [patch|minor|major]');
  console.log('2. npm publish');
  console.log('3. git push origin --tags');
} else {
  console.log('\n❌ Some checks failed. Please fix the issues before publishing.');
  process.exit(1);
}

/**
 * Validate package.json configuration
 */
function validatePackageJson() {
  console.log('📦 Validating package.json...');
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Required fields
  const requiredFields = ['name', 'version', 'description', 'main', 'types', 'exports'];
  for (const field of requiredFields) {
    if (!packageJson[field]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  
  // Validate exports
  if (!packageJson.exports['.']) {
    throw new Error('Missing main export in exports field');
  }
  
  // Validate keywords
  if (!packageJson.keywords || packageJson.keywords.length < 5) {
    throw new Error('Package should have at least 5 keywords');
  }
  
  // Validate files
  if (!packageJson.files || !packageJson.files.includes('dist')) {
    throw new Error('Package files should include dist directory');
  }
  
  console.log('✅ package.json validation passed');
}

/**
 * Validate TypeScript configuration
 */
function validateTypeScriptConfig() {
  console.log('🔧 Validating TypeScript configuration...');
  
  const configs = ['tsconfig.json', 'tsconfig.build.json', 'tsconfig.esm.json'];
  
  for (const config of configs) {
    if (!fs.existsSync(config)) {
      throw new Error(`Missing TypeScript config: ${config}`);
    }
  }
  
  // Validate build config
  const buildConfig = JSON.parse(fs.readFileSync('tsconfig.build.json', 'utf8'));
  if (!buildConfig.compilerOptions.declaration) {
    throw new Error('Build config must generate declarations');
  }
  
  console.log('✅ TypeScript configuration validation passed');
}

/**
 * Validate source files structure
 */
function validateSourceFiles() {
  console.log('📁 Validating source files...');
  
  const requiredFiles = [
    'src/index.ts',
    'src/core/index.ts',
    'src/tools/index.ts',
    'src/orchestration/index.ts'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      throw new Error(`Missing required source file: ${file}`);
    }
  }
  
  // Check for TypeScript files
  const srcFiles = getAllFiles('src', '.ts');
  if (srcFiles.length === 0) {
    throw new Error('No TypeScript source files found');
  }
  
  console.log('✅ Source files validation passed');
}

/**
 * Validate tests
 */
function validateTests() {
  console.log('🧪 Validating tests...');
  
  if (!fs.existsSync('tests') && !fs.existsSync('src/__tests__')) {
    throw new Error('No tests directory found');
  }
  
  // Check for test files
  const testFiles = [
    ...getAllFiles('tests', '.test.ts'),
    ...getAllFiles('src', '.test.ts'),
    ...getAllFiles('src', '.spec.ts')
  ];
  
  if (testFiles.length === 0) {
    throw new Error('No test files found');
  }
  
  console.log(`✅ Tests validation passed (${testFiles.length} test files found)`);
}

/**
 * Validate documentation
 */
function validateDocumentation() {
  console.log('📚 Validating documentation...');
  
  const requiredDocs = ['README.md', 'CHANGELOG.md', 'LICENSE'];
  
  for (const doc of requiredDocs) {
    if (!fs.existsSync(doc)) {
      throw new Error(`Missing required documentation: ${doc}`);
    }
  }
  
  // Validate README content
  const readme = fs.readFileSync('README.md', 'utf8');
  if (!readme.includes('npm install ag3ntic')) {
    throw new Error('README should include installation instructions');
  }
  
  console.log('✅ Documentation validation passed');
}

/**
 * Validate build output
 */
function validateBuild() {
  console.log('🔨 Validating build...');
  
  // Run build
  try {
    execSync('npm run build', { stdio: 'pipe' });
  } catch (error) {
    throw new Error('Build failed');
  }
  
  // Check build outputs
  const requiredBuildFiles = [
    'dist/index.js',
    'dist/index.d.ts',
    'dist/index.esm.js',
    'dist/core/index.js',
    'dist/core/index.d.ts',
    'dist/lib/agent-helpers.js',
    'dist/lib/agent-helpers.d.ts',
    'dist/lib/tool-helpers.js',
    'dist/lib/tool-helpers.d.ts'
  ];
  
  for (const file of requiredBuildFiles) {
    if (!fs.existsSync(file)) {
      throw new Error(`Missing build output: ${file}`);
    }
  }
  
  console.log('✅ Build validation passed');
}

/**
 * Validate package exports
 */
function validateExports() {
  console.log('📤 Validating exports...');
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const exports = packageJson.exports;
  
  // Check main export
  const mainExport = exports['.'];
  if (!mainExport.import || !mainExport.require || !mainExport.types) {
    throw new Error('Main export must have import, require, and types');
  }
  
  // Check submodule exports
  const submodules = ['core', 'lib'];
  for (const submodule of submodules) {
    const subExport = exports[`./${submodule}`];
    if (!subExport || !subExport.import || !subExport.require || !subExport.types) {
      throw new Error(`Submodule ${submodule} export must have import, require, and types`);
    }
  }
  
  console.log('✅ Exports validation passed');
}

/**
 * Get all files with specific extension recursively
 */
function getAllFiles(dir, ext) {
  if (!fs.existsSync(dir)) return [];
  
  const files = [];
  const items = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const item of items) {
    const fullPath = path.join(dir, item.name);
    if (item.isDirectory()) {
      files.push(...getAllFiles(fullPath, ext));
    } else if (item.name.endsWith(ext)) {
      files.push(fullPath);
    }
  }
  
  return files;
}
