/**
 * AG3NTIC Advanced Multi-Agent Orchestration System
 * 
 * This is the most sophisticated orchestration system that combines the best patterns from:
 * - CrewAI: Role-based agents with delegation and collaboration
 * - LangGraph: Graph-based workflows with conditional routing
 * - AutoGen: Multi-agent conversations with handoffs
 * - Custom AG3NTIC: Performance optimizations and unique features
 */

export * from './types.js';
export * from './orchestrator.js';
export * from './patterns/index.js';
export * from './agents/index.js';
export * from './routing/index.js';
export * from './coordination/index.js';
export * from './memory/index.js';
export * from './planning/index.js';

// Re-export commonly used orchestration components
export {
  AdvancedOrchestrator,
  SupervisorPattern,
  SwarmPattern,
  HierarchicalPattern,
  NetworkPattern,
  IntelligentRouter,
  DynamicCoordinator,
  ContextualMemory,
  StrategicPlanner
} from './orchestrator.js';
