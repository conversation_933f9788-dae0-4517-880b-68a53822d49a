# Universal Runnable Interface

The AG3NTIC framework now includes a powerful Universal Runnable Interface inspired by <PERSON><PERSON><PERSON><PERSON>'s Runnable system. This interface provides a unified way to compose, execute, and monitor all components in the framework.

## 🌟 Key Features

- **Universal Interface**: All components implement the same `Runnable` interface
- **Composition Operators**: Chain components using `|` operator or `pipe()` function
- **Streaming Support**: Built-in streaming capabilities for real-time processing
- **Callback System**: Comprehensive monitoring and event tracking
- **Memory Management**: Conversation history and state persistence
- **Configuration Propagation**: Automatic config inheritance through chains
- **Parallel Execution**: Run multiple components concurrently
- **Conditional Branching**: Dynamic routing based on input conditions

## 🚀 Quick Start

### Basic Usage

```typescript
import { RunnableUtils, pipe } from '@ag3ntic/core';

// Create simple runnables
const upperCase = RunnableUtils.lambda(
  (text: string) => text.toUpperCase(),
  'UpperCase'
);

const addBrackets = RunnableUtils.lambda(
  (text: string) => `[${text}]`,
  'AddBrackets'
);

// Compose them
const chain = pipe(upperCase, addBrackets);

// Execute
const result = await chain.invoke('hello world');
console.log(result); // "[HELLO WORLD]"
```

### With Callbacks

```typescript
import { CallbackUtils } from '@ag3ntic/core';

const config = CallbackUtils.createConfig({
  console: true,
  metrics: true
});

const result = await chain.invoke('hello world', config);
```

## 🔗 Composition Patterns

### Sequential Composition

```typescript
// Using pipe operator
const chain = runnable1.pipe(runnable2).pipe(runnable3);

// Using pipe function
const chain = pipe(runnable1, runnable2, runnable3);

// Using composition operator (if supported)
const chain = runnable1 | runnable2 | runnable3;
```

### Parallel Execution

```typescript
const parallel = RunnableUtils.parallel({
  analysis: textAnalyzer,
  summary: textSummarizer,
  length: RunnableUtils.lambda((text: string) => text.length)
});

const result = await parallel.invoke("Some text");
// Result: { analysis: {...}, summary: "...", length: 9 }
```

### Conditional Branching

```typescript
const branch = RunnableUtils.branch([
  {
    condition: (input: string) => input.length > 100,
    runnable: longTextHandler
  },
  {
    condition: (input: string) => input.length <= 100,
    runnable: shortTextHandler
  }
]);
```

### Assignment and Transformation

```typescript
const assign = RunnableUtils.assign({
  uppercase: (input: { text: string }) => input.text.toUpperCase(),
  wordCount: (input: { text: string }) => input.text.split(' ').length
});

// Input: { text: "hello world" }
// Output: { text: "hello world", uppercase: "HELLO WORLD", wordCount: 2 }
```

## 📊 Callback System

### Built-in Callbacks

```typescript
import { CallbackUtils } from '@ag3ntic/core';

// Console logging
const consoleCallback = CallbackUtils.console({ 
  verbose: true, 
  colors: true 
});

// Metrics collection
const metricsCallback = CallbackUtils.metrics();

// Event streaming
const eventCallback = CallbackUtils.eventStream();

// File logging
const fileCallback = CallbackUtils.file('./logs/execution.log');
```

### Custom Callbacks

```typescript
import { CallbackHandler, RunInfo } from '@ag3ntic/core';

const customCallback: CallbackHandler = {
  onStart: (run: RunInfo) => {
    console.log(`Starting ${run.name}...`);
  },
  onEnd: (run: RunInfo) => {
    console.log(`Completed ${run.name} in ${run.endTime! - run.startTime}ms`);
  },
  onError: (run: RunInfo, error: Error) => {
    console.error(`Error in ${run.name}:`, error.message);
  }
};
```

## 🧠 Memory Integration

### Basic Memory Usage

```typescript
import { MemoryUtils } from '@ag3ntic/memory';

// Create conversation memory
const memory = MemoryUtils.buffer({ maxMessages: 50 });

// Add messages
await memory.addMessage({
  role: 'user',
  content: 'Hello!'
});

await memory.addMessage({
  role: 'assistant', 
  content: 'Hi there! How can I help you?'
});

// Get conversation history
const history = await memory.getMessages();
```

### Memory Types

```typescript
// Buffer memory (keeps all messages)
const bufferMemory = MemoryUtils.buffer();

// Window memory (keeps last N messages)
const windowMemory = MemoryUtils.bufferWindow(10);

// Summary memory (summarizes old messages)
const summaryMemory = MemoryUtils.summary(20, customSummarizer);

// Read-only memory (prevents modifications)
const readOnlyMemory = MemoryUtils.readOnly(existingMemory);
```

## 📈 Graph Integration

The existing Graph class now implements the Runnable interface:

```typescript
import { Graph } from '@ag3ntic/core';

interface MyState extends AgentState {
  value: number;
}

const graph = new Graph<MyState>('MyGraph');

graph.addNode('process', async (state) => ({
  value: state.value * 2
}));

graph.addEdge('process', '__END__');

// Use as a runnable
const result = await graph.invoke({ 
  messages: [], 
  value: 5 
});

console.log(result.value); // 10
```

## 🌊 Streaming

### Basic Streaming

```typescript
// Stream individual chunks
for await (const chunk of runnable.stream(input)) {
  console.log('Chunk:', chunk);
}
```

### Custom Streaming Runnable

```typescript
class StreamingProcessor extends BaseRunnable<string, string> {
  async *stream(input: string): AsyncGenerator<string, void, unknown> {
    const words = input.split(' ');
    let result = '';
    
    for (const word of words) {
      result += word + ' ';
      yield result.trim();
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
}
```

## 🛠️ Tool Integration

Tools can now be used as runnables:

```typescript
import { ToolRunnableUtils } from '@ag3ntic/core';

// Wrap a tool as a runnable
const toolRunnable = ToolRunnableUtils.fromTool(myTool);

// Create a tool executor for agent state
const toolExecutor = ToolRunnableUtils.executor(myTool);

// Multi-tool executor
const multiExecutor = ToolRunnableUtils.multiExecutor([tool1, tool2, tool3]);

// Use in a chain
const chain = pipe(
  agentProcessor,
  multiExecutor,
  responseFormatter
);
```

## 🔧 Configuration

### Runnable Configuration

```typescript
interface RunnableConfig {
  callbacks?: CallbackHandler[];
  tags?: string[];
  metadata?: Record<string, any>;
  maxConcurrency?: number;
  timeout?: number;
  threadId?: string;
  checkpointId?: string;
}
```

### Configuration Inheritance

```typescript
const runnable = baseRunnable
  .withTags(['processing', 'text'])
  .withMetadata({ version: '1.0' })
  .withCallbacks([consoleCallback]);

// Configuration is inherited by all sub-runnables
const result = await runnable.invoke(input);
```

## 📚 Examples

See the complete examples in:
- `examples/runnable-composition-demo.ts` - Comprehensive demonstration
- `tests/runnable-interface.test.ts` - Test cases and usage patterns

## 🎯 Benefits

1. **Consistency**: All components use the same interface
2. **Composability**: Easy to chain and combine components
3. **Observability**: Built-in monitoring and debugging
4. **Flexibility**: Support for streaming, parallel execution, and branching
5. **Performance**: Optimized execution with minimal overhead
6. **Developer Experience**: Familiar patterns from LangChain ecosystem

## 🔄 Migration Guide

### From Old Graph API

```typescript
// Old way
const result = await graph.execute(initialState);

// New way (both work)
const result = await graph.execute(initialState); // Still works
const result = await graph.invoke(initialState);  // New runnable interface
```

### From Direct Tool Execution

```typescript
// Old way
const toolResult = await tool.execute(input);

// New way
const toolRunnable = ToolRunnableUtils.fromTool(tool);
const result = await toolRunnable.invoke({ input });
```

## 🚀 What's Next

The Universal Runnable Interface is the foundation for upcoming features:

- **Human-in-the-Loop**: Interrupt and resume execution
- **Advanced State Management**: Checkpointing and time travel
- **Distributed Execution**: Remote runnable execution
- **Enhanced Streaming**: Real-time event processing
- **Tool Ecosystem**: Pre-built runnable components

This interface makes AG3NTIC more powerful while maintaining the performance and type safety advantages that make it unique in the AI framework ecosystem.
