import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { z } from 'zod';
import {
  AG3NTICMCPServer,
  AG3NTICMCPClient,
  createMCPServer,
  createMCPClient,
  createAG3NTICToMCPAdapter,
  createMCPToAG3NTICAdapter,
  createMCPServerFromAG3NTICTools,
  MCP,
  globalMCPRegistry
} from '../mcp/index.js';
import { ToolFactory } from '../tools/index.js';
import type { MCPServerConfig, MCPToolDefinition } from '../mcp/types.js';

describe('MCP Library', () => {
  beforeEach(() => {
    globalMCPRegistry.shutdown();
  });

  afterEach(async () => {
    await globalMCPRegistry.shutdown();
  });

  describe('MCP Server', () => {
    it('should create MCP server with stdio transport', () => {
      const server = createMCPServer('test-server', '1.0.0', 'stdio');
      
      expect(server).toBeInstanceOf(AG3NTICMCPServer);
      expect(server.config.name).toBe('test-server');
      expect(server.config.version).toBe('1.0.0');
      expect(server.config.transport.type).toBe('stdio');
    });

    it('should register tools', () => {
      const server = createMCPServer('test-server');
      
      const toolDef: MCPToolDefinition = {
        name: 'test_tool',
        description: 'A test tool',
        inputSchema: {
          type: 'object',
          properties: {
            input: { type: 'string' }
          }
        },
        handler: async (args) => ({
          content: [{ type: 'text', text: `Received: ${args.input}` }]
        })
      };

      expect(() => server.registerTool(toolDef)).not.toThrow();
    });

    it('should track running state', () => {
      const server = createMCPServer('test-server');
      
      expect(server.isRunning()).toBe(false);
      // Note: We can't actually start the server in tests without a real transport
    });
  });

  describe('MCP Client', () => {
    it('should create MCP client with stdio transport', () => {
      const client = createMCPClient('test-client', 'node', ['--version']);
      
      expect(client).toBeInstanceOf(AG3NTICMCPClient);
      expect(client.config.name).toBe('test-client');
      expect(client.config.transport.type).toBe('stdio');
    });

    it('should track connection state', () => {
      const client = createMCPClient('test-client', 'node', ['--version']);
      
      expect(client.isConnected()).toBe(false);
      // Note: We can't actually connect in tests without a real server
    });
  });

  describe('AG3NTIC to MCP Adapter', () => {
    it('should convert AG3NTIC tool to MCP tool', () => {
      const adapter = createAG3NTICToMCPAdapter();
      
      const ag3nticTool = ToolFactory.create(
        {
          name: 'test_tool',
          title: 'Test Tool',
          description: 'A test tool for conversion'
        },
        z.object({ input: z.string() }),
        undefined,
        async (input: any) => ({
          success: true,
          data: { output: input.input.toUpperCase() },
          content: [{ type: 'text' as const, text: input.input.toUpperCase() }]
        })
      );

      const mcpTool = adapter.convertTool(ag3nticTool);
      
      expect(mcpTool.name).toBe('test_tool');
      expect(mcpTool.description).toBe('A test tool for conversion');
      expect(typeof mcpTool.handler).toBe('function');
    });

    it('should create MCP server from AG3NTIC tools', async () => {
      const tools = [
        ToolFactory.create(
          { name: 'tool1', title: 'Tool 1', description: 'First tool' },
          z.object({ value: z.string() }),
          undefined,
          async (input: any) => ({ success: true, data: input })
        ),
        ToolFactory.create(
          { name: 'tool2', title: 'Tool 2', description: 'Second tool' },
          z.object({ value: z.number() }),
          undefined,
          async (input: any) => ({ success: true, data: input })
        )
      ];

      const server = await createMCPServerFromAG3NTICTools(
        tools,
        'ag3ntic-server',
        '1.0.0',
        'stdio'
      );

      expect(server).toBeInstanceOf(AG3NTICMCPServer);
      expect(server.config.name).toBe('ag3ntic-server');
    });
  });

  describe('MCP to AG3NTIC Adapter', () => {
    it('should create adapter', () => {
      const adapter = createMCPToAG3NTICAdapter();
      expect(adapter).toBeDefined();
      expect(typeof adapter.convertToolToNode).toBe('function');
      expect(typeof adapter.convertResourceToNode).toBe('function');
    });

    it('should convert MCP tool to graph node', () => {
      const adapter = createMCPToAG3NTICAdapter();
      
      // Create a mock client
      const mockClient = {
        isConnected: () => true,
        callTool: async (name: string, _args: any) => ({
          content: [{ type: 'text' as const, text: `Mock result for ${name}` }],
          isError: false
        })
      } as any;

      const node = adapter.convertToolToNode(mockClient, 'test_tool');
      expect(typeof node).toBe('function');
    });
  });

  describe('MCP Registry', () => {
    it('should register and retrieve servers', () => {
      const server = createMCPServer('registry-test-server');
      
      MCP.registerServer('test-server', server);
      
      const retrieved = MCP.getServer('test-server');
      expect(retrieved).toBe(server);
    });

    it('should register and retrieve clients', () => {
      const client = createMCPClient('registry-test-client', 'node', ['--version']);
      
      MCP.registerClient('test-client', client);
      
      const retrieved = MCP.getClient('test-client');
      expect(retrieved).toBe(client);
    });

    it('should provide registry statistics', () => {
      const server = createMCPServer('stats-server');
      const client = createMCPClient('stats-client', 'node', ['--version']);
      
      MCP.registerServer('server1', server);
      MCP.registerClient('client1', client);
      
      const stats = MCP.getStats();
      expect(stats.servers.total).toBe(1);
      expect(stats.clients.total).toBe(1);
    });

    it('should clear registry on shutdown', async () => {
      const server = createMCPServer('shutdown-server');
      const client = createMCPClient('shutdown-client', 'node', ['--version']);
      
      MCP.registerServer('server1', server);
      MCP.registerClient('client1', client);
      
      await MCP.shutdown();
      
      const stats = MCP.getStats();
      expect(stats.servers.total).toBe(0);
      expect(stats.clients.total).toBe(0);
    });
  });

  describe('MCP Integration Utilities', () => {
    it('should setup MCP integration with server only', async () => {
      const tools = [
        ToolFactory.create(
          { name: 'setup_tool', title: 'Setup Tool', description: 'Tool for setup testing' },
          z.object({ input: z.string() }),
          undefined,
          async (input: any) => ({ success: true, data: input })
        )
      ];

      const { setupMCPIntegration } = await import('../mcp/index.js');
      
      const result = await setupMCPIntegration({
        server: {
          name: 'setup-test-server',
          tools,
          autoStart: false // Don't actually start in tests
        }
      });

      expect(result.server).toBeDefined();
      expect(result.clients).toEqual({});
    });

    it('should handle MCP event listeners', () => {
      const server = createMCPServer('event-server');
      
      // Event tracking for test
      server.addEventListener('server_started', () => {
        _eventReceived = true;
      });

      // Simulate event emission (in real usage, this would happen automatically)
      (server as any).emitEvent('server_started', {});
      
      // Note: In a real test, we'd need to actually start the server to test events
      expect(typeof server.addEventListener).toBe('function');
    });
  });

  describe('MCP Transport Configuration', () => {
    it('should configure stdio transport', () => {
      const config: MCPServerConfig = {
        name: 'stdio-server',
        version: '1.0.0',
        transport: {
          type: 'stdio'
        }
      };

      const server = new AG3NTICMCPServer(config);
      expect(server.config.transport.type).toBe('stdio');
    });

    it('should configure streamable HTTP transport', () => {
      const config: MCPServerConfig = {
        name: 'http-server',
        version: '1.0.0',
        transport: {
          type: 'streamable-http',
          options: {
            sessionIdGenerator: () => 'test-session',
            enableDnsRebindingProtection: true
          }
        }
      };

      const server = new AG3NTICMCPServer(config);
      expect(server.config.transport.type).toBe('streamable-http');
      expect(server.config.transport.options?.enableDnsRebindingProtection).toBe(true);
    });

    it('should throw error for unsupported transport', () => {
      const config: MCPServerConfig = {
        name: 'invalid-server',
        version: '1.0.0',
        transport: {
          type: 'invalid' as any
        }
      };

      expect(() => new AG3NTICMCPServer(config)).toThrow('Unsupported transport type');
    });
  });
});
