# AG3NTIC Framework

> **Powerful Simplicity for Agentic Workflows**

AG3NTIC is a lightweight, TypeScript-native framework for building agentic workflows with radical simplicity. It delivers the power of complex agentic orchestration through an API that is ruthlessly simple, transparent, and built for the modern developer.

## 🎯 Why AG3NTIC?

The current AI agent framework landscape forces developers into a painful trade-off:

- **Powerful but Painful** (<PERSON><PERSON><PERSON><PERSON>, AutoGen): Deep capabilities but immense cognitive overhead
- **Simple but Shackling** (CrewAI): Easy to start but quickly becomes restrictive

AG3NTIC bridges this gap with **Powerful Simplicity** - offering the conceptual power of stateful graph models with a radically simplified, developer-first API.

## ✨ Core Principles

1. **Clarity Over Cleverness** - Explicit, predictable behavior
2. **Minimize Boilerplate** - 80% functionality with 20% code
3. **Strongly-Typed** - Full TypeScript support with self-documenting APIs
4. **Unopinionated Core** - Flexible primitives for any workflow
5. **No Hidden State** - Complete transparency and debuggability
6. **Stateless Nodes** - Pure functions that are easy to test
7. **Fail Fast and Loud** - Clear, actionable error messages

## 🚀 Quick Start

### Installation

```bash
npm install ag3ntic
# or
yarn add ag3ntic
```

### Your First Agent in 5 Minutes

```typescript
import { Graph, Executor, createToolNode, shouldCallTools } from 'ag3ntic';

// 1. Define your state
interface WeatherState extends AgentState {
  messages: MCPMessage[];
}

// 2. Define your tools
const tools = {
  getCurrentWeather: async ({ location }: { location: string }) => {
    // Your weather API call here
    return `The weather in ${location} is sunny and 72°F`;
  }
};

// 3. Create your graph
const graph = new Graph<WeatherState>()
  .addNode('agent', agentNode)           // Your LLM node
  .addNode('tools', createToolNode(tools)) // Auto-generated tool executor
  .setEntryPoint('agent')
  .addConditionalEdge('agent', shouldCallTools, {
    'tools': 'tools',
    '__end__': '__END__'
  })
  .addEdge('tools', 'agent');

// 4. Execute
const executor = new Executor(graph);
const result = await executor.execute({
  messages: [{ role: 'user', content: 'What\'s the weather in Tokyo?' }]
});
```

## 🏗️ Architecture

AG3NTIC is built around three core concepts:

### 1. **State** - Your Data, Your Control
```typescript
interface MyState extends AgentState {
  messages: MCPMessage[];
  context: Record<string, any>;
  // Add any properties you need
}
```

The state is a plain TypeScript interface that you define. No hidden properties, no magic - what you define is what you get.

### 2. **Nodes** - Pure, Testable Functions
```typescript
const myNode = async (state: MyState): Promise<Partial<MyState>> => {
  // Do work based on current state
  return { 
    messages: [...state.messages, newMessage]
  };
};
```

Nodes are stateless functions that receive the current state and return updates. They're trivial to unit test.

### 3. **Graph** - Explicit Control Flow
```typescript
const graph = new Graph<MyState>()
  .addNode('step1', node1)
  .addNode('step2', node2)
  .addEdge('step1', 'step2')
  .addConditionalEdge('step2', routingLogic, {
    'continue': 'step1',
    'finish': '__END__'
  });
```

The graph defines how your workflow moves between nodes. The control flow is explicit and debuggable.

## 🛠️ Key Features

### Powerful Helpers
- `createAgentNode()` - LLM integration with any client
- `createToolNode()` - Automatic tool dispatch and execution
- `shouldCallTools()` - Common routing logic
- State utilities: `getLastMessage()`, `addMessage()`, etc.

### Full Observability
```typescript
// Stream execution step-by-step
for await (const state of graph.stream(initialState)) {
  console.log('Current state:', state);
  // Full visibility into every step
}

// Or execute with callbacks
await executor.executeWithCallback(initialState, (state, step) => {
  console.log(`Step ${step}:`, getLastMessage(state));
});
```

### TypeScript-First
- Generic over your state type: `Graph<MyState>`
- Full IDE autocompletion and compile-time checking
- Self-documenting APIs

## 📚 Examples

### Weather Agent
```bash
npm run example:weather
```

A simple agent that can answer weather questions using tools.

### Advanced Multi-Tool Agent
```bash
npm run example:advanced
```

An agent with multiple tools (weather, search, calculator) showcasing conditional routing.

## 🧪 Testing

AG3NTIC nodes are pure functions, making them trivial to test:

```typescript
import { myNode } from './nodes';

test('myNode should process user input', async () => {
  const initialState = {
    messages: [{ role: 'user', content: 'Hello' }]
  };
  
  const result = await myNode(initialState);
  
  expect(result.messages).toHaveLength(2);
  expect(result.messages[1].role).toBe('assistant');
});
```

## 🔧 Development

```bash
# Install dependencies
npm install

# Run examples
npm run example:weather
npm run example:advanced

# Build
npm run build

# Test
npm run test
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🌟 Why Choose AG3NTIC?

- **Developer Experience First** - Built by developers, for developers
- **TypeScript Native** - Not a Python port, designed for TS from day one
- **Transparent & Debuggable** - No magic, no hidden state
- **Production Ready** - Simple enough to understand, powerful enough to scale
- **Framework Agnostic** - Bring your own LLM client, database, etc.

---

**AG3NTIC**: Where powerful agentic workflows meet developer happiness. 🚀
