import {
  Tool,
  ToolRegistry,
  ToolMetadata,
  ToolContext,
  ToolExecutionResult,
  ToolExecutionOptions
} from './types.js';

/**
 * Default tool registry implementation
 */
export class DefaultToolRegistry implements ToolRegistry {
  private tools = new Map<string, Tool>();
  private metadata = new Map<string, ToolMetadata>();

  /**
   * Register a tool in the registry
   */
  register(tool: Tool): void {
    const metadata = tool.getMetadata();
    
    if (this.tools.has(metadata.name)) {
      console.warn(`Tool '${metadata.name}' is already registered. Overwriting.`);
    }

    this.tools.set(metadata.name, tool);
    this.metadata.set(metadata.name, metadata);
  }

  /**
   * Unregister a tool from the registry
   */
  unregister(name: string): void {
    this.tools.delete(name);
    this.metadata.delete(name);
  }

  /**
   * Get a tool by name
   */
  get(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  /**
   * List all registered tools
   */
  list(): ToolMetadata[] {
    return Array.from(this.metadata.values());
  }

  /**
   * Search tools by criteria
   */
  search(query: { 
    category?: string; 
    tags?: string[]; 
    enabled?: boolean;
    name?: string;
  }): ToolMetadata[] {
    const allTools = this.list();
    
    return allTools.filter(tool => {
      // Filter by enabled status
      if (query.enabled !== undefined && tool.enabled !== query.enabled) {
        return false;
      }

      // Filter by category
      if (query.category && tool.category !== query.category) {
        return false;
      }

      // Filter by name (partial match)
      if (query.name && !tool.name.toLowerCase().includes(query.name.toLowerCase())) {
        return false;
      }

      // Filter by tags
      if (query.tags && query.tags.length > 0) {
        if (!tool.tags || tool.tags.length === 0) {
          return false;
        }
        
        // Check if any of the query tags match tool tags
        const hasMatchingTag = query.tags.some(queryTag =>
          tool.tags!.some(toolTag => 
            toolTag.toLowerCase().includes(queryTag.toLowerCase())
          )
        );
        
        if (!hasMatchingTag) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Execute a tool by name
   */
  async execute(
    name: string, 
    input: any, 
    context?: ToolContext,
    options?: ToolExecutionOptions
  ): Promise<ToolExecutionResult> {
    const tool = this.get(name);
    
    if (!tool) {
      return {
        success: false,
        error: `Tool '${name}' not found in registry`
      };
    }

    // Use executeWithValidation if available (from BaseTool)
    if ('executeWithValidation' in tool && typeof tool.executeWithValidation === 'function') {
      return (tool as any).executeWithValidation(input, context, options);
    }

    // Fallback to basic execution
    return tool.execute(input, context);
  }

  /**
   * Get tools by category
   */
  getByCategory(category: string): ToolMetadata[] {
    return this.search({ category });
  }

  /**
   * Get enabled tools only
   */
  getEnabled(): ToolMetadata[] {
    return this.search({ enabled: true });
  }

  /**
   * Get disabled tools only
   */
  getDisabled(): ToolMetadata[] {
    return this.search({ enabled: false });
  }

  /**
   * Enable a tool
   */
  enable(name: string): boolean {
    const tool = this.get(name);
    if (!tool) {
      return false;
    }

    tool.config.enabled = true;
    // Update metadata
    const metadata = tool.getMetadata();
    this.metadata.set(name, metadata);
    return true;
  }

  /**
   * Disable a tool
   */
  disable(name: string): boolean {
    const tool = this.get(name);
    if (!tool) {
      return false;
    }

    tool.config.enabled = false;
    // Update metadata
    const metadata = tool.getMetadata();
    this.metadata.set(name, metadata);
    return true;
  }

  /**
   * Check if a tool exists
   */
  has(name: string): boolean {
    return this.tools.has(name);
  }

  /**
   * Get the number of registered tools
   */
  size(): number {
    return this.tools.size;
  }

  /**
   * Clear all tools from the registry
   */
  clear(): void {
    this.tools.clear();
    this.metadata.clear();
  }

  /**
   * Get tool names
   */
  getNames(): string[] {
    return Array.from(this.tools.keys());
  }

  /**
   * Get tools that match a search term in name or description
   */
  searchText(searchTerm: string): ToolMetadata[] {
    const term = searchTerm.toLowerCase();
    return this.list().filter(tool => 
      tool.name.toLowerCase().includes(term) ||
      tool.title.toLowerCase().includes(term) ||
      tool.description.toLowerCase().includes(term) ||
      (tool.tags && tool.tags.some(tag => tag.toLowerCase().includes(term)))
    );
  }

  /**
   * Get registry statistics
   */
  getStats(): {
    total: number;
    enabled: number;
    disabled: number;
    categories: Record<string, number>;
  } {
    const allTools = this.list();
    const stats = {
      total: allTools.length,
      enabled: 0,
      disabled: 0,
      categories: {} as Record<string, number>
    };

    allTools.forEach(tool => {
      if (tool.enabled) {
        stats.enabled++;
      } else {
        stats.disabled++;
      }

      if (tool.category) {
        stats.categories[tool.category] = (stats.categories[tool.category] || 0) + 1;
      }
    });

    return stats;
  }
}

/**
 * Global tool registry instance
 */
export const globalToolRegistry = new DefaultToolRegistry();

/**
 * Convenience functions for working with the global registry
 */
export const registerTool = (tool: Tool) => globalToolRegistry.register(tool);
export const unregisterTool = (name: string) => globalToolRegistry.unregister(name);
export const getTool = (name: string) => globalToolRegistry.get(name);
export const listTools = () => globalToolRegistry.list();
export const searchTools = (query: Parameters<ToolRegistry['search']>[0]) => globalToolRegistry.search(query);
export const executeTool = (
  name: string, 
  input: any, 
  context?: ToolContext,
  options?: ToolExecutionOptions
) => globalToolRegistry.execute(name, input, context, options);
