/**
 * AG3NTIC Runnable Composition Demo
 * 
 * This example demonstrates the new LangChain-inspired Universal Runnable Interface
 * and composition capabilities in AG3NTIC.
 */

import {
  BaseRunnable,
  RunnableConfig,
  CallbackHandler,
  RunInfo
} from '../src/core/runnable.js';

import {
  RunnableLambda,
  RunnableParallel,
  RunnableBranch,
  RunnableUtils,
  pipe
} from '../src/core/composition.js';

import {
  <PERSON><PERSON>e<PERSON>allback<PERSON>andler,
  MetricsCallbackHandler,
  CallbackUtils
} from '../src/core/callbacks.js';

import { Graph } from '../src/core/graph.js';
import { AgentState, MCPMessage } from '../src/core/types.js';
import { ConversationBufferMemory, MemoryUtils } from '../src/memory/index.js';

// Example: Text Processing Pipeline
class TextAnalyzer extends BaseRunnable<string, { text: string; wordCount: number; sentiment: string }> {
  constructor() {
    super('TextAnalyzer');
  }

  async invoke(input: string, config?: RunnableConfig) {
    return this.executeWithCallbacks(async () => {
      // Simulate text analysis
      const wordCount = input.split(' ').length;
      const sentiment = input.includes('good') || input.includes('great') ? 'positive' : 
                       input.includes('bad') || input.includes('terrible') ? 'negative' : 'neutral';
      
      return {
        text: input,
        wordCount,
        sentiment
      };
    }, input, config);
  }
}

class TextSummarizer extends BaseRunnable<string, string> {
  constructor() {
    super('TextSummarizer');
  }

  async invoke(input: string, config?: RunnableConfig): Promise<string> {
    return this.executeWithCallbacks(async () => {
      // Simulate summarization
      const words = input.split(' ');
      if (words.length <= 10) {
        return input;
      }
      return words.slice(0, 10).join(' ') + '...';
    }, input, config);
  }
}

// Example: Agent State Processing
interface ChatState extends AgentState {
  userInput?: string;
  analysis?: any;
  response?: string;
  conversationId?: string;
}

class ChatProcessor extends BaseRunnable<ChatState, ChatState> {
  constructor(private memory: ConversationBufferMemory) {
    super('ChatProcessor');
  }

  async invoke(input: ChatState, config?: RunnableConfig): Promise<ChatState> {
    return this.executeWithCallbacks(async () => {
      // Load conversation history
      const memoryVars = await this.memory.getMemoryVariables();
      
      // Process the input
      const response = `I understand you said: "${input.userInput}". How can I help you further?`;
      
      // Update memory
      if (input.userInput) {
        await this.memory.addMessage({
          role: 'user',
          content: input.userInput
        });
        
        await this.memory.addMessage({
          role: 'assistant',
          content: response
        });
      }
      
      return {
        ...input,
        response,
        messages: [...input.messages, {
          role: 'assistant',
          content: response
        }]
      };
    }, input, config);
  }
}

async function demonstrateBasicComposition() {
  console.log('\n🔗 Basic Runnable Composition Demo');
  console.log('=====================================');

  // Create a text processing pipeline
  const analyzer = new TextAnalyzer();
  const summarizer = new TextSummarizer();
  
  // Create a lambda for formatting
  const formatter = RunnableUtils.lambda(
    (analysis: any) => `Analysis: ${analysis.wordCount} words, ${analysis.sentiment} sentiment`,
    'Formatter'
  );

  // Compose the pipeline
  const pipeline = pipe(
    analyzer,
    RunnableUtils.lambda((analysis) => analysis.text, 'ExtractText'),
    summarizer,
    formatter
  );

  // Add callbacks for monitoring
  const config = CallbackUtils.createConfig({
    console: { verbose: true, colors: true },
    metrics: true
  });

  const result = await pipeline.invoke(
    "This is a great example of how the new runnable system works in AG3NTIC!",
    config
  );

  console.log('📊 Result:', result);
}

async function demonstrateParallelExecution() {
  console.log('\n⚡ Parallel Execution Demo');
  console.log('===========================');

  const analyzer = new TextAnalyzer();
  const summarizer = new TextSummarizer();
  
  // Create parallel processing
  const parallel = RunnableUtils.parallel({
    analysis: analyzer,
    summary: summarizer,
    length: RunnableUtils.lambda((text: string) => text.length, 'LengthCalculator'),
    uppercase: RunnableUtils.lambda((text: string) => text.toUpperCase(), 'Uppercaser')
  });

  const config = CallbackUtils.createConfig({ console: true });
  
  const result = await parallel.invoke(
    "The AG3NTIC framework now supports powerful composition patterns inspired by LangChain.",
    config
  );

  console.log('📊 Parallel Results:', JSON.stringify(result, null, 2));
}

async function demonstrateConditionalBranching() {
  console.log('\n🔀 Conditional Branching Demo');
  console.log('==============================');

  const shortHandler = RunnableUtils.lambda(
    (text: string) => `Short text: ${text}`,
    'ShortHandler'
  );
  
  const longHandler = pipe(
    new TextSummarizer(),
    RunnableUtils.lambda((summary: string) => `Long text summary: ${summary}`, 'LongFormatter')
  );

  const branch = RunnableUtils.branch([
    {
      condition: (text: string) => text.length > 50,
      runnable: longHandler
    }
  ], shortHandler, 'TextLengthBranch');

  const config = CallbackUtils.createConfig({ console: true });

  const shortResult = await branch.invoke("Hello!", config);
  const longResult = await branch.invoke(
    "This is a much longer text that should trigger the long text handler and get summarized appropriately.",
    config
  );

  console.log('📊 Short text result:', shortResult);
  console.log('📊 Long text result:', longResult);
}

async function demonstrateMemoryIntegration() {
  console.log('\n🧠 Memory Integration Demo');
  console.log('===========================');

  const memory = MemoryUtils.buffer({ maxMessages: 10 });
  const chatProcessor = new ChatProcessor(memory);

  // Create a conversation chain
  const conversationChain = pipe(
    RunnableUtils.lambda((input: string) => ({
      messages: [],
      userInput: input
    } as ChatState), 'InputFormatter'),
    chatProcessor,
    RunnableUtils.lambda((state: ChatState) => state.response!, 'ResponseExtractor')
  );

  const config = CallbackUtils.createConfig({ console: true });

  console.log('💬 Starting conversation...');
  
  const response1 = await conversationChain.invoke("Hello, how are you?", config);
  console.log('🤖 Response 1:', response1);

  const response2 = await conversationChain.invoke("What's the weather like?", config);
  console.log('🤖 Response 2:', response2);

  const response3 = await conversationChain.invoke("Tell me a joke", config);
  console.log('🤖 Response 3:', response3);

  // Show memory contents
  const memoryVars = await memory.getMemoryVariables();
  console.log('🧠 Memory contents:', memoryVars.history.length, 'messages');
}

async function demonstrateGraphIntegration() {
  console.log('\n📊 Graph Integration Demo');
  console.log('==========================');

  interface ProcessingState extends AgentState {
    input?: string;
    processed?: string;
    analyzed?: any;
    final?: string;
  }

  const graph = new Graph<ProcessingState>('ProcessingGraph');

  // Add nodes
  graph.addNode('preprocess', async (state) => ({
    processed: state.input?.toLowerCase().trim()
  }));

  graph.addNode('analyze', async (state) => {
    const analyzer = new TextAnalyzer();
    const analysis = await analyzer.invoke(state.processed || '');
    return { analyzed: analysis };
  });

  graph.addNode('format', async (state) => ({
    final: `Processed "${state.processed}" -> ${state.analyzed?.wordCount} words, ${state.analyzed?.sentiment} sentiment`
  }));

  // Add edges
  graph.addEdge('preprocess', 'analyze');
  graph.addEdge('analyze', 'format');
  graph.addEdge('format', '__END__');

  const config = CallbackUtils.createConfig({ console: true });

  const result = await graph.invoke({
    messages: [],
    input: "  THIS IS A GREAT EXAMPLE!  "
  }, config);

  console.log('📊 Graph result:', result.final);
}

async function demonstrateStreamingCapabilities() {
  console.log('\n🌊 Streaming Capabilities Demo');
  console.log('===============================');

  class StreamingProcessor extends BaseRunnable<string, string> {
    constructor() {
      super('StreamingProcessor');
    }

    async invoke(input: string, config?: RunnableConfig): Promise<string> {
      return this.executeWithCallbacks(async () => {
        return `Processed: ${input}`;
      }, input, config);
    }

    async *stream(input: string, config?: RunnableConfig): AsyncGenerator<string, void, unknown> {
      const words = input.split(' ');
      let result = 'Processed:';
      
      for (const word of words) {
        await new Promise(resolve => setTimeout(resolve, 100)); // Simulate processing delay
        result += ` ${word}`;
        yield result;
      }
    }
  }

  const processor = new StreamingProcessor();
  const config = CallbackUtils.createConfig({ console: true });

  console.log('🌊 Streaming processing...');
  for await (const chunk of processor.stream("Hello world from AG3NTIC", config)) {
    console.log('📡 Chunk:', chunk);
  }
}

async function main() {
  console.log('🚀 AG3NTIC Universal Runnable Interface Demo');
  console.log('==============================================');

  try {
    await demonstrateBasicComposition();
    await demonstrateParallelExecution();
    await demonstrateConditionalBranching();
    await demonstrateMemoryIntegration();
    await demonstrateGraphIntegration();
    await demonstrateStreamingCapabilities();

    console.log('\n✅ All demos completed successfully!');
    console.log('\n🎉 The Universal Runnable Interface brings powerful composition');
    console.log('   capabilities to AG3NTIC, making it easier to build complex');
    console.log('   AI workflows with monitoring, streaming, and memory management.');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

// Run the demo if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export {
  demonstrateBasicComposition,
  demonstrateParallelExecution,
  demonstrateConditionalBranching,
  demonstrateMemoryIntegration,
  demonstrateGraphIntegration,
  demonstrateStreamingCapabilities
};
