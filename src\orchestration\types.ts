/**
 * Advanced Multi-Agent Orchestration Types
 * 
 * Comprehensive type system for sophisticated multi-agent coordination,
 * routing, and collaboration patterns.
 */

import { z } from 'zod';
import { Runnable } from '../core/runnable.js';
import { GraphState } from '../core/graph.js';

/**
 * Agent specialization and capability types
 */
export enum AgentRole {
  SUPERVISOR = 'supervisor',
  COORDINATOR = 'coordinator', 
  SPECIALIST = 'specialist',
  RESEARCHER = 'researcher',
  ANALYST = 'analyst',
  PLANNER = 'planner',
  EXECUTOR = 'executor',
  REVIEWER = 'reviewer',
  SYNTHESIZER = 'synthesizer',
  ROUTER = 'router'
}

export enum AgentCapability {
  DELEGATION = 'delegation',
  COORDINATION = 'coordination',
  ANALYSIS = 'analysis',
  RESEARCH = 'research',
  PLANNING = 'planning',
  EXECUTION = 'execution',
  REVIEW = 'review',
  SYNTHESIS = 'synthesis',
  ROUTING = 'routing',
  MEMORY_MANAGEMENT = 'memory_management',
  TOOL_USAGE = 'tool_usage',
  HUMAN_INTERACTION = 'human_interaction'
}

/**
 * Orchestration patterns
 */
export enum OrchestrationPattern {
  SUPERVISOR = 'supervisor',        // Central coordinator delegates to specialists
  SWARM = 'swarm',                 // Dynamic handoffs based on specialization
  HIERARCHICAL = 'hierarchical',   // Multi-level teams with sub-supervisors
  NETWORK = 'network',             // Peer-to-peer agent communication
  PIPELINE = 'pipeline',           // Sequential processing chain
  HYBRID = 'hybrid'                // Combination of multiple patterns
}

/**
 * Agent state and context
 */
export interface AgentContext {
  id: string;
  role: AgentRole;
  capabilities: AgentCapability[];
  specializations: string[];
  currentTask?: string;
  workload: number;
  performance: AgentPerformance;
  memory: AgentMemory;
  tools: string[];
  collaborators: string[];
  preferences: Record<string, any>;
}

export interface AgentPerformance {
  successRate: number;
  averageResponseTime: number;
  taskComplexityHandled: number;
  collaborationScore: number;
  reliabilityScore: number;
  lastUpdated: string;
}

export interface AgentMemory {
  conversationHistory: ConversationEntry[];
  taskHistory: TaskEntry[];
  learnings: Learning[];
  preferences: Record<string, any>;
  relationships: AgentRelationship[];
}

export interface ConversationEntry {
  timestamp: string;
  participants: string[];
  messages: Message[];
  outcome: string;
  context: Record<string, any>;
}

export interface TaskEntry {
  id: string;
  description: string;
  assignedBy: string;
  startTime: string;
  endTime?: string;
  status: TaskStatus;
  result?: any;
  feedback?: string;
  complexity: number;
}

export interface Learning {
  id: string;
  type: 'success' | 'failure' | 'optimization' | 'pattern';
  description: string;
  context: Record<string, any>;
  confidence: number;
  timestamp: string;
  applicability: string[];
}

export interface AgentRelationship {
  agentId: string;
  relationshipType: 'supervisor' | 'peer' | 'subordinate' | 'specialist';
  trustLevel: number;
  collaborationHistory: number;
  preferredCommunicationStyle: string;
}

/**
 * Task and workflow types
 */
export enum TaskStatus {
  PENDING = 'pending',
  ASSIGNED = 'assigned',
  IN_PROGRESS = 'in_progress',
  BLOCKED = 'blocked',
  REVIEW = 'review',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum TaskPriority {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  URGENT = 4,
  CRITICAL = 5
}

export interface Task {
  id: string;
  description: string;
  requirements: TaskRequirement[];
  priority: TaskPriority;
  complexity: number;
  estimatedDuration: number;
  dependencies: string[];
  assignedTo?: string;
  status: TaskStatus;
  progress: number;
  result?: any;
  feedback?: TaskFeedback;
  metadata: Record<string, any>;
  created: string;
  updated: string;
}

export interface TaskRequirement {
  type: 'capability' | 'tool' | 'knowledge' | 'resource';
  value: string;
  importance: 'required' | 'preferred' | 'optional';
  alternatives?: string[];
}

export interface TaskFeedback {
  quality: number;
  timeliness: number;
  completeness: number;
  comments: string;
  reviewer: string;
  timestamp: string;
}

/**
 * Routing and coordination types
 */
export interface RoutingDecision {
  targetAgent: string;
  confidence: number;
  reasoning: string;
  alternatives: Array<{
    agent: string;
    confidence: number;
    reasoning: string;
  }>;
  context: Record<string, any>;
  timestamp: string;
}

export interface CoordinationStrategy {
  pattern: OrchestrationPattern;
  participants: string[];
  roles: Record<string, AgentRole>;
  communicationProtocol: CommunicationProtocol;
  conflictResolution: ConflictResolutionStrategy;
  performanceMetrics: string[];
}

export interface CommunicationProtocol {
  messageFormat: 'structured' | 'natural' | 'hybrid';
  acknowledgmentRequired: boolean;
  timeoutDuration: number;
  retryPolicy: RetryPolicy;
  escalationRules: EscalationRule[];
}

export interface RetryPolicy {
  maxAttempts: number;
  backoffStrategy: 'linear' | 'exponential' | 'fixed';
  baseDelay: number;
  maxDelay: number;
}

export interface EscalationRule {
  condition: string;
  action: 'retry' | 'reassign' | 'escalate' | 'abort';
  target?: string;
  delay: number;
}

export enum ConflictResolutionStrategy {
  SUPERVISOR_DECISION = 'supervisor_decision',
  VOTING = 'voting',
  EXPERTISE_WEIGHTED = 'expertise_weighted',
  PERFORMANCE_BASED = 'performance_based',
  CONSENSUS = 'consensus',
  ESCALATION = 'escalation'
}

/**
 * Message and communication types
 */
export interface Message {
  id: string;
  from: string;
  to: string | string[];
  type: MessageType;
  content: any;
  priority: TaskPriority;
  timestamp: string;
  context: Record<string, any>;
  requiresResponse: boolean;
  responseDeadline?: string;
  metadata: Record<string, any>;
}

export enum MessageType {
  TASK_ASSIGNMENT = 'task_assignment',
  TASK_COMPLETION = 'task_completion',
  TASK_UPDATE = 'task_update',
  DELEGATION_REQUEST = 'delegation_request',
  COLLABORATION_REQUEST = 'collaboration_request',
  INFORMATION_REQUEST = 'information_request',
  INFORMATION_RESPONSE = 'information_response',
  STATUS_UPDATE = 'status_update',
  ERROR_REPORT = 'error_report',
  FEEDBACK = 'feedback',
  COORDINATION = 'coordination',
  HANDOFF = 'handoff'
}

/**
 * Planning and strategy types
 */
export interface ExecutionPlan {
  id: string;
  objective: string;
  strategy: PlanningStrategy;
  phases: ExecutionPhase[];
  resources: ResourceAllocation[];
  timeline: Timeline;
  riskAssessment: RiskAssessment;
  successCriteria: SuccessCriteria[];
  contingencyPlans: ContingencyPlan[];
  created: string;
  createdBy: string;
}

export interface PlanningStrategy {
  approach: 'sequential' | 'parallel' | 'adaptive' | 'hybrid';
  decomposition: 'functional' | 'temporal' | 'resource-based' | 'capability-based';
  optimization: 'time' | 'quality' | 'cost' | 'risk' | 'balanced';
  adaptability: 'rigid' | 'flexible' | 'dynamic';
}

export interface ExecutionPhase {
  id: string;
  name: string;
  description: string;
  tasks: string[];
  dependencies: string[];
  estimatedDuration: number;
  requiredCapabilities: AgentCapability[];
  assignedAgents: string[];
  status: TaskStatus;
  startTime?: string;
  endTime?: string;
}

export interface ResourceAllocation {
  type: 'agent' | 'tool' | 'data' | 'compute' | 'time';
  resource: string;
  allocation: number;
  duration: number;
  priority: TaskPriority;
  constraints: string[];
}

export interface Timeline {
  start: string;
  end: string;
  milestones: Milestone[];
  criticalPath: string[];
  bufferTime: number;
}

export interface Milestone {
  id: string;
  name: string;
  description: string;
  targetDate: string;
  dependencies: string[];
  successCriteria: string[];
  status: 'pending' | 'achieved' | 'missed' | 'at_risk';
}

export interface RiskAssessment {
  risks: Risk[];
  overallRiskLevel: 'low' | 'medium' | 'high' | 'critical';
  mitigationStrategies: MitigationStrategy[];
}

export interface Risk {
  id: string;
  description: string;
  category: 'technical' | 'resource' | 'timeline' | 'quality' | 'external';
  probability: number;
  impact: number;
  riskLevel: number;
  indicators: string[];
  mitigations: string[];
}

export interface MitigationStrategy {
  riskId: string;
  strategy: string;
  actions: string[];
  responsible: string;
  timeline: string;
  cost: number;
  effectiveness: number;
}

export interface SuccessCriteria {
  id: string;
  description: string;
  metric: string;
  target: number;
  threshold: number;
  measurement: 'objective' | 'subjective' | 'hybrid';
  frequency: 'continuous' | 'periodic' | 'milestone' | 'final';
}

export interface ContingencyPlan {
  id: string;
  trigger: string;
  condition: string;
  actions: ContingencyAction[];
  responsible: string;
  activationCriteria: string[];
}

export interface ContingencyAction {
  type: 'reassign' | 'escalate' | 'modify_plan' | 'add_resources' | 'change_strategy';
  description: string;
  parameters: Record<string, any>;
  timeline: string;
  cost: number;
}

/**
 * Orchestration configuration and state
 */
export interface OrchestrationConfig {
  pattern: OrchestrationPattern;
  agents: AgentContext[];
  coordinationStrategy: CoordinationStrategy;
  communicationProtocol: CommunicationProtocol;
  performanceThresholds: PerformanceThresholds;
  adaptationRules: AdaptationRule[];
  loggingLevel: 'minimal' | 'standard' | 'detailed' | 'debug';
  monitoringEnabled: boolean;
}

export interface PerformanceThresholds {
  responseTime: number;
  successRate: number;
  taskCompletionRate: number;
  collaborationEfficiency: number;
  resourceUtilization: number;
}

export interface AdaptationRule {
  condition: string;
  action: 'reassign' | 'escalate' | 'modify_strategy' | 'add_agent' | 'remove_agent';
  parameters: Record<string, any>;
  cooldown: number;
}

export interface OrchestrationState extends GraphState {
  agents: Record<string, AgentContext>;
  tasks: Record<string, Task>;
  messages: Message[];
  currentPlan?: ExecutionPlan;
  performance: OrchestrationPerformance;
  metadata: Record<string, any>;
}

export interface OrchestrationPerformance {
  overallEfficiency: number;
  taskCompletionRate: number;
  averageResponseTime: number;
  resourceUtilization: number;
  collaborationScore: number;
  adaptationRate: number;
  errorRate: number;
  lastUpdated: string;
}

/**
 * Orchestrator interface
 */
export interface IOrchestrator extends Runnable<OrchestrationState, OrchestrationState> {
  addAgent(agent: AgentContext): Promise<void>;
  removeAgent(agentId: string): Promise<void>;
  assignTask(task: Task): Promise<RoutingDecision>;
  coordinateAgents(strategy: CoordinationStrategy): Promise<void>;
  adaptStrategy(performance: OrchestrationPerformance): Promise<void>;
  getPerformanceMetrics(): Promise<OrchestrationPerformance>;
  optimizeWorkflow(): Promise<void>;
}
