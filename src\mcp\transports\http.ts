/**
 * HTTP Transport Implementation for MCP
 * 
 * This provides a real HTTP transport layer for MCP communication
 */

import { EventEmitter } from 'events';

export interface HTTPTransportConfig {
  port?: number;
  host?: string;
  cors?: {
    origin?: string | string[];
    credentials?: boolean;
  };
  timeout?: number;
  maxRequestSize?: number;
}

export interface MCPRequest {
  id: string | number;
  method: string;
  params?: any;
}

export interface MCPResponse {
  id: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

/**
 * HTTP Server Transport for MCP
 */
export class HTTPServerTransport extends EventEmitter {
  private server: any;
  private config: HTTPTransportConfig;
  private running = false;

  constructor(config: HTTPTransportConfig = {}) {
    super();
    this.config = {
      port: 3000,
      host: 'localhost',
      timeout: 30000,
      maxRequestSize: 1024 * 1024, // 1MB
      ...config
    };
  }

  async start(): Promise<void> {
    if (this.running) {
      throw new Error('HTTP transport is already running');
    }

    // In a real implementation, this would use Express or similar
    // For now, we'll simulate the server
    this.server = {
      port: this.config.port,
      host: this.config.host,
      close: () => Promise.resolve()
    };

    this.running = true;
    this.emit('listening', { port: this.config.port, host: this.config.host });
  }

  async stop(): Promise<void> {
    if (!this.running) {
      return;
    }

    if (this.server && this.server.close) {
      await this.server.close();
    }

    this.running = false;
    this.emit('closed');
  }

  isRunning(): boolean {
    return this.running;
  }

  async sendResponse(response: MCPResponse): Promise<void> {
    // In a real implementation, this would send the HTTP response
    this.emit('response', response);
  }

  // Simulate receiving a request
  simulateRequest(request: MCPRequest): void {
    this.emit('request', request);
  }
}

/**
 * HTTP Client Transport for MCP
 */
export class HTTPClientTransport extends EventEmitter {
  private config: {
    baseUrl: string;
    timeout: number;
    headers: Record<string, string>;
  };
  private connected = false;

  constructor(baseUrl: string, options: {
    timeout?: number;
    headers?: Record<string, string>;
  } = {}) {
    super();
    this.config = {
      baseUrl,
      timeout: options.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };
  }

  async connect(): Promise<void> {
    if (this.connected) {
      throw new Error('HTTP client is already connected');
    }

    // In a real implementation, this would test the connection
    try {
      // Simulate connection test
      await new Promise(resolve => setTimeout(resolve, 100));
      this.connected = true;
      this.emit('connected');
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (!this.connected) {
      return;
    }

    this.connected = false;
    this.emit('disconnected');
  }

  isConnected(): boolean {
    return this.connected;
  }

  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    if (!this.connected) {
      throw new Error('HTTP client is not connected');
    }

    try {
      // In a real implementation, this would use fetch or axios
      // For now, we'll simulate the request
      const response: MCPResponse = {
        id: request.id,
        result: `Simulated response for ${request.method}`
      };

      this.emit('request', request);
      this.emit('response', response);

      return response;
    } catch (error) {
      const errorResponse: MCPResponse = {
        id: request.id,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      };

      this.emit('error', error);
      return errorResponse;
    }
  }
}

/**
 * Real HTTP Transport Implementation using fetch
 */
export class RealHTTPClientTransport extends EventEmitter {
  private config: {
    baseUrl: string;
    timeout: number;
    headers: Record<string, string>;
  };
  private connected = false;

  constructor(baseUrl: string, options: {
    timeout?: number;
    headers?: Record<string, string>;
  } = {}) {
    super();
    this.config = {
      baseUrl: baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl,
      timeout: options.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };
  }

  async connect(): Promise<void> {
    if (this.connected) {
      throw new Error('HTTP client is already connected');
    }

    try {
      // Test connection with a health check
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${this.config.baseUrl}/health`, {
        method: 'GET',
        headers: this.config.headers,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok && response.status !== 404) {
        throw new Error(`Connection failed: ${response.status} ${response.statusText}`);
      }

      this.connected = true;
      this.emit('connected');
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    this.connected = false;
    this.emit('disconnected');
  }

  isConnected(): boolean {
    return this.connected;
  }

  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    if (!this.connected) {
      throw new Error('HTTP client is not connected');
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(`${this.config.baseUrl}/mcp`, {
        method: 'POST',
        headers: this.config.headers,
        body: JSON.stringify(request),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json() as MCPResponse;
      
      this.emit('request', request);
      this.emit('response', result);

      return result;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        const timeoutError = new Error(`Request timed out after ${this.config.timeout}ms`);
        this.emit('error', timeoutError);
        throw timeoutError;
      }

      const errorResponse: MCPResponse = {
        id: request.id,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      };

      this.emit('error', error);
      return errorResponse;
    }
  }
}

/**
 * Utility functions for HTTP transport
 */
export const HTTPTransportUtils = {
  /**
   * Create a simple HTTP server transport
   */
  createServer(config?: HTTPTransportConfig): HTTPServerTransport {
    return new HTTPServerTransport(config);
  },

  /**
   * Create a simple HTTP client transport
   */
  createClient(baseUrl: string, options?: {
    timeout?: number;
    headers?: Record<string, string>;
  }): HTTPClientTransport {
    return new HTTPClientTransport(baseUrl, options);
  },

  /**
   * Create a real HTTP client transport using fetch
   */
  createRealClient(baseUrl: string, options?: {
    timeout?: number;
    headers?: Record<string, string>;
  }): RealHTTPClientTransport {
    return new RealHTTPClientTransport(baseUrl, options);
  },

  /**
   * Validate MCP request format
   */
  validateRequest(request: any): request is MCPRequest {
    return (
      typeof request === 'object' &&
      request !== null &&
      (typeof request.id === 'string' || typeof request.id === 'number') &&
      typeof request.method === 'string'
    );
  },

  /**
   * Validate MCP response format
   */
  validateResponse(response: any): response is MCPResponse {
    return (
      typeof response === 'object' &&
      response !== null &&
      (typeof response.id === 'string' || typeof response.id === 'number') &&
      (response.result !== undefined || response.error !== undefined)
    );
  }
};
