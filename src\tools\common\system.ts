import { z } from 'zod';
import { BaseTool } from '../base.js';
import { ToolConfig, ToolContext, ToolExecutionResult, ToolCategory } from '../types.js';

/**
 * Environment variable tool input schema
 */
const EnvVarInputSchema = z.object({
  operation: z.enum(['get', 'set', 'list', 'check']),
  key: z.string().optional(),
  value: z.string().optional(),
  defaultValue: z.string().optional()
});

type EnvVarInput = z.infer<typeof EnvVarInputSchema>;

/**
 * Environment variable tool
 */
export class EnvironmentVariableTool extends BaseTool<EnvVarInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'env_var',
      title: 'Environment Variables',
      description: 'Manage environment variables',
      category: ToolCategory.SYSTEM,
      tags: ['environment', 'variables', 'config', 'system'],
      examples: [
        {
          description: 'Get environment variable',
          input: { operation: 'get', key: 'NODE_ENV' }
        },
        {
          description: 'Check if variable exists',
          input: { operation: 'check', key: 'API_KEY' }
        }
      ]
    };

    super(config, EnvVarInputSchema, undefined);
  }

  async execute(input: EnvVarInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      let result: any;

      switch (input.operation) {
        case 'get':
          if (!input.key) {
            return this.createErrorResult('Key is required for get operation');
          }
          result = {
            key: input.key,
            value: process.env[input.key] || input.defaultValue || null,
            exists: input.key in process.env
          };
          break;

        case 'set':
          if (!input.key || input.value === undefined) {
            return this.createErrorResult('Key and value are required for set operation');
          }
          process.env[input.key] = input.value;
          result = {
            key: input.key,
            value: input.value,
            success: true
          };
          break;

        case 'list':
          // Only return non-sensitive environment variables
          const safeEnvVars = Object.entries(process.env)
            .filter(([key]) => !this.isSensitiveKey(key))
            .reduce((acc, [key, value]) => {
              acc[key] = value;
              return acc;
            }, {} as Record<string, string | undefined>);
          
          result = {
            count: Object.keys(safeEnvVars).length,
            variables: safeEnvVars
          };
          break;

        case 'check':
          if (!input.key) {
            return this.createErrorResult('Key is required for check operation');
          }
          result = {
            key: input.key,
            exists: input.key in process.env,
            hasValue: Boolean(process.env[input.key])
          };
          break;

        default:
          return this.createErrorResult(`Unknown operation: ${input.operation}`);
      }

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `Environment variable ${input.operation} completed:\n${JSON.stringify(result, null, 2)}`
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `Environment variable operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private isSensitiveKey(key: string): boolean {
    const sensitivePatterns = [
      /password/i,
      /secret/i,
      /key/i,
      /token/i,
      /auth/i,
      /credential/i,
      /private/i
    ];
    
    return sensitivePatterns.some(pattern => pattern.test(key));
  }
}

/**
 * System info tool input schema
 */
const SystemInfoInputSchema = z.object({
  category: z.enum(['os', 'process', 'memory', 'cpu', 'network', 'all']).default('all')
});

type SystemInfoInput = z.infer<typeof SystemInfoInputSchema>;

/**
 * System information tool
 */
export class SystemInfoTool extends BaseTool<SystemInfoInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'system_info',
      title: 'System Information',
      description: 'Get system and process information',
      category: ToolCategory.SYSTEM,
      tags: ['system', 'info', 'os', 'process', 'memory'],
      examples: [
        {
          description: 'Get all system info',
          input: { category: 'all' }
        },
        {
          description: 'Get memory info only',
          input: { category: 'memory' }
        }
      ]
    };

    super(config, SystemInfoInputSchema, undefined);
  }

  async execute(input: SystemInfoInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      let result: any = {};

      if (input.category === 'os' || input.category === 'all') {
        result.os = {
          platform: process.platform,
          arch: process.arch,
          version: process.version,
          nodeVersion: process.versions.node,
          v8Version: process.versions.v8
        };
      }

      if (input.category === 'process' || input.category === 'all') {
        result.process = {
          pid: process.pid,
          ppid: process.ppid,
          title: process.title,
          argv: process.argv,
          execPath: process.execPath,
          cwd: process.cwd(),
          uptime: process.uptime()
        };
      }

      if (input.category === 'memory' || input.category === 'all') {
        const memUsage = process.memoryUsage();
        result.memory = {
          rss: this.formatBytes(memUsage.rss),
          heapTotal: this.formatBytes(memUsage.heapTotal),
          heapUsed: this.formatBytes(memUsage.heapUsed),
          external: this.formatBytes(memUsage.external),
          arrayBuffers: this.formatBytes(memUsage.arrayBuffers || 0)
        };
      }

      if (input.category === 'cpu' || input.category === 'all') {
        result.cpu = {
          usage: process.cpuUsage(),
          loadAverage: typeof process.loadavg === 'function' ? process.loadavg() : null
        };
      }

      if (input.category === 'network' || input.category === 'all') {
        result.network = {
          hostname: typeof require !== 'undefined' ? require('os').hostname() : 'unknown'
        };
      }

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `System information (${input.category}):\n${JSON.stringify(result, null, 2)}`
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `System info retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}

/**
 * Date/time tool input schema
 */
const DateTimeInputSchema = z.object({
  operation: z.enum(['now', 'format', 'parse', 'add', 'subtract', 'diff', 'timezone']),
  date: z.string().optional(),
  format: z.string().optional(),
  amount: z.number().optional(),
  unit: z.enum(['milliseconds', 'seconds', 'minutes', 'hours', 'days', 'weeks', 'months', 'years']).optional(),
  timezone: z.string().optional(),
  targetDate: z.string().optional()
});

type DateTimeInput = z.infer<typeof DateTimeInputSchema>;

/**
 * Date/time utility tool
 */
export class DateTimeTool extends BaseTool<DateTimeInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'datetime',
      title: 'Date/Time Utilities',
      description: 'Date and time manipulation utilities',
      category: ToolCategory.UTILITY,
      tags: ['date', 'time', 'format', 'parse', 'timezone'],
      examples: [
        {
          description: 'Get current date/time',
          input: { operation: 'now' }
        },
        {
          description: 'Format a date',
          input: { 
            operation: 'format', 
            date: '2023-12-25T10:30:00Z',
            format: 'YYYY-MM-DD HH:mm:ss'
          }
        }
      ]
    };

    super(config, DateTimeInputSchema, undefined);
  }

  async execute(input: DateTimeInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      let result: any;

      switch (input.operation) {
        case 'now':
          const now = new Date();
          result = {
            iso: now.toISOString(),
            timestamp: now.getTime(),
            local: now.toLocaleString(),
            utc: now.toUTCString(),
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
          };
          break;

        case 'format':
          if (!input.date) {
            return this.createErrorResult('Date is required for format operation');
          }
          const dateToFormat = new Date(input.date);
          result = {
            original: input.date,
            formatted: this.formatDate(dateToFormat, input.format),
            iso: dateToFormat.toISOString(),
            timestamp: dateToFormat.getTime()
          };
          break;

        case 'parse':
          if (!input.date) {
            return this.createErrorResult('Date string is required for parse operation');
          }
          const parsedDate = new Date(input.date);
          if (isNaN(parsedDate.getTime())) {
            return this.createErrorResult('Invalid date string');
          }
          result = {
            original: input.date,
            parsed: parsedDate.toISOString(),
            timestamp: parsedDate.getTime(),
            valid: true
          };
          break;

        case 'add':
        case 'subtract':
          if (!input.date || !input.amount || !input.unit) {
            return this.createErrorResult('Date, amount, and unit are required for add/subtract operations');
          }
          const baseDate = new Date(input.date);
          const modifiedDate = this.modifyDate(baseDate, input.amount, input.unit, input.operation === 'add');
          result = {
            original: input.date,
            modified: modifiedDate.toISOString(),
            operation: input.operation,
            amount: input.amount,
            unit: input.unit
          };
          break;

        case 'diff':
          if (!input.date || !input.targetDate) {
            return this.createErrorResult('Both date and targetDate are required for diff operation');
          }
          const date1 = new Date(input.date);
          const date2 = new Date(input.targetDate);
          const diffMs = Math.abs(date2.getTime() - date1.getTime());
          result = {
            date1: input.date,
            date2: input.targetDate,
            differenceMs: diffMs,
            differenceDays: Math.floor(diffMs / (1000 * 60 * 60 * 24)),
            differenceHours: Math.floor(diffMs / (1000 * 60 * 60)),
            differenceMinutes: Math.floor(diffMs / (1000 * 60))
          };
          break;

        case 'timezone':
          const tzDate = input.date ? new Date(input.date) : new Date();
          result = {
            date: tzDate.toISOString(),
            currentTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            utc: tzDate.toUTCString(),
            local: tzDate.toLocaleString()
          };
          
          if (input.timezone) {
            try {
              result.targetTimezone = input.timezone;
              result.targetLocal = tzDate.toLocaleString('en-US', { timeZone: input.timezone });
            } catch {
              result.error = 'Invalid timezone';
            }
          }
          break;

        default:
          return this.createErrorResult(`Unknown operation: ${input.operation}`);
      }

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `Date/time ${input.operation} completed:\n${JSON.stringify(result, null, 2)}`
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `Date/time operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private formatDate(date: Date, format?: string): string {
    if (!format) {
      return date.toISOString();
    }

    // Simple format implementation - in real use, you'd want a proper date formatting library
    return format
      .replace('YYYY', date.getFullYear().toString())
      .replace('MM', (date.getMonth() + 1).toString().padStart(2, '0'))
      .replace('DD', date.getDate().toString().padStart(2, '0'))
      .replace('HH', date.getHours().toString().padStart(2, '0'))
      .replace('mm', date.getMinutes().toString().padStart(2, '0'))
      .replace('ss', date.getSeconds().toString().padStart(2, '0'));
  }

  private modifyDate(date: Date, amount: number, unit: string, add: boolean): Date {
    const result = new Date(date);
    const multiplier = add ? 1 : -1;
    const value = amount * multiplier;

    switch (unit) {
      case 'milliseconds':
        result.setMilliseconds(result.getMilliseconds() + value);
        break;
      case 'seconds':
        result.setSeconds(result.getSeconds() + value);
        break;
      case 'minutes':
        result.setMinutes(result.getMinutes() + value);
        break;
      case 'hours':
        result.setHours(result.getHours() + value);
        break;
      case 'days':
        result.setDate(result.getDate() + value);
        break;
      case 'weeks':
        result.setDate(result.getDate() + (value * 7));
        break;
      case 'months':
        result.setMonth(result.getMonth() + value);
        break;
      case 'years':
        result.setFullYear(result.getFullYear() + value);
        break;
    }

    return result;
  }
}
