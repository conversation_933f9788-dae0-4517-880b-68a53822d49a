import { z } from 'zod';
import type { Tool as AG3NT<PERSON><PERSON>ool, ToolExecutionResult } from '../tools/types.js';
import type { NodeFunction, AgentState } from '../core/types.js';
import {
  MCPToolDefinition,
  MCPServerInterface,
  MCPClientInterface,
  MCPServerConfig,
  AG3NTICToMCPAdapter,
  MCPToAG3NTICAdapter
} from './types.js';
import { AG3NTICMCPServer } from './server.js';
import { SimpleTool } from '../tools/base.js';

/**
 * Adapter to convert AG3NTIC tools to MCP tools
 */
export class DefaultAG3NTICToMCPAdapter implements AG3NTICToMCPAdapter {
  /**
   * Convert AG3NTIC tool to MCP tool definition
   */
  convertTool(tool: AG3NTICTool): MCPToolDefinition {
    return {
      name: tool.config.name,
      description: tool.config.description,
      inputSchema: this.zodSchemaToJSON(tool.inputSchema),
      handler: async (args: any) => {
        try {
          const result = await tool.execute(args);
          
          if (!result.success) {
            return {
              content: [{
                type: 'text',
                text: `Error: ${result.error || 'Unknown error'}`
              }],
              isError: true
            };
          }

          return {
            content: result.content || [{
              type: 'text',
              text: JSON.stringify(result.data, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            }],
            isError: true
          };
        }
      }
    };
  }

  /**
   * Convert AG3NTIC graph node to MCP tool definition
   */
  convertGraphNode(name: string, node: NodeFunction<AgentState>): MCPToolDefinition {
    return {
      name,
      description: `AG3NTIC graph node: ${name}`,
      inputSchema: {
        type: 'object',
        properties: {
          state: {
            type: 'object',
            description: 'Graph state to pass to the node'
          }
        },
        required: ['state']
      },
      handler: async (args: any) => {
        try {
          const { state } = args;
          const result = await node(state);
          
          return {
            content: [{
              type: 'text',
              text: JSON.stringify(result, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            }],
            isError: true
          };
        }
      }
    };
  }

  /**
   * Create MCP server from AG3NTIC tools
   */
  createServerFromTools(tools: AG3NTICTool[], config: MCPServerConfig): MCPServerInterface {
    const server = new AG3NTICMCPServer(config);
    
    // Register all tools
    tools.forEach(tool => {
      const mcpTool = this.convertTool(tool);
      server.registerTool(mcpTool);
    });

    return server;
  }

  /**
   * Convert Zod schema to JSON schema (simplified)
   */
  private zodSchemaToJSON(schema: z.ZodSchema): any {
    // This is a simplified conversion
    // In a real implementation, you might want to use a library like zod-to-json-schema
    try {
      // Try to get the schema description if available
      const description = (schema as any)._def?.description;
      
      // For now, return a generic object schema
      // This should be enhanced to properly convert Zod schemas
      return {
        type: 'object',
        description: description || 'Tool input parameters',
        additionalProperties: true
      };
    } catch {
      return {
        type: 'object',
        additionalProperties: true
      };
    }
  }
}

/**
 * Adapter to convert MCP tools to AG3NTIC tools
 */
export class DefaultMCPToAG3NTICAdapter implements MCPToAG3NTICAdapter {
  /**
   * Convert MCP tool to AG3NTIC graph node
   */
  convertToolToNode(client: MCPClientInterface, toolName: string): NodeFunction<AgentState> {
    return async (state: AgentState) => {
      try {
        // Simple implementation - call the MCP tool and add result to state
        const result = await client.callTool(toolName, {});

        return {
          ...state,
          messages: [
            ...state.messages,
            {
              role: 'assistant' as const,
              content: result.content?.[0]?.text || 'MCP tool executed',
              tool_calls: undefined
            }
          ]
        };
      } catch (error) {
        return {
          ...state,
          messages: [
            ...state.messages,
            {
              role: 'assistant' as const,
              content: `Error calling MCP tool ${toolName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
              tool_calls: undefined
            }
          ]
        };
      }
    };
  }

  /**
   * Convert MCP resource to AG3NTIC graph node
   */
  convertResourceToNode(client: MCPClientInterface, resourceUri: string): NodeFunction<AgentState> {
    return async (state: AgentState) => {
      try {
        const result = await client.readResource(resourceUri);

        return {
          ...state,
          messages: [
            ...state.messages,
            {
              role: 'assistant' as const,
              content: result.contents?.[0]?.text || `Resource: ${resourceUri}`,
              tool_calls: undefined
            }
          ]
        };
      } catch (error) {
        return {
          ...state,
          messages: [
            ...state.messages,
            {
              role: 'assistant' as const,
              content: `Error reading resource ${resourceUri}: ${error instanceof Error ? error.message : 'Unknown error'}`,
              tool_calls: undefined
            }
          ]
        };
      }
    };
  }

  /**
   * Create AG3NTIC tools from MCP client
   */
  async createToolsFromClient(client: MCPClientInterface): Promise<AG3NTICTool[]> {
    if (!client.isConnected()) {
      throw new Error('MCP client is not connected');
    }

    const tools: AG3NTICTool[] = [];
    
    try {
      // Get available tools from the MCP server
      const mcpTools = await client.listTools();
      
      for (const mcpTool of mcpTools) {
        const ag3nticTool = new SimpleTool(
          {
            name: mcpTool.name,
            title: mcpTool.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            description: mcpTool.description,
            category: 'mcp',
            tags: ['mcp', 'external']
          },
          z.any(), // MCP tools don't have strict schemas in our conversion
          async (input: any, context?: any): Promise<ToolExecutionResult> => {
            try {
              const result = await client.callTool(mcpTool.name, input);
              
              return {
                success: !result.isError,
                data: result.content,
                content: result.content,
                error: result.isError ? 'MCP tool returned error' : undefined
              };
            } catch (error) {
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                content: [{
                  type: 'text',
                  text: `Error calling MCP tool: ${error instanceof Error ? error.message : 'Unknown error'}`
                }]
              };
            }
          }
        );
        
        tools.push(ag3nticTool);
      }
    } catch (error) {
      throw new Error(`Failed to create tools from MCP client: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return tools;
  }
}

/**
 * Create default AG3NTIC to MCP adapter
 */
export function createAG3NTICToMCPAdapter(): AG3NTICToMCPAdapter {
  return new DefaultAG3NTICToMCPAdapter();
}

/**
 * Create default MCP to AG3NTIC adapter
 */
export function createMCPToAG3NTICAdapter(): MCPToAG3NTICAdapter {
  return new DefaultMCPToAG3NTICAdapter();
}

/**
 * Utility function to create an MCP server from AG3NTIC tools
 */
export async function createMCPServerFromAG3NTICTools(
  tools: AG3NTICTool[],
  serverName: string,
  serverVersion: string = '1.0.0',
  transportType: 'stdio' | 'streamable-http' = 'stdio'
): Promise<MCPServerInterface> {
  const adapter = createAG3NTICToMCPAdapter();
  
  const config: MCPServerConfig = {
    name: serverName,
    version: serverVersion,
    description: `AG3NTIC MCP Server with ${tools.length} tools`,
    transport: {
      type: transportType
    },
    capabilities: {
      tools: true,
      resources: false,
      prompts: false,
      logging: false
    }
  };

  return adapter.createServerFromTools(tools, config);
}

/**
 * Utility function to create AG3NTIC tools from an MCP client
 */
export async function createAG3NTICToolsFromMCPClient(
  client: MCPClientInterface
): Promise<AG3NTICTool[]> {
  const adapter = createMCPToAG3NTICAdapter();
  return adapter.createToolsFromClient(client);
}

/**
 * Utility function to create graph nodes from MCP client tools
 */
export async function createGraphNodesFromMCPClient(
  client: MCPClientInterface
): Promise<Record<string, GraphNode>> {
  if (!client.isConnected()) {
    throw new Error('MCP client is not connected');
  }

  const adapter = createMCPToAG3NTICAdapter();
  const nodes: Record<string, GraphNode> = {};
  
  try {
    const mcpTools = await client.listTools();
    
    for (const mcpTool of mcpTools) {
      nodes[mcpTool.name] = adapter.convertToolToNode(client, mcpTool.name);
    }
  } catch (error) {
    throw new Error(`Failed to create graph nodes from MCP client: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return nodes;
}
