/**
 * API Client MCP Tool
 * 
 * Provides HTTP client capabilities for making API requests with
 * authentication, retry logic, and response processing.
 */

import { z } from 'zod';
import { BaseTool } from '../base.js';
import { MCPTool, MCPToolConfig, MCPToolContext, MCPToolExecutionResult, MCPCapabilities, MCPToolCategory } from './types.js';

const APIClientInputSchema = z.object({
  url: z.string().url().describe('API endpoint URL'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).default('GET').describe('HTTP method'),
  headers: z.record(z.string()).optional().describe('Request headers'),
  body: z.any().optional().describe('Request body'),
  auth: z.object({
    type: z.enum(['bearer', 'basic', 'apikey']),
    token: z.string().optional(),
    username: z.string().optional(),
    password: z.string().optional(),
    apiKey: z.string().optional(),
    headerName: z.string().optional()
  }).optional().describe('Authentication configuration'),
  timeout: z.number().default(30000).describe('Request timeout in milliseconds'),
  retries: z.number().default(3).describe('Number of retry attempts'),
  validateSSL: z.boolean().default(true).describe('Whether to validate SSL certificates')
});

export class APIClientTool extends BaseTool implements MCPTool {
  constructor() {
    const config: MCPToolConfig = {
      name: 'api_client',
      title: 'API Client',
      description: 'HTTP client for making API requests with authentication and retry logic',
      category: MCPToolCategory.API,
      tags: ['api', 'http', 'client', 'rest'],
      version: '1.0.0',
      author: 'AG3NTIC Framework',
      capabilities: { callTool: true, logging: true }
    };
    super(config, APIClientInputSchema, undefined);
  }

  getCapabilities(): MCPCapabilities {
    return { callTool: true, logging: true, listTools: true };
  }

  async execute(input: any, context?: MCPToolContext): Promise<MCPToolExecutionResult> {
    // Mock implementation
    const result = this.createSuccessResult({
      status: 200,
      data: { message: 'Mock API response' },
      headers: { 'content-type': 'application/json' }
    });

    return {
      ...result,
      content: result.content || []
    } as MCPToolExecutionResult;
  }
}
