/**
 * Composition operators for AG3NTIC Runnables
 * 
 * This module provides operator overloading and composition utilities
 * to enable LangChain-style syntax like: prompt | model | parser
 */

import { Runnable, RunnableSequence, BaseRunnable, RunnableConfig } from './runnable.js';

/**
 * Symbol for the pipe operator
 */
const PIPE_SYMBOL = Symbol('pipe');

/**
 * Enhanced Runnable interface with composition operators
 */
export interface ComposableRunnable<TInput = any, TOutput = any> extends Runnable<TInput, TOutput> {
  [PIPE_SYMBOL]<TNext>(next: Runnable<TOutput, TNext>): RunnableSequence<TInput, TNext>;
}

/**
 * Base class that implements composition operators
 */
export abstract class ComposableBaseRunnable<TInput = any, TOutput = any> 
  extends BaseRunnable<TInput, TOutput> 
  implements ComposableRunnable<TInput, TOutput> {

  [PIPE_SYMBOL]<TNext>(next: Runnable<TOutput, TNext>): RunnableSequence<TInput, TNext> {
    return this.pipe(next);
  }
}

/**
 * Utility function to make any runnable composable
 */
export function makeComposable<TInput, TOutput>(
  runnable: Runnable<TInput, TOutput>
): ComposableRunnable<TInput, TOutput> {
  if (isComposable(runnable)) {
    return runnable;
  }

  return new ComposableWrapper(runnable);
}

/**
 * Check if a runnable is already composable
 */
export function isComposable<TInput, TOutput>(
  runnable: Runnable<TInput, TOutput>
): runnable is ComposableRunnable<TInput, TOutput> {
  return PIPE_SYMBOL in runnable;
}

/**
 * Wrapper to make any runnable composable
 */
class ComposableWrapper<TInput, TOutput> extends ComposableBaseRunnable<TInput, TOutput> {
  constructor(private wrapped: Runnable<TInput, TOutput>) {
    super(wrapped.name);
  }

  async invoke(input: TInput, config?: RunnableConfig): Promise<TOutput> {
    return this.wrapped.invoke(input, config);
  }

  override async *stream(input: TInput, config?: RunnableConfig): AsyncGenerator<TOutput, void, unknown> {
    yield* this.wrapped.stream(input, config);
  }

  async batch(inputs: TInput[], config?: RunnableConfig): Promise<TOutput[]> {
    return this.wrapped.batch(inputs, config);
  }
}

/**
 * Parallel execution runnable
 */
export class RunnableParallel<TInput, TOutput extends Record<string, any>> 
  extends ComposableBaseRunnable<TInput, TOutput> {
  
  constructor(
    private runnables: Record<keyof TOutput, Runnable<TInput, any>>,
    name?: string
  ) {
    super(name || `RunnableParallel[${Object.keys(runnables).join(', ')}]`);
  }

  async invoke(input: TInput, config?: RunnableConfig): Promise<TOutput> {
    return this.executeWithCallbacks(async () => {
      const promises = Object.entries(this.runnables).map(async ([key, runnable]) => {
        const result = await runnable.invoke(input, config);
        return [key, result] as const;
      });

      const results = await Promise.all(promises);
      return Object.fromEntries(results) as TOutput;
    }, input, config);
  }

  override async *stream(input: TInput, config?: RunnableConfig): AsyncGenerator<TOutput, void, unknown> {
    // For parallel streaming, we'll collect all results and yield once
    // In a more advanced implementation, we could yield partial results as they arrive
    const result = await this.invoke(input, config);
    yield result;
  }
}

/**
 * Conditional runnable that routes based on input
 */
export class RunnableBranch<TInput, TOutput> extends ComposableBaseRunnable<TInput, TOutput> {
  constructor(
    private branches: Array<{
      condition: (input: TInput) => boolean | Promise<boolean>;
      runnable: Runnable<TInput, TOutput>;
    }>,
    private defaultRunnable?: Runnable<TInput, TOutput>,
    name?: string
  ) {
    super(name || 'RunnableBranch');
  }

  async invoke(input: TInput, config?: RunnableConfig): Promise<TOutput> {
    return this.executeWithCallbacks(async () => {
      for (const branch of this.branches) {
        const shouldExecute = await branch.condition(input);
        if (shouldExecute) {
          return branch.runnable.invoke(input, config);
        }
      }

      if (this.defaultRunnable) {
        return this.defaultRunnable.invoke(input, config);
      }

      throw new Error('No matching branch found and no default runnable provided');
    }, input, config);
  }

  override async *stream(input: TInput, config?: RunnableConfig): AsyncGenerator<TOutput, void, unknown> {
    for (const branch of this.branches) {
      const shouldExecute = await branch.condition(input);
      if (shouldExecute) {
        yield* branch.runnable.stream(input, config);
        return;
      }
    }

    if (this.defaultRunnable) {
      yield* this.defaultRunnable.stream(input, config);
      return;
    }

    throw new Error('No matching branch found and no default runnable provided');
  }
}

/**
 * Lambda runnable for simple transformations
 */
export class RunnableLambda<TInput, TOutput> extends ComposableBaseRunnable<TInput, TOutput> {
  constructor(
    private func: (input: TInput) => TOutput | Promise<TOutput>,
    name?: string
  ) {
    super(name || 'RunnableLambda');
  }

  async invoke(input: TInput, config?: RunnableConfig): Promise<TOutput> {
    return this.executeWithCallbacks(async () => {
      return await this.func(input);
    }, input, config);
  }
}

/**
 * Passthrough runnable that returns input unchanged
 */
export class RunnablePassthrough<T> extends ComposableBaseRunnable<T, T> {
  constructor() {
    super('RunnablePassthrough');
  }

  async invoke(input: T, config?: RunnableConfig): Promise<T> {
    return this.executeWithCallbacks(async () => input, input, config);
  }
}

/**
 * Utility functions for creating common runnables
 */
export const RunnableUtils = {
  /**
   * Create a lambda runnable from a function
   */
  lambda<TInput, TOutput>(
    func: (input: TInput) => TOutput | Promise<TOutput>,
    name?: string
  ): RunnableLambda<TInput, TOutput> {
    return new RunnableLambda(func, name);
  },

  /**
   * Create a parallel runnable from a record of runnables
   */
  parallel<TInput, TOutput extends Record<string, any>>(
    runnables: Record<keyof TOutput, Runnable<TInput, any>>,
    name?: string
  ): RunnableParallel<TInput, TOutput> {
    return new RunnableParallel(runnables, name);
  },

  /**
   * Create a conditional branch runnable
   */
  branch<TInput, TOutput>(
    branches: Array<{
      condition: (input: TInput) => boolean | Promise<boolean>;
      runnable: Runnable<TInput, TOutput>;
    }>,
    defaultRunnable?: Runnable<TInput, TOutput>,
    name?: string
  ): RunnableBranch<TInput, TOutput> {
    return new RunnableBranch(branches, defaultRunnable, name);
  },

  /**
   * Create a passthrough runnable
   */
  passthrough<T>(): RunnablePassthrough<T> {
    return new RunnablePassthrough<T>();
  },

  /**
   * Create a runnable that assigns values to the input
   */
  assign<TInput extends Record<string, any>, TOutput extends Record<string, any>>(
    assignments: Record<keyof TOutput, Runnable<TInput, any> | ((input: TInput) => any)>
  ): Runnable<TInput, TInput & TOutput> {
    const runnableAssignments: Record<string, Runnable<TInput, any>> = {};
    
    for (const [key, value] of Object.entries(assignments)) {
      if (typeof value === 'function') {
        runnableAssignments[key] = new RunnableLambda(value as any, `assign_${key}`);
      } else {
        runnableAssignments[key] = value;
      }
    }

    return new RunnableLambda(async (input: TInput) => {
      const parallel = new RunnableParallel(runnableAssignments);
      const results = await parallel.invoke(input);
      return { ...input, ...results } as TInput & TOutput;
    }, 'RunnableAssign');
  }
};

/**
 * Type-safe pipe function with better inference
 */
export function pipe<T1, T2>(
  r1: Runnable<T1, T2>
): ComposableRunnable<T1, T2>;

export function pipe<T1, T2, T3>(
  r1: Runnable<T1, T2>,
  r2: Runnable<T2, T3>
): RunnableSequence<T1, T3>;

export function pipe<T1, T2, T3, T4>(
  r1: Runnable<T1, T2>,
  r2: Runnable<T2, T3>,
  r3: Runnable<T3, T4>
): RunnableSequence<T1, T4>;

export function pipe<T1, T2, T3, T4, T5>(
  r1: Runnable<T1, T2>,
  r2: Runnable<T2, T3>,
  r3: Runnable<T3, T4>,
  r4: Runnable<T4, T5>
): RunnableSequence<T1, T5>;

export function pipe(...runnables: Runnable<any, any>[]): any {
  if (runnables.length === 0) {
    throw new Error('pipe() requires at least one runnable');
  }
  
  if (runnables.length === 1) {
    return makeComposable(runnables[0]);
  }
  
  return makeComposable(new RunnableSequence(runnables));
}

/**
 * Operator overloading setup (for environments that support it)
 */
declare global {
  interface Object {
    [PIPE_SYMBOL]?<TNext>(this: Runnable<any, any>, next: Runnable<any, TNext>): RunnableSequence<any, TNext>;
  }
}

// Add the pipe operator to the Object prototype (optional, for advanced usage)
if (typeof Object.defineProperty === 'function') {
  try {
    Object.defineProperty(Object.prototype, PIPE_SYMBOL, {
      value: function<TNext>(this: Runnable<any, any>, next: Runnable<any, TNext>) {
        if (this && typeof this.pipe === 'function') {
          return this.pipe(next);
        }
        throw new Error('Pipe operator can only be used on Runnable objects');
      },
      enumerable: false,
      configurable: true
    });
  } catch (error) {
    // Ignore errors in environments where this isn't supported
  }
}
