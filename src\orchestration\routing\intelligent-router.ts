/**
 * Intelligent Agent Router
 * 
 * Advanced routing system that uses ML-like algorithms to determine
 * the optimal agent for each task based on capabilities, performance,
 * workload, and contextual factors.
 */

import {
  AgentContext,
  Task,
  RoutingDecision,
  AgentCapability,
  TaskRequirement,
  OrchestrationConfig,
  AgentRole
} from '../types.js';

export class IntelligentRouter {
  
  private config: OrchestrationConfig;
  private routingHistory: RoutingDecision[] = [];
  private performanceWeights: Record<string, number> = {
    capability_match: 0.3,
    performance_score: 0.25,
    workload_balance: 0.2,
    specialization_fit: 0.15,
    collaboration_history: 0.1
  };

  constructor(config: OrchestrationConfig) {
    this.config = config;
  }

  /**
   * Route a task to the most suitable agent
   */
  async routeTask(task: Task, availableAgents: AgentContext[]): Promise<RoutingDecision> {
    const startTime = Date.now();
    
    // Filter agents by basic requirements
    const eligibleAgents = this.filterEligibleAgents(task, availableAgents);
    
    if (eligibleAgents.length === 0) {
      throw new Error(`No eligible agents found for task: ${task.description}`);
    }

    // Score each eligible agent
    const agentScores = await Promise.all(
      eligibleAgents.map(agent => this.scoreAgent(agent, task))
    );

    // Sort by score (highest first)
    const rankedAgents = agentScores
      .map((score, index) => ({ agent: eligibleAgents[index], score }))
      .sort((a, b) => b.score.total - a.score.total);

    // Select the best agent
    const selectedAgent = rankedAgents[0];
    
    // Generate alternatives
    const alternatives = rankedAgents.slice(1, 4).map(({ agent, score }) => ({
      agent: agent.id,
      confidence: score.total,
      reasoning: this.generateReasoning(score)
    }));

    const decision: RoutingDecision = {
      targetAgent: selectedAgent.agent.id,
      confidence: selectedAgent.score.total,
      reasoning: this.generateReasoning(selectedAgent.score),
      alternatives,
      context: {
        taskId: task.id,
        taskComplexity: task.complexity,
        taskPriority: task.priority,
        routingTime: Date.now() - startTime,
        eligibleAgentsCount: eligibleAgents.length,
        scoringDetails: selectedAgent.score
      },
      timestamp: new Date().toISOString()
    };

    // Record routing decision for learning
    this.routingHistory.push(decision);
    this.adaptWeights(decision);

    return decision;
  }

  /**
   * Filter agents that meet basic task requirements
   */
  private filterEligibleAgents(task: Task, agents: AgentContext[]): AgentContext[] {
    return agents.filter(agent => {
      // Check if agent has required capabilities
      const hasRequiredCapabilities = task.requirements
        .filter(req => req.type === 'capability' && req.importance === 'required')
        .every(req => agent.capabilities.includes(req.value as AgentCapability));

      // Check if agent has required tools
      const hasRequiredTools = task.requirements
        .filter(req => req.type === 'tool' && req.importance === 'required')
        .every(req => agent.tools.includes(req.value));

      // Check workload capacity
      const hasCapacity = agent.workload < this.getMaxWorkload(agent);

      // Check if agent is available (not in a critical task)
      const isAvailable = !this.isAgentInCriticalTask(agent);

      return hasRequiredCapabilities && hasRequiredTools && hasCapacity && isAvailable;
    });
  }

  /**
   * Score an agent for a specific task
   */
  private async scoreAgent(agent: AgentContext, task: Task): Promise<{
    total: number;
    breakdown: Record<string, number>;
  }> {
    const scores = {
      capability_match: this.scoreCapabilityMatch(agent, task),
      performance_score: this.scorePerformance(agent, task),
      workload_balance: this.scoreWorkloadBalance(agent),
      specialization_fit: this.scoreSpecializationFit(agent, task),
      collaboration_history: this.scoreCollaborationHistory(agent, task)
    };

    // Calculate weighted total
    const total = Object.entries(scores).reduce((sum, [key, score]) => {
      return sum + (score * this.performanceWeights[key]);
    }, 0);

    return {
      total: Math.min(1.0, Math.max(0.0, total)), // Normalize to 0-1
      breakdown: scores
    };
  }

  /**
   * Score how well agent capabilities match task requirements
   */
  private scoreCapabilityMatch(agent: AgentContext, task: Task): number {
    const requiredCapabilities = task.requirements
      .filter(req => req.type === 'capability')
      .map(req => ({ capability: req.value as AgentCapability, importance: req.importance }));

    if (requiredCapabilities.length === 0) return 0.8; // Default score if no specific requirements

    let totalScore = 0;
    let totalWeight = 0;

    for (const req of requiredCapabilities) {
      const weight = req.importance === 'required' ? 1.0 : 
                    req.importance === 'preferred' ? 0.7 : 0.3;
      
      const hasCapability = agent.capabilities.includes(req.capability);
      const score = hasCapability ? 1.0 : 0.0;
      
      totalScore += score * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  /**
   * Score agent's historical performance
   */
  private scorePerformance(agent: AgentContext, task: Task): number {
    const performance = agent.performance;
    
    // Weight different performance metrics
    const successWeight = 0.4;
    const speedWeight = 0.3;
    const reliabilityWeight = 0.3;

    const successScore = performance.successRate;
    const speedScore = Math.max(0, 1 - (performance.averageResponseTime / 10000)); // Normalize response time
    const reliabilityScore = performance.reliabilityScore;

    return (successScore * successWeight) + 
           (speedScore * speedWeight) + 
           (reliabilityScore * reliabilityWeight);
  }

  /**
   * Score workload balance (prefer less loaded agents)
   */
  private scoreWorkloadBalance(agent: AgentContext): number {
    const maxWorkload = this.getMaxWorkload(agent);
    const currentWorkload = agent.workload;
    
    // Higher score for lower workload
    return Math.max(0, 1 - (currentWorkload / maxWorkload));
  }

  /**
   * Score how well agent specializations fit the task
   */
  private scoreSpecializationFit(agent: AgentContext, task: Task): number {
    if (agent.specializations.length === 0) return 0.5; // Neutral score for generalists

    // Extract keywords from task description
    const taskKeywords = this.extractKeywords(task.description.toLowerCase());
    
    // Check overlap with agent specializations
    const matchingSpecializations = agent.specializations.filter(spec =>
      taskKeywords.some(keyword => spec.toLowerCase().includes(keyword))
    );

    return matchingSpecializations.length / agent.specializations.length;
  }

  /**
   * Score collaboration history with other agents involved in the task
   */
  private scoreCollaborationHistory(agent: AgentContext, task: Task): number {
    // Check if this task has dependencies that involve other agents
    const relatedAgents = this.getRelatedAgents(task);
    
    if (relatedAgents.length === 0) return 0.7; // Neutral score if no collaboration needed

    // Calculate average collaboration score with related agents
    const collaborationScores = relatedAgents.map(relatedAgentId => {
      const relationship = agent.memory.relationships.find(rel => rel.agentId === relatedAgentId);
      return relationship ? relationship.trustLevel : 0.5; // Default trust level
    });

    return collaborationScores.reduce((sum, score) => sum + score, 0) / collaborationScores.length;
  }

  /**
   * Generate human-readable reasoning for routing decision
   */
  private generateReasoning(score: { total: number; breakdown: Record<string, number> }): string {
    const reasons: string[] = [];
    
    if (score.breakdown.capability_match > 0.8) {
      reasons.push("excellent capability match");
    } else if (score.breakdown.capability_match > 0.6) {
      reasons.push("good capability match");
    }

    if (score.breakdown.performance_score > 0.8) {
      reasons.push("strong performance history");
    }

    if (score.breakdown.workload_balance > 0.7) {
      reasons.push("optimal workload balance");
    }

    if (score.breakdown.specialization_fit > 0.7) {
      reasons.push("relevant specialization");
    }

    if (score.breakdown.collaboration_history > 0.7) {
      reasons.push("positive collaboration history");
    }

    if (reasons.length === 0) {
      reasons.push("best available option");
    }

    return `Selected based on: ${reasons.join(", ")} (confidence: ${(score.total * 100).toFixed(1)}%)`;
  }

  /**
   * Adapt routing weights based on historical performance
   */
  private adaptWeights(decision: RoutingDecision): void {
    // This would implement learning from routing outcomes
    // For now, it's a placeholder for future ML integration
    
    // Example: If recent decisions with high capability_match scores performed well,
    // increase the weight for capability_match
    
    const recentDecisions = this.routingHistory.slice(-10);
    if (recentDecisions.length >= 5) {
      // Implement weight adaptation logic here
      this.optimizeWeights(recentDecisions);
    }
  }

  /**
   * Optimize routing weights based on historical performance
   */
  private optimizeWeights(recentDecisions: RoutingDecision[]): void {
    // Placeholder for weight optimization algorithm
    // This could use techniques like:
    // - Gradient descent
    // - Genetic algorithms
    // - Reinforcement learning
    // - Bayesian optimization
  }

  /**
   * Helper methods
   */
  private getMaxWorkload(agent: AgentContext): number {
    // Determine max workload based on agent role and capabilities
    switch (agent.role) {
      case AgentRole.SUPERVISOR:
        return 3; // Supervisors can handle fewer direct tasks
      case AgentRole.SPECIALIST:
        return 5; // Specialists can focus on more tasks
      case AgentRole.COORDINATOR:
        return 2; // Coordinators need bandwidth for coordination
      default:
        return 4; // Default workload capacity
    }
  }

  private isAgentInCriticalTask(agent: AgentContext): boolean {
    // Check if agent is currently handling a critical/urgent task
    return agent.currentTask !== undefined && 
           agent.memory.taskHistory.some(task => 
             task.id === agent.currentTask && 
             task.status === 'in_progress' &&
             task.complexity > 8
           );
  }

  private extractKeywords(text: string): string[] {
    // Simple keyword extraction (could be enhanced with NLP)
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
    
    return text
      .split(/\s+/)
      .map(word => word.replace(/[^\w]/g, ''))
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, 10); // Limit to top 10 keywords
  }

  private getRelatedAgents(task: Task): string[] {
    // Extract agent IDs that might be involved in task dependencies
    // This is a simplified implementation
    return task.dependencies
      .map(depId => this.findAgentForTask(depId))
      .filter(Boolean) as string[];
  }

  private findAgentForTask(taskId: string): string | undefined {
    // Find which agent is assigned to a specific task
    // This would typically query the current orchestration state
    return undefined; // Placeholder
  }

  /**
   * Get routing statistics and insights
   */
  getRoutingStats(): {
    totalDecisions: number;
    averageConfidence: number;
    topPerformingAgents: string[];
    routingPatterns: Record<string, number>;
  } {
    const totalDecisions = this.routingHistory.length;
    const averageConfidence = this.routingHistory.reduce((sum, decision) => 
      sum + decision.confidence, 0) / totalDecisions;

    // Count agent selections
    const agentCounts: Record<string, number> = {};
    this.routingHistory.forEach(decision => {
      agentCounts[decision.targetAgent] = (agentCounts[decision.targetAgent] || 0) + 1;
    });

    const topPerformingAgents = Object.entries(agentCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([agentId]) => agentId);

    return {
      totalDecisions,
      averageConfidence,
      topPerformingAgents,
      routingPatterns: agentCounts
    };
  }

  /**
   * Reset routing history (useful for testing or retraining)
   */
  resetHistory(): void {
    this.routingHistory = [];
  }

  /**
   * Update routing weights manually
   */
  updateWeights(newWeights: Partial<Record<string, number>>): void {
    this.performanceWeights = { ...this.performanceWeights, ...newWeights };
    
    // Normalize weights to sum to 1
    const totalWeight = Object.values(this.performanceWeights).reduce((sum, weight) => sum + weight, 0);
    Object.keys(this.performanceWeights).forEach(key => {
      this.performanceWeights[key] /= totalWeight;
    });
  }
}
