/**
 * Sequential Thinking MCP Tool
 * 
 * Provides structured thinking capabilities for complex problem-solving,
 * allowing AI agents to break down problems into sequential steps.
 */

import { z } from 'zod';
import { BaseTool } from '../base.js';
import { 
  MCPTool, 
  MCPToolConfig, 
  MCPToolContext, 
  MCPToolExecutionResult,
  MCPCapabilities,
  MCPToolCategory,
  MCPSchemas,
  MCPOutputSchemas
} from './types.js';

/**
 * Sequential thinking input schema
 */
const SequentialThinkingInputSchema = z.object({
  problem: z.string().describe('The problem or question to think through'),
  context: z.string().optional().describe('Additional context or background information'),
  steps: z.number().min(1).max(20).default(5).describe('Number of thinking steps to perform'),
  approach: z.enum(['analytical', 'creative', 'systematic', 'exploratory']).default('analytical').describe('Thinking approach to use'),
  depth: z.enum(['shallow', 'medium', 'deep']).default('medium').describe('Depth of analysis for each step'),
  constraints: z.array(z.string()).optional().describe('Constraints or limitations to consider'),
  goals: z.array(z.string()).optional().describe('Specific goals or objectives to achieve')
});

/**
 * Sequential thinking output schema
 */
const SequentialThinkingOutputSchema = z.object({
  thoughts: z.array(z.object({
    step: z.number().describe('Step number'),
    title: z.string().describe('Step title or focus'),
    thought: z.string().describe('The thinking content for this step'),
    reasoning: z.string().describe('Reasoning behind this thought'),
    insights: z.array(z.string()).optional().describe('Key insights from this step'),
    questions: z.array(z.string()).optional().describe('Questions raised by this step'),
    confidence: z.number().min(0).max(1).describe('Confidence level in this step')
  })),
  synthesis: z.object({
    summary: z.string().describe('Summary of all thinking steps'),
    keyInsights: z.array(z.string()).describe('Most important insights'),
    conclusions: z.array(z.string()).describe('Main conclusions reached'),
    recommendations: z.array(z.string()).optional().describe('Recommended actions'),
    uncertainties: z.array(z.string()).optional().describe('Areas of uncertainty'),
    nextSteps: z.array(z.string()).optional().describe('Suggested next steps')
  }),
  metadata: z.object({
    totalSteps: z.number(),
    approach: z.string(),
    depth: z.string(),
    processingTime: z.number().optional(),
    overallConfidence: z.number().min(0).max(1)
  })
});

type SequentialThinkingInput = z.infer<typeof SequentialThinkingInputSchema>;
type SequentialThinkingOutput = z.infer<typeof SequentialThinkingOutputSchema>;

/**
 * Sequential Thinking MCP Tool implementation
 */
export class SequentialThinkingTool extends BaseTool<SequentialThinkingInput, SequentialThinkingOutput> implements MCPTool<SequentialThinkingInput, SequentialThinkingOutput> {
  
  constructor() {
    const config: MCPToolConfig = {
      name: 'sequential_thinking',
      title: 'Sequential Thinking',
      description: 'Break down complex problems into sequential thinking steps for systematic analysis',
      category: MCPToolCategory.THINKING,
      tags: ['thinking', 'analysis', 'problem-solving', 'reasoning'],
      version: '1.0.0',
      author: 'AG3NTIC Framework',
      capabilities: {
        callTool: true,
        logging: true
      },
      examples: [
        {
          description: 'Analyze a business problem',
          input: {
            problem: 'How can we increase customer retention for our SaaS product?',
            steps: 5,
            approach: 'analytical'
          }
        },
        {
          description: 'Creative problem solving',
          input: {
            problem: 'Design a new user onboarding experience',
            steps: 6,
            approach: 'creative',
            depth: 'deep'
          }
        }
      ]
    };

    super(config, SequentialThinkingInputSchema, SequentialThinkingOutputSchema);
  }

  getCapabilities(): MCPCapabilities {
    return {
      callTool: true,
      logging: true,
      listTools: true
    };
  }

  async execute(input: SequentialThinkingInput, context?: MCPToolContext): Promise<MCPToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      const thoughts = await this.performSequentialThinking(input);
      const synthesis = await this.synthesizeThoughts(thoughts, input);
      
      const processingTime = Date.now() - startTime;
      const overallConfidence = thoughts.reduce((sum, t) => sum + t.confidence, 0) / thoughts.length;

      const result: SequentialThinkingOutput = {
        thoughts,
        synthesis,
        metadata: {
          totalSteps: thoughts.length,
          approach: input.approach,
          depth: input.depth,
          processingTime,
          overallConfidence
        }
      };

      return {
        success: true,
        data: result,
        content: [
          {
            type: 'text',
            text: this.formatThinkingOutput(result)
          }
        ],
        metadata: {
          processingTime,
          stepsCompleted: thoughts.length,
          approach: input.approach
        }
      };

    } catch (error) {
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Unknown error in sequential thinking',
        { processingTime: Date.now() - startTime }
      );
    }
  }

  private async performSequentialThinking(input: SequentialThinkingInput): Promise<SequentialThinkingOutput['thoughts']> {
    const thoughts: SequentialThinkingOutput['thoughts'] = [];
    
    for (let step = 1; step <= input.steps; step++) {
      const thought = await this.performThinkingStep(step, input, thoughts);
      thoughts.push(thought);
    }
    
    return thoughts;
  }

  private async performThinkingStep(
    stepNumber: number, 
    input: SequentialThinkingInput, 
    previousThoughts: SequentialThinkingOutput['thoughts']
  ): Promise<SequentialThinkingOutput['thoughts'][0]> {
    
    // Determine the focus for this step based on the approach
    const stepFocus = this.determineStepFocus(stepNumber, input.steps, input.approach);
    
    // Generate the thinking content based on the approach and depth
    const thinkingContent = await this.generateThinkingContent(
      stepNumber,
      stepFocus,
      input,
      previousThoughts
    );

    // Extract insights and questions
    const insights = this.extractInsights(thinkingContent, input.approach);
    const questions = this.generateQuestions(thinkingContent, stepNumber, input.steps);
    
    // Calculate confidence based on various factors
    const confidence = this.calculateStepConfidence(
      thinkingContent,
      stepNumber,
      input,
      previousThoughts
    );

    return {
      step: stepNumber,
      title: stepFocus,
      thought: thinkingContent,
      reasoning: this.generateReasoning(stepFocus, input.approach, stepNumber),
      insights,
      questions,
      confidence
    };
  }

  private determineStepFocus(stepNumber: number, totalSteps: number, approach: string): string {
    const stepRatio = stepNumber / totalSteps;
    
    switch (approach) {
      case 'analytical':
        if (stepRatio <= 0.2) return 'Problem Definition & Scope';
        if (stepRatio <= 0.4) return 'Data Gathering & Analysis';
        if (stepRatio <= 0.6) return 'Root Cause Analysis';
        if (stepRatio <= 0.8) return 'Solution Generation';
        return 'Evaluation & Recommendation';
        
      case 'creative':
        if (stepRatio <= 0.2) return 'Ideation & Brainstorming';
        if (stepRatio <= 0.4) return 'Concept Exploration';
        if (stepRatio <= 0.6) return 'Creative Synthesis';
        if (stepRatio <= 0.8) return 'Innovation Development';
        return 'Creative Validation';
        
      case 'systematic':
        if (stepRatio <= 0.2) return 'System Mapping';
        if (stepRatio <= 0.4) return 'Component Analysis';
        if (stepRatio <= 0.6) return 'Interaction Patterns';
        if (stepRatio <= 0.8) return 'System Optimization';
        return 'Implementation Strategy';
        
      case 'exploratory':
        if (stepRatio <= 0.2) return 'Initial Exploration';
        if (stepRatio <= 0.4) return 'Pattern Recognition';
        if (stepRatio <= 0.6) return 'Hypothesis Formation';
        if (stepRatio <= 0.8) return 'Testing & Validation';
        return 'Discovery Integration';
        
      default:
        return `Step ${stepNumber} Analysis`;
    }
  }

  private async generateThinkingContent(
    stepNumber: number,
    stepFocus: string,
    input: SequentialThinkingInput,
    previousThoughts: SequentialThinkingOutput['thoughts']
  ): Promise<string> {
    
    // Build context from previous thoughts
    const previousContext = previousThoughts.length > 0 
      ? `Previous insights: ${previousThoughts.map(t => t.insights?.join(', ')).filter(Boolean).join('; ')}`
      : '';

    // Generate thinking content based on the step focus and approach
    let content = `Focusing on: ${stepFocus}\n\n`;
    
    content += `Problem: ${input.problem}\n`;
    if (input.context) {
      content += `Context: ${input.context}\n`;
    }
    if (previousContext) {
      content += `${previousContext}\n`;
    }
    
    content += '\nThinking:\n';
    
    // Generate step-specific analysis
    switch (input.approach) {
      case 'analytical':
        content += this.generateAnalyticalThinking(stepFocus, input, previousThoughts);
        break;
      case 'creative':
        content += this.generateCreativeThinking(stepFocus, input, previousThoughts);
        break;
      case 'systematic':
        content += this.generateSystematicThinking(stepFocus, input, previousThoughts);
        break;
      case 'exploratory':
        content += this.generateExploratoryThinking(stepFocus, input, previousThoughts);
        break;
    }
    
    return content;
  }

  private generateAnalyticalThinking(
    stepFocus: string, 
    input: SequentialThinkingInput, 
    previousThoughts: SequentialThinkingOutput['thoughts']
  ): string {
    // Simulate analytical thinking process
    return `Analyzing ${stepFocus.toLowerCase()} for the problem: "${input.problem}". 
    
Key considerations:
- What are the core components involved?
- What data or evidence is available?
- What logical connections can be made?
- What assumptions need to be validated?

This step builds upon previous analysis to deepen understanding and move toward actionable insights.`;
  }

  private generateCreativeThinking(
    stepFocus: string, 
    input: SequentialThinkingInput, 
    previousThoughts: SequentialThinkingOutput['thoughts']
  ): string {
    return `Exploring ${stepFocus.toLowerCase()} creatively for: "${input.problem}".
    
Creative exploration:
- What unconventional approaches could work?
- How might we reframe the problem entirely?
- What analogies or metaphors apply?
- What would an ideal solution look like?

Building on creative momentum from previous steps to generate innovative possibilities.`;
  }

  private generateSystematicThinking(
    stepFocus: string, 
    input: SequentialThinkingInput, 
    previousThoughts: SequentialThinkingOutput['thoughts']
  ): string {
    return `Systematically examining ${stepFocus.toLowerCase()} for: "${input.problem}".
    
Systematic analysis:
- What are the system boundaries and components?
- How do different elements interact?
- What feedback loops exist?
- Where are the leverage points for change?

Integrating systematic understanding with insights from previous steps.`;
  }

  private generateExploratoryThinking(
    stepFocus: string, 
    input: SequentialThinkingInput, 
    previousThoughts: SequentialThinkingOutput['thoughts']
  ): string {
    return `Exploring ${stepFocus.toLowerCase()} to understand: "${input.problem}".
    
Exploratory investigation:
- What patterns are emerging?
- What questions does this raise?
- What unexpected connections exist?
- What hypotheses can we form?

Continuing exploration based on discoveries from previous steps.`;
  }

  private extractInsights(content: string, approach: string): string[] {
    // Simulate insight extraction based on approach
    const baseInsights = [
      'Key patterns identified in the problem space',
      'Important relationships between components',
      'Critical success factors for solutions'
    ];
    
    switch (approach) {
      case 'creative':
        return [...baseInsights, 'Novel approaches discovered', 'Creative opportunities identified'];
      case 'systematic':
        return [...baseInsights, 'System dynamics understood', 'Leverage points identified'];
      case 'exploratory':
        return [...baseInsights, 'New hypotheses formed', 'Unexpected connections found'];
      default:
        return baseInsights;
    }
  }

  private generateQuestions(content: string, stepNumber: number, totalSteps: number): string[] {
    const questions = [
      'What additional information would be helpful?',
      'What assumptions should be challenged?'
    ];
    
    if (stepNumber < totalSteps) {
      questions.push('What should be explored in the next step?');
    } else {
      questions.push('What implementation challenges might arise?');
    }
    
    return questions;
  }

  private generateReasoning(stepFocus: string, approach: string, stepNumber: number): string {
    return `This step focuses on ${stepFocus.toLowerCase()} using a ${approach} approach. 
    Step ${stepNumber} is designed to build upon previous insights while advancing toward a comprehensive understanding of the problem and potential solutions.`;
  }

  private calculateStepConfidence(
    content: string,
    stepNumber: number,
    input: SequentialThinkingInput,
    previousThoughts: SequentialThinkingOutput['thoughts']
  ): number {
    // Base confidence starts at 0.7
    let confidence = 0.7;
    
    // Increase confidence with more context
    if (input.context && input.context.length > 50) confidence += 0.1;
    
    // Increase confidence as we progress through steps
    confidence += (stepNumber / input.steps) * 0.1;
    
    // Adjust based on depth
    switch (input.depth) {
      case 'deep': confidence += 0.05; break;
      case 'shallow': confidence -= 0.05; break;
    }
    
    // Ensure confidence is within bounds
    return Math.max(0.1, Math.min(0.95, confidence));
  }

  private async synthesizeThoughts(
    thoughts: SequentialThinkingOutput['thoughts'],
    input: SequentialThinkingInput
  ): Promise<SequentialThinkingOutput['synthesis']> {
    
    const allInsights = thoughts.flatMap(t => t.insights || []);
    const keyInsights = [...new Set(allInsights)].slice(0, 5);
    
    const conclusions = [
      'Comprehensive analysis completed through sequential thinking',
      'Multiple perspectives considered for robust understanding',
      'Key patterns and relationships identified'
    ];
    
    const recommendations = [
      'Implement solutions based on highest-confidence insights',
      'Monitor key metrics identified during analysis',
      'Iterate based on feedback and new information'
    ];
    
    const uncertainties = thoughts
      .filter(t => t.confidence < 0.6)
      .map(t => `Uncertainty in ${t.title}: ${t.questions?.[0] || 'Further investigation needed'}`);
    
    const nextSteps = [
      'Validate key assumptions identified',
      'Develop detailed implementation plan',
      'Establish success metrics and monitoring'
    ];
    
    return {
      summary: `Sequential thinking analysis of "${input.problem}" completed using ${input.approach} approach across ${thoughts.length} steps.`,
      keyInsights,
      conclusions,
      recommendations,
      uncertainties: uncertainties.length > 0 ? uncertainties : undefined,
      nextSteps
    };
  }

  private formatThinkingOutput(result: SequentialThinkingOutput): string {
    let output = `# Sequential Thinking Analysis\n\n`;
    output += `**Problem:** ${result.thoughts[0]?.thought.split('\n')[1]?.replace('Problem: ', '') || 'N/A'}\n`;
    output += `**Approach:** ${result.metadata.approach}\n`;
    output += `**Steps:** ${result.metadata.totalSteps}\n`;
    output += `**Overall Confidence:** ${(result.metadata.overallConfidence * 100).toFixed(1)}%\n\n`;
    
    output += `## Thinking Steps\n\n`;
    for (const thought of result.thoughts) {
      output += `### Step ${thought.step}: ${thought.title}\n`;
      output += `**Confidence:** ${(thought.confidence * 100).toFixed(1)}%\n\n`;
      output += `${thought.thought}\n\n`;
      if (thought.insights && thought.insights.length > 0) {
        output += `**Insights:**\n${thought.insights.map(i => `- ${i}`).join('\n')}\n\n`;
      }
      if (thought.questions && thought.questions.length > 0) {
        output += `**Questions:**\n${thought.questions.map(q => `- ${q}`).join('\n')}\n\n`;
      }
    }
    
    output += `## Synthesis\n\n`;
    output += `**Summary:** ${result.synthesis.summary}\n\n`;
    output += `**Key Insights:**\n${result.synthesis.keyInsights.map(i => `- ${i}`).join('\n')}\n\n`;
    output += `**Conclusions:**\n${result.synthesis.conclusions.map(c => `- ${c}`).join('\n')}\n\n`;
    
    if (result.synthesis.recommendations) {
      output += `**Recommendations:**\n${result.synthesis.recommendations.map(r => `- ${r}`).join('\n')}\n\n`;
    }
    
    if (result.synthesis.uncertainties) {
      output += `**Uncertainties:**\n${result.synthesis.uncertainties.map(u => `- ${u}`).join('\n')}\n\n`;
    }
    
    if (result.synthesis.nextSteps) {
      output += `**Next Steps:**\n${result.synthesis.nextSteps.map(s => `- ${s}`).join('\n')}\n\n`;
    }
    
    return output;
  }
}
