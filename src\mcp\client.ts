import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import {
  MCPClientInterface,
  MCPClientConfig,
  MCPToolResult,
  MCPResourceResult,
  MCPPromptResult,
  MCPEvent,
  MCPEventListener,
  MCPEventType
} from './types.js';

/**
 * AG3NTIC MCP Client implementation
 */
export class AG3NTICMCPClient implements MCPClientInterface {
  public readonly client: Client;
  public readonly config: MCPClientConfig;
  private transport: any;
  private connected = false;
  private eventListeners = new Map<MCPEventType, Set<MCPEventListener>>();

  constructor(config: MCPClientConfig) {
    this.config = config;
    
    // Initialize MCP client
    this.client = new Client({
      name: config.name,
      version: config.version
    });

    this.setupTransport();
  }

  /**
   * Setup transport based on configuration
   */
  private setupTransport(): void {
    const { type, options = {} } = this.config.transport;

    switch (type) {
      case 'stdio':
        if (!options.command) {
          throw new Error('Stdio transport requires command in options');
        }
        this.transport = new StdioClientTransport({
          command: options.command,
          args: options.args,
          env: options.env
        });
        break;

      case 'streamable-http':
        if (!options.url) {
          throw new Error('Streamable HTTP transport requires URL in options');
        }
        this.transport = new StreamableHTTPClientTransport(new URL(options.url));
        break;

      case 'sse':
        if (!options.url) {
          throw new Error('SSE transport requires URL in options');
        }
        this.transport = new SSEClientTransport(new URL(options.url));
        break;

      case 'http':
        throw new Error('HTTP transport not yet implemented');

      default:
        throw new Error(`Unsupported transport type: ${type}`);
    }
  }

  /**
   * Connect to the MCP server
   */
  async connect(): Promise<void> {
    if (this.connected) {
      throw new Error('Client is already connected');
    }

    try {
      await this.client.connect(this.transport);
      this.connected = true;
      this.emitEvent('client_connected', { config: this.config });
    } catch (error) {
      this.emitEvent('error', { 
        type: 'client_connect_error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Disconnect from the MCP server
   */
  async disconnect(): Promise<void> {
    if (!this.connected) {
      return;
    }

    try {
      // Close transport if it has a close method
      if (this.transport && typeof this.transport.close === 'function') {
        await this.transport.close();
      }
      
      this.connected = false;
      this.emitEvent('client_disconnected', { config: this.config });
    } catch (error) {
      this.emitEvent('error', { 
        type: 'client_disconnect_error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Check if connected to server
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * List available tools from the server
   */
  async listTools(): Promise<Array<{ name: string; description: string; inputSchema: any }>> {
    if (!this.connected) {
      throw new Error('Client is not connected');
    }

    try {
      const response = await this.client.listTools();
      return response.tools.map(tool => ({
        name: tool.name,
        description: tool.description || '',
        inputSchema: tool.inputSchema
      }));
    } catch (error) {
      this.emitEvent('error', { 
        type: 'list_tools_error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Call a tool on the server
   */
  async callTool(name: string, args: any): Promise<MCPToolResult> {
    if (!this.connected) {
      throw new Error('Client is not connected');
    }

    try {
      this.emitEvent('tool_called', { toolName: name, args });
      const response = await this.client.callTool({
        name,
        arguments: args
      });

      return {
        content: response.content,
        isError: response.isError
      };
    } catch (error) {
      this.emitEvent('error', { 
        type: 'tool_call_error', 
        toolName: name,
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * List available resources from the server
   */
  async listResources(): Promise<Array<{ uri: string; name: string; description: string; mimeType?: string }>> {
    if (!this.connected) {
      throw new Error('Client is not connected');
    }

    try {
      const response = await this.client.listResources();
      return response.resources.map(resource => ({
        uri: resource.uri,
        name: resource.name || '',
        description: resource.description || '',
        mimeType: resource.mimeType
      }));
    } catch (error) {
      this.emitEvent('error', { 
        type: 'list_resources_error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Read a resource from the server
   */
  async readResource(uri: string): Promise<MCPResourceResult> {
    if (!this.connected) {
      throw new Error('Client is not connected');
    }

    try {
      this.emitEvent('resource_accessed', { uri });
      const response = await this.client.readResource({ uri });
      
      return {
        contents: response.contents
      };
    } catch (error) {
      this.emitEvent('error', { 
        type: 'resource_read_error', 
        uri,
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * List available prompts from the server
   */
  async listPrompts(): Promise<Array<{ name: string; description: string; argsSchema?: any }>> {
    if (!this.connected) {
      throw new Error('Client is not connected');
    }

    try {
      const response = await this.client.listPrompts();
      return response.prompts.map(prompt => ({
        name: prompt.name,
        description: prompt.description || '',
        argsSchema: prompt.arguments
      }));
    } catch (error) {
      this.emitEvent('error', { 
        type: 'list_prompts_error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Get a prompt from the server
   */
  async getPrompt(name: string, args?: any): Promise<MCPPromptResult> {
    if (!this.connected) {
      throw new Error('Client is not connected');
    }

    try {
      this.emitEvent('prompt_requested', { promptName: name, args });
      const response = await this.client.getPrompt({
        name,
        arguments: args
      });

      return {
        description: response.description,
        messages: response.messages
      };
    } catch (error) {
      this.emitEvent('error', { 
        type: 'prompt_get_error', 
        promptName: name,
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Add event listener
   */
  addEventListener(type: MCPEventType, listener: MCPEventListener): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(type: MCPEventType, listener: MCPEventListener): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * Emit an event to all listeners
   */
  private emitEvent(type: MCPEventType, data: any): void {
    const event: MCPEvent = {
      type,
      timestamp: new Date(),
      data
    };

    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          const result = listener(event);
          if (result instanceof Promise) {
            result.catch(error => {
              console.error(`Error in event listener for ${type}:`, error);
            });
          }
        } catch (error) {
          console.error(`Error in event listener for ${type}:`, error);
        }
      });
    }
  }

  /**
   * Get client statistics
   */
  getStats(): {
    connected: boolean;
    connectionTime: number;
    toolCallCount: number;
    resourceAccessCount: number;
    promptRequestCount: number;
  } {
    // Note: We'd need to track these statistics ourselves
    return {
      connected: this.connected,
      connectionTime: 0, // Would need to track this
      toolCallCount: 0, // Would need to track this
      resourceAccessCount: 0, // Would need to track this
      promptRequestCount: 0 // Would need to track this
    };
  }
}

/**
 * Create an MCP client with default configuration
 */
export function createMCPClient(
  name: string,
  command: string,
  args: string[] = [],
  version: string = '1.0.0'
): AG3NTICMCPClient {
  const config: MCPClientConfig = {
    name,
    version,
    transport: {
      type: 'stdio',
      options: {
        command,
        args
      }
    },
    timeout: 30000,
    retry: {
      maxAttempts: 3,
      delay: 1000
    }
  };

  return new AG3NTICMCPClient(config);
}

/**
 * Create an HTTP MCP client for web integration
 */
export function createHTTPMCPClient(
  name: string,
  url: string,
  version: string = '1.0.0'
): AG3NTICMCPClient {
  const config: MCPClientConfig = {
    name,
    version,
    transport: {
      type: 'streamable-http',
      options: {
        url
      }
    },
    timeout: 30000,
    retry: {
      maxAttempts: 3,
      delay: 1000
    }
  };

  return new AG3NTICMCPClient(config);
}
