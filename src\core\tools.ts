import { z } from 'zod';
import { AgentState } from './types.js';

/**
 * Tool execution context
 */
export interface ToolContext<TState extends AgentState = AgentState> {
  state: TState;
  agentName: string;
  threadId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

/**
 * Tool execution result
 */
export interface ToolResult {
  success: boolean;
  output: string;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Tool approval status
 */
export type ToolApprovalStatus = 'approved' | 'denied' | 'pending';

/**
 * Tool configuration interface
 */
export interface ToolConfig<TParams = any> {
  name: string;
  description: string;
  parameters: z.ZodSchema<TParams> | object;
  needsApproval?: boolean;
  timeout?: number;
  retries?: number;
  rateLimit?: {
    requests: number;
    window: number; // milliseconds
  };
}

/**
 * Tool execution function
 */
export type ToolExecuteFunction<TParams = any, TState extends AgentState = AgentState> = (
  params: TParams,
  context: ToolContext<TState>
) => Promise<string> | string;

/**
 * Tool approval function
 */
export type ToolApprovalFunction<TParams = any> = (
  params: TParams,
  context: ToolContext
) => Promise<ToolApprovalStatus> | ToolApprovalStatus;

/**
 * Ultra-high-performance Tool implementation
 * 
 * Features:
 * - Zod-based parameter validation
 * - Approval workflows
 * - Rate limiting and timeouts
 * - Streaming execution support
 * - Error handling and retries
 * - Performance monitoring
 */
export class Tool<TParams = any, TState extends AgentState = AgentState> {
  private readonly config: Required<ToolConfig<TParams>>;
  private readonly executeFunction: ToolExecuteFunction<TParams, TState>;
  private readonly approvalFunction: ToolApprovalFunction<TParams> | undefined;
  private readonly rateLimitMap = new Map<string, number[]>();

  constructor(
    config: ToolConfig<TParams>,
    executeFunction: ToolExecuteFunction<TParams, TState>,
    approvalFunction?: ToolApprovalFunction<TParams>
  ) {
    this.config = {
      needsApproval: false,
      timeout: 30000, // 30 seconds
      retries: 3,
      rateLimit: { requests: 100, window: 60000 }, // 100 requests per minute
      ...config,
    };
    this.executeFunction = executeFunction;
    this.approvalFunction = approvalFunction;
  }

  /**
   * Execute tool with full validation and error handling
   */
  async execute(
    params: unknown,
    context: ToolContext<TState>
  ): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      // Rate limiting check
      if (!this.checkRateLimit(context.userId || context.agentName)) {
        return {
          success: false,
          output: '',
          error: 'Rate limit exceeded',
        };
      }

      // Parameter validation
      const validatedParams = this.validateParameters(params);
      if (!validatedParams.success) {
        return {
          success: false,
          output: '',
          error: `Parameter validation failed: ${validatedParams.error}`,
        };
      }

      // Approval check
      if (this.config.needsApproval && this.approvalFunction) {
        const approval = await this.approvalFunction(validatedParams.data, context);
        if (approval !== 'approved') {
          return {
            success: false,
            output: '',
            error: `Tool execution ${approval}`,
          };
        }
      }

      // Execute with timeout and retries
      const result = await this.executeWithRetries(validatedParams.data, context);

      return {
        success: true,
        output: result,
        metadata: {
          executionTime: Date.now() - startTime,
          retries: 0, // Would track actual retries
        },
      };

    } catch (error) {
      return {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          executionTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Validate parameters using Zod schema
   */
  private validateParameters(params: unknown): {
    success: boolean;
    data?: any;
    error?: string;
  } {
    try {
      if (this.config.parameters && typeof this.config.parameters === 'object' && 'safeParse' in this.config.parameters) {
        const result = (this.config.parameters as z.ZodSchema).safeParse(params);
        if (result.success) {
          return { success: true, data: result.data };
        } else {
          return {
            success: false,
            error: result.error.issues.map((e: any) => e.message).join(', '),
          };
        }
      } else {
        // Basic validation for non-Zod schemas
        return { success: true, data: params };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Validation error',
      };
    }
  }

  /**
   * Execute with timeout and retry logic
   */
  private async executeWithRetries(
    params: any,
    context: ToolContext<TState>
  ): Promise<string> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.config.retries; attempt++) {
      try {
        const result = await Promise.race([
          this.executeFunction(params, context),
          this.createTimeoutPromise(),
        ]);

        return typeof result === 'string' ? result : String(result);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Don't retry on the last attempt
        if (attempt === this.config.retries) {
          break;
        }

        // Exponential backoff
        await this.delay(Math.pow(2, attempt) * 1000);
      }
    }

    throw lastError || new Error('Tool execution failed');
  }

  /**
   * Create timeout promise
   */
  private createTimeoutPromise(): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Tool execution timed out after ${this.config.timeout}ms`));
      }, this.config.timeout);
    });
  }

  /**
   * Check rate limiting
   */
  private checkRateLimit(identifier: string): boolean {
    const now = Date.now();
    const windowStart = now - this.config.rateLimit.window;
    
    // Get existing requests for this identifier
    const requests = this.rateLimitMap.get(identifier) || [];
    
    // Filter out old requests
    const recentRequests = requests.filter(time => time > windowStart);
    
    // Check if under limit
    if (recentRequests.length >= this.config.rateLimit.requests) {
      return false;
    }

    // Add current request
    recentRequests.push(now);
    this.rateLimitMap.set(identifier, recentRequests);
    
    return true;
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Getters
  get name(): string {
    return this.config.name;
  }

  get description(): string {
    return this.config.description;
  }

  get parameters(): z.ZodSchema | object {
    return this.config.parameters;
  }

  get needsApproval(): boolean {
    return this.config.needsApproval;
  }
}

/**
 * Tool registry for dynamic loading and management
 */
export class ToolRegistry {
  private readonly tools = new Map<string, Tool>();
  private readonly categories = new Map<string, Set<string>>();

  /**
   * Register a tool
   */
  register(tool: Tool, category?: string): void {
    this.tools.set(tool.name, tool);
    
    if (category) {
      if (!this.categories.has(category)) {
        this.categories.set(category, new Set());
      }
      this.categories.get(category)!.add(tool.name);
    }
  }

  /**
   * Get tool by name
   */
  get(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  /**
   * Get tools by category
   */
  getByCategory(category: string): Tool[] {
    const toolNames = this.categories.get(category);
    if (!toolNames) return [];
    
    return Array.from(toolNames)
      .map(name => this.tools.get(name))
      .filter((tool): tool is Tool => tool !== undefined);
  }

  /**
   * List all tools
   */
  list(): Tool[] {
    return Array.from(this.tools.values());
  }

  /**
   * Remove tool
   */
  unregister(name: string): boolean {
    const removed = this.tools.delete(name);
    
    // Remove from categories
    for (const [category, toolNames] of this.categories) {
      toolNames.delete(name);
      if (toolNames.size === 0) {
        this.categories.delete(category);
      }
    }
    
    return removed;
  }
}

/**
 * Tool map type for performance-optimized lookups
 */
export type ToolMap = Map<string, Tool>;

/**
 * Utility function to create a tool with Zod validation
 */
export function tool<TParams = any, TState extends AgentState = AgentState>(
  config: ToolConfig<TParams>,
  executeFunction: ToolExecuteFunction<TParams, TState>,
  approvalFunction?: ToolApprovalFunction<TParams>
): Tool<TParams, TState> {
  return new Tool(config, executeFunction, approvalFunction);
}

/**
 * Create a tool map from an array of tools
 */
export function createToolMap(tools: Tool[]): ToolMap {
  const map = new Map<string, Tool>();
  tools.forEach(tool => map.set(tool.name, tool));
  return map;
}

/**
 * Global tool registry instance
 */
export const globalToolRegistry = new ToolRegistry();
