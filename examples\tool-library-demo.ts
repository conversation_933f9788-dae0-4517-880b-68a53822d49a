/**
 * AG3NTIC Tool Library Demo
 * 
 * This demonstrates the new tool library functionality
 * that has been added to AG3NTIC framework.
 */

import { z } from 'zod';

// Simple tool types for demonstration
interface ToolConfig {
  name: string;
  title: string;
  description: string;
  category?: string;
}

interface ToolExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  content?: Array<{
    type: 'text' | 'image';
    text?: string;
    uri?: string;
  }>;
}

interface Tool<TInput = any> {
  config: ToolConfig;
  inputSchema: z.ZodSchema<TInput>;
  execute(input: TInput): Promise<ToolExecutionResult>;
}

// Simple tool implementation
class SimpleTool<TInput = any> implements Tool<TInput> {
  constructor(
    public config: ToolConfig,
    public inputSchema: z.ZodSchema<TInput>,
    private handler: (input: TInput) => Promise<ToolExecutionResult>
  ) {}

  async execute(input: TInput): Promise<ToolExecutionResult> {
    return this.handler(input);
  }
}

// Tool registry
class ToolRegistry {
  private tools = new Map<string, Tool>();

  register(tool: Tool): void {
    this.tools.set(tool.config.name, tool);
  }

  get(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  list(): Tool[] {
    return Array.from(this.tools.values());
  }

  async execute(name: string, input: any): Promise<ToolExecutionResult> {
    const tool = this.get(name);
    if (!tool) {
      return {
        success: false,
        error: `Tool '${name}' not found`
      };
    }

    try {
      return await tool.execute(input);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Demo function
async function demonstrateToolLibrary() {
  console.log('🔧 AG3NTIC Tool Library Demo\n');

  // Create tool registry
  const registry = new ToolRegistry();

  // Create a simple text processing tool
  const textTool = new SimpleTool(
    {
      name: 'text_transform',
      title: 'Text Transform',
      description: 'Transform text to uppercase',
      category: 'text'
    },
    z.object({
      text: z.string(),
      operation: z.enum(['uppercase', 'lowercase', 'reverse'])
    }),
    async (input) => {
      let result: string;
      
      switch (input.operation) {
        case 'uppercase':
          result = input.text.toUpperCase();
          break;
        case 'lowercase':
          result = input.text.toLowerCase();
          break;
        case 'reverse':
          result = input.text.split('').reverse().join('');
          break;
        default:
          throw new Error('Unknown operation');
      }

      return {
        success: true,
        data: { result },
        content: [{
          type: 'text',
          text: `Transformed "${input.text}" to "${result}"`
        }]
      };
    }
  );

  // Create a math tool
  const mathTool = new SimpleTool(
    {
      name: 'calculator',
      title: 'Calculator',
      description: 'Perform basic math operations',
      category: 'math'
    },
    z.object({
      a: z.number(),
      b: z.number(),
      operation: z.enum(['add', 'subtract', 'multiply', 'divide'])
    }),
    async (input) => {
      let result: number;
      
      switch (input.operation) {
        case 'add':
          result = input.a + input.b;
          break;
        case 'subtract':
          result = input.a - input.b;
          break;
        case 'multiply':
          result = input.a * input.b;
          break;
        case 'divide':
          if (input.b === 0) {
            throw new Error('Division by zero');
          }
          result = input.a / input.b;
          break;
        default:
          throw new Error('Unknown operation');
      }

      return {
        success: true,
        data: { result },
        content: [{
          type: 'text',
          text: `${input.a} ${input.operation} ${input.b} = ${result}`
        }]
      };
    }
  );

  // Register tools
  registry.register(textTool);
  registry.register(mathTool);

  console.log('📋 Registered Tools:');
  registry.list().forEach(tool => {
    console.log(`  • ${tool.config.title} (${tool.config.name}): ${tool.config.description}`);
  });
  console.log();

  // Execute text tool
  console.log('🔤 Testing Text Transform Tool:');
  const textResult = await registry.execute('text_transform', {
    text: 'Hello World',
    operation: 'uppercase'
  });
  
  if (textResult.success) {
    console.log(`  ✅ ${textResult.content?.[0]?.text}`);
  } else {
    console.log(`  ❌ Error: ${textResult.error}`);
  }

  // Execute math tool
  console.log('\n🧮 Testing Calculator Tool:');
  const mathResult = await registry.execute('calculator', {
    a: 15,
    b: 3,
    operation: 'multiply'
  });
  
  if (mathResult.success) {
    console.log(`  ✅ ${mathResult.content?.[0]?.text}`);
  } else {
    console.log(`  ❌ Error: ${mathResult.error}`);
  }

  // Test error handling
  console.log('\n⚠️  Testing Error Handling:');
  const errorResult = await registry.execute('calculator', {
    a: 10,
    b: 0,
    operation: 'divide'
  });
  
  if (!errorResult.success) {
    console.log(`  ✅ Correctly caught error: ${errorResult.error}`);
  }

  console.log('\n🎉 Tool Library Demo Complete!');
  console.log('\nKey Features Demonstrated:');
  console.log('  • Tool registration and discovery');
  console.log('  • Input validation with Zod schemas');
  console.log('  • Structured execution results');
  console.log('  • Error handling and reporting');
  console.log('  • Extensible tool architecture');
}

// Run the demo
if (require.main === module) {
  demonstrateToolLibrary().catch(console.error);
}

export { demonstrateToolLibrary };
