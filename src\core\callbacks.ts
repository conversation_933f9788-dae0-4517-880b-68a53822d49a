/**
 * Enhanced Callback and Event System for AG3NTIC
 * 
 * Provides comprehensive monitoring, logging, and event streaming
 * capabilities inspired by <PERSON><PERSON><PERSON><PERSON>'s callback system.
 */

import { RunnableConfig, RunInfo } from './runnable.js';

/**
 * Event types for the AG3NTIC system
 */
export type EventType = 
  | 'on_start'
  | 'on_end' 
  | 'on_error'
  | 'on_stream'
  | 'on_retry'
  | 'on_tool_start'
  | 'on_tool_end'
  | 'on_tool_error'
  | 'on_agent_start'
  | 'on_agent_end'
  | 'on_agent_error'
  | 'on_graph_start'
  | 'on_graph_end'
  | 'on_graph_error'
  | 'on_memory_read'
  | 'on_memory_write'
  | 'on_checkpoint_save'
  | 'on_checkpoint_load'
  | 'on_human_input_required'
  | 'on_human_input_received';

/**
 * Event data structure
 */
export interface Event {
  type: EventType;
  timestamp: number;
  runId: string;
  parentRunId?: string;
  name: string;
  data: any;
  tags: string[];
  metadata: Record<string, any>;
}

/**
 * Enhanced callback handler interface
 */
export interface CallbackHandler {
  /** Called when any operation starts */
  onStart?(run: RunInfo): void | Promise<void>;
  
  /** Called when any operation ends successfully */
  onEnd?(run: RunInfo): void | Promise<void>;
  
  /** Called when any operation encounters an error */
  onError?(run: RunInfo, error: Error): void | Promise<void>;
  
  /** Called when streaming data is available */
  onStream?(run: RunInfo, chunk: any): void | Promise<void>;
  
  /** Called when an operation is retried */
  onRetry?(run: RunInfo, attempt: number): void | Promise<void>;
  
  /** Called when a tool execution starts */
  onToolStart?(run: RunInfo): void | Promise<void>;
  
  /** Called when a tool execution ends */
  onToolEnd?(run: RunInfo): void | Promise<void>;
  
  /** Called when a tool execution errors */
  onToolError?(run: RunInfo, error: Error): void | Promise<void>;
  
  /** Called when an agent starts processing */
  onAgentStart?(run: RunInfo): void | Promise<void>;
  
  /** Called when an agent finishes processing */
  onAgentEnd?(run: RunInfo): void | Promise<void>;
  
  /** Called when an agent encounters an error */
  onAgentError?(run: RunInfo, error: Error): void | Promise<void>;
  
  /** Called when a graph execution starts */
  onGraphStart?(run: RunInfo): void | Promise<void>;
  
  /** Called when a graph execution ends */
  onGraphEnd?(run: RunInfo): void | Promise<void>;
  
  /** Called when a graph execution errors */
  onGraphError?(run: RunInfo, error: Error): void | Promise<void>;
  
  /** Called when memory is read */
  onMemoryRead?(run: RunInfo, key: string, value: any): void | Promise<void>;
  
  /** Called when memory is written */
  onMemoryWrite?(run: RunInfo, key: string, value: any): void | Promise<void>;
  
  /** Called when a checkpoint is saved */
  onCheckpointSave?(run: RunInfo, checkpointId: string): void | Promise<void>;
  
  /** Called when a checkpoint is loaded */
  onCheckpointLoad?(run: RunInfo, checkpointId: string): void | Promise<void>;
  
  /** Called when human input is required */
  onHumanInputRequired?(run: RunInfo, prompt: string): void | Promise<void>;
  
  /** Called when human input is received */
  onHumanInputReceived?(run: RunInfo, input: any): void | Promise<void>;
  
  /** Generic event handler for custom events */
  onEvent?(event: Event): void | Promise<void>;
}

/**
 * Console logging callback handler
 */
export class ConsoleCallbackHandler implements CallbackHandler {
  private verbose: boolean;
  private colors: boolean;

  constructor(options: { verbose?: boolean; colors?: boolean } = {}) {
    this.verbose = options.verbose ?? true;
    this.colors = options.colors ?? true;
  }

  onStart(run: RunInfo): void {
    if (this.verbose) {
      const color = this.colors ? '\x1b[32m' : '';
      const reset = this.colors ? '\x1b[0m' : '';
      console.log(`${color}▶ Starting ${run.name} (${run.runId})${reset}`);
    }
  }

  onEnd(run: RunInfo): void {
    if (this.verbose) {
      const duration = run.endTime ? run.endTime - run.startTime : 0;
      const color = this.colors ? '\x1b[32m' : '';
      const reset = this.colors ? '\x1b[0m' : '';
      console.log(`${color}✓ Completed ${run.name} in ${duration}ms${reset}`);
    }
  }

  onError(run: RunInfo, error: Error): void {
    const color = this.colors ? '\x1b[31m' : '';
    const reset = this.colors ? '\x1b[0m' : '';
    console.error(`${color}✗ Error in ${run.name}: ${error.message}${reset}`);
  }

  onStream(run: RunInfo, chunk: any): void {
    if (this.verbose) {
      const color = this.colors ? '\x1b[36m' : '';
      const reset = this.colors ? '\x1b[0m' : '';
      console.log(`${color}📡 Stream from ${run.name}:${reset}`, chunk);
    }
  }
}

/**
 * File logging callback handler
 */
export class FileCallbackHandler implements CallbackHandler {
  // private _logFile: string;
  private writeStream?: any;

  constructor(_logFile: string) {
    // this._logFile = logFile;
    this.initializeStream();
  }

  private initializeStream(): void {
    // In a real implementation, you'd use fs.createWriteStream
    // For now, we'll simulate it
    this.writeStream = {
      write: (data: string) => {
        // Simulate file writing
        console.log(`[LOG] ${data}`);
      }
    };
  }

  onStart(run: RunInfo): void {
    this.writeLog('START', run);
  }

  onEnd(run: RunInfo): void {
    this.writeLog('END', run);
  }

  onError(run: RunInfo, error: Error): void {
    this.writeLog('ERROR', run, { error: error.message, stack: error.stack });
  }

  private writeLog(level: string, run: RunInfo, extra?: any): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      runId: run.runId,
      name: run.name,
      type: run.type,
      tags: run.tags,
      metadata: run.metadata,
      ...extra
    };

    this.writeStream?.write(JSON.stringify(logEntry) + '\n');
  }
}

/**
 * Metrics collection callback handler
 */
export class MetricsCallbackHandler implements CallbackHandler {
  private metrics: Map<string, {
    count: number;
    totalDuration: number;
    errors: number;
    lastRun: number;
  }> = new Map();

  onStart(run: RunInfo): void {
    // Initialize metrics if not exists
    if (!this.metrics.has(run.name)) {
      this.metrics.set(run.name, {
        count: 0,
        totalDuration: 0,
        errors: 0,
        lastRun: 0
      });
    }
  }

  onEnd(run: RunInfo): void {
    const metrics = this.metrics.get(run.name);
    if (metrics && run.endTime) {
      metrics.count++;
      metrics.totalDuration += run.endTime - run.startTime;
      metrics.lastRun = run.endTime;
    }
  }

  onError(run: RunInfo, _error: Error): void {
    const metrics = this.metrics.get(run.name);
    if (metrics) {
      metrics.errors++;
    }
  }

  getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [name, metrics] of this.metrics.entries()) {
      result[name] = {
        ...metrics,
        averageDuration: metrics.count > 0 ? metrics.totalDuration / metrics.count : 0,
        errorRate: metrics.count > 0 ? metrics.errors / metrics.count : 0
      };
    }
    
    return result;
  }

  reset(): void {
    this.metrics.clear();
  }
}

/**
 * Event streaming callback handler
 */
export class EventStreamCallbackHandler implements CallbackHandler {
  private eventStream: AsyncIterable<Event>;
  private eventQueue: Event[] = [];
  private listeners: ((event: Event) => void)[] = [];

  constructor() {
    this.eventStream = this.createEventStream();
  }

  private async *createEventStream(): AsyncGenerator<Event, void, unknown> {
    while (true) {
      if (this.eventQueue.length > 0) {
        const event = this.eventQueue.shift()!;
        yield event;
      } else {
        // Wait a bit before checking again
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
  }

  private emitEvent(type: EventType, run: RunInfo, data?: any): void {
    const event: Event = {
      type,
      timestamp: Date.now(),
      runId: run.runId,
      parentRunId: run.parentRunId,
      name: run.name,
      data: data || run.output || run.input,
      tags: run.tags,
      metadata: run.metadata
    };

    this.eventQueue.push(event);
    this.listeners.forEach(listener => listener(event));
  }

  onStart(run: RunInfo): void {
    this.emitEvent('on_start', run);
  }

  onEnd(run: RunInfo): void {
    this.emitEvent('on_end', run);
  }

  onError(run: RunInfo, error: Error): void {
    this.emitEvent('on_error', run, { error: error.message });
  }

  onStream(run: RunInfo, chunk: any): void {
    this.emitEvent('on_stream', run, chunk);
  }

  onToolStart(run: RunInfo): void {
    this.emitEvent('on_tool_start', run);
  }

  onToolEnd(run: RunInfo): void {
    this.emitEvent('on_tool_end', run);
  }

  onToolError(run: RunInfo, error: Error): void {
    this.emitEvent('on_tool_error', run, { error: error.message });
  }

  addEventListener(listener: (event: Event) => void): void {
    this.listeners.push(listener);
  }

  removeEventListener(listener: (event: Event) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  getEventStream(): AsyncIterable<Event> {
    return this.eventStream;
  }
}

/**
 * Callback manager for handling multiple callbacks
 */
export class CallbackManager {
  private handlers: CallbackHandler[] = [];

  addHandler(handler: CallbackHandler): void {
    this.handlers.push(handler);
  }

  removeHandler(handler: CallbackHandler): void {
    const index = this.handlers.indexOf(handler);
    if (index > -1) {
      this.handlers.splice(index, 1);
    }
  }

  async callHandlers<K extends keyof CallbackHandler>(
    method: K,
    ...args: Parameters<NonNullable<CallbackHandler[K]>>
  ): Promise<void> {
    await Promise.all(
      this.handlers.map(async (handler) => {
        try {
          const fn = handler[method];
          if (fn) {
            await (fn as any).apply(handler, args);
          }
        } catch (error) {
          console.error(`Error in callback handler ${method}:`, error);
        }
      })
    );
  }

  getHandlers(): CallbackHandler[] {
    return [...this.handlers];
  }

  clear(): void {
    this.handlers = [];
  }
}

/**
 * Global callback manager instance
 */
export const globalCallbackManager = new CallbackManager();

/**
 * Utility functions for common callback setups
 */
export const CallbackUtils = {
  /**
   * Create a console callback handler
   */
  console(options?: { verbose?: boolean; colors?: boolean }): ConsoleCallbackHandler {
    return new ConsoleCallbackHandler(options);
  },

  /**
   * Create a file callback handler
   */
  file(logFile: string): FileCallbackHandler {
    return new FileCallbackHandler(logFile);
  },

  /**
   * Create a metrics callback handler
   */
  metrics(): MetricsCallbackHandler {
    return new MetricsCallbackHandler();
  },

  /**
   * Create an event stream callback handler
   */
  eventStream(): EventStreamCallbackHandler {
    return new EventStreamCallbackHandler();
  },

  /**
   * Create a configuration with common callbacks
   */
  createConfig(options: {
    console?: boolean | { verbose?: boolean; colors?: boolean };
    file?: string;
    metrics?: boolean;
    eventStream?: boolean;
    custom?: CallbackHandler[];
  }): RunnableConfig {
    const callbacks: CallbackHandler[] = [];

    if (options.console) {
      const consoleOptions = typeof options.console === 'object' ? options.console : {};
      callbacks.push(new ConsoleCallbackHandler(consoleOptions));
    }

    if (options.file) {
      callbacks.push(new FileCallbackHandler(options.file));
    }

    if (options.metrics) {
      callbacks.push(new MetricsCallbackHandler());
    }

    if (options.eventStream) {
      callbacks.push(new EventStreamCallbackHandler());
    }

    if (options.custom) {
      callbacks.push(...options.custom);
    }

    return { callbacks };
  }
};
