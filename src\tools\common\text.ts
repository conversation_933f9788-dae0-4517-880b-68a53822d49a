import { z } from 'zod';
import { BaseTool } from '../base.js';
import { ToolConfig, ToolContext, ToolExecutionResult, ToolCategory } from '../types.js';

/**
 * Text analysis tool input schema
 */
const TextAnalysisInputSchema = z.object({
  text: z.string().min(1, 'Text is required'),
  includeWordCount: z.boolean().optional().default(true),
  includeCharCount: z.boolean().optional().default(true),
  includeSentenceCount: z.boolean().optional().default(true),
  includeParagraphCount: z.boolean().optional().default(true),
  includeReadingTime: z.boolean().optional().default(true),
  wordsPerMinute: z.number().min(100).max(500).optional().default(200)
});

type TextAnalysisInput = z.infer<typeof TextAnalysisInputSchema>;

/**
 * Text analysis tool
 */
export class TextAnalysisTool extends BaseTool<TextAnalysisInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'text_analysis',
      title: 'Text Analysis',
      description: 'Analyze text for word count, reading time, and other metrics',
      category: ToolCategory.TEXT,
      tags: ['text', 'analysis', 'count', 'metrics'],
      examples: [
        {
          description: 'Analyze a paragraph',
          input: { text: 'This is a sample paragraph for analysis. It contains multiple sentences.' }
        },
        {
          description: 'Custom reading speed',
          input: { 
            text: 'Long text content here...',
            wordsPerMinute: 250
          }
        }
      ]
    };

    super(config, TextAnalysisInputSchema);
  }

  async execute(input: TextAnalysisInput, context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      const analysis: any = {};

      if (input.includeCharCount) {
        analysis.characterCount = input.text.length;
        analysis.characterCountNoSpaces = input.text.replace(/\s/g, '').length;
      }

      if (input.includeWordCount) {
        const words = input.text.trim().split(/\s+/).filter(word => word.length > 0);
        analysis.wordCount = words.length;
        analysis.averageWordLength = words.length > 0 
          ? words.reduce((sum, word) => sum + word.length, 0) / words.length 
          : 0;
      }

      if (input.includeSentenceCount) {
        const sentences = input.text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        analysis.sentenceCount = sentences.length;
        analysis.averageSentenceLength = analysis.wordCount && sentences.length > 0
          ? analysis.wordCount / sentences.length
          : 0;
      }

      if (input.includeParagraphCount) {
        const paragraphs = input.text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
        analysis.paragraphCount = paragraphs.length;
      }

      if (input.includeReadingTime && analysis.wordCount) {
        const readingTimeMinutes = analysis.wordCount / input.wordsPerMinute;
        analysis.readingTime = {
          minutes: Math.ceil(readingTimeMinutes),
          seconds: Math.ceil(readingTimeMinutes * 60),
          wordsPerMinute: input.wordsPerMinute
        };
      }

      return this.createSuccessResult(analysis, [
        {
          type: 'text',
          text: this.formatAnalysisResult(analysis)
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `Text analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private formatAnalysisResult(analysis: any): string {
    const lines = ['📊 Text Analysis Results:'];
    
    if (analysis.characterCount !== undefined) {
      lines.push(`• Characters: ${analysis.characterCount} (${analysis.characterCountNoSpaces} without spaces)`);
    }
    
    if (analysis.wordCount !== undefined) {
      lines.push(`• Words: ${analysis.wordCount}`);
      lines.push(`• Average word length: ${analysis.averageWordLength.toFixed(1)} characters`);
    }
    
    if (analysis.sentenceCount !== undefined) {
      lines.push(`• Sentences: ${analysis.sentenceCount}`);
      lines.push(`• Average sentence length: ${analysis.averageSentenceLength.toFixed(1)} words`);
    }
    
    if (analysis.paragraphCount !== undefined) {
      lines.push(`• Paragraphs: ${analysis.paragraphCount}`);
    }
    
    if (analysis.readingTime) {
      lines.push(`• Reading time: ${analysis.readingTime.minutes} minutes (${analysis.readingTime.seconds} seconds)`);
      lines.push(`• Based on ${analysis.readingTime.wordsPerMinute} words per minute`);
    }

    return lines.join('\n');
  }
}

/**
 * Text transformation tool input schema
 */
const TextTransformInputSchema = z.object({
  text: z.string().min(1, 'Text is required'),
  operation: z.enum([
    'uppercase',
    'lowercase',
    'title_case',
    'sentence_case',
    'camel_case',
    'snake_case',
    'kebab_case',
    'reverse',
    'remove_whitespace',
    'normalize_whitespace',
    'remove_punctuation',
    'extract_emails',
    'extract_urls',
    'extract_numbers'
  ]),
  preserveFormatting: z.boolean().optional().default(false)
});

type TextTransformInput = z.infer<typeof TextTransformInputSchema>;

/**
 * Text transformation tool
 */
export class TextTransformTool extends BaseTool<TextTransformInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'text_transform',
      title: 'Text Transform',
      description: 'Transform text using various operations like case changes, formatting, and extraction',
      category: ToolCategory.TEXT,
      tags: ['text', 'transform', 'format', 'case', 'extract'],
      examples: [
        {
          description: 'Convert to uppercase',
          input: { text: 'hello world', operation: 'uppercase' }
        },
        {
          description: 'Extract emails',
          input: { text: 'Contact <NAME_EMAIL> or <EMAIL>', operation: 'extract_emails' }
        }
      ]
    };

    super(config, TextTransformInputSchema);
  }

  async execute(input: TextTransformInput, context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      let result: string | string[];

      switch (input.operation) {
        case 'uppercase':
          result = input.text.toUpperCase();
          break;
        
        case 'lowercase':
          result = input.text.toLowerCase();
          break;
        
        case 'title_case':
          result = input.text.replace(/\w\S*/g, (txt) => 
            txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
          );
          break;
        
        case 'sentence_case':
          result = input.text.charAt(0).toUpperCase() + input.text.slice(1).toLowerCase();
          break;
        
        case 'camel_case':
          result = input.text
            .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
              index === 0 ? word.toLowerCase() : word.toUpperCase()
            )
            .replace(/\s+/g, '');
          break;
        
        case 'snake_case':
          result = input.text
            .replace(/\W+/g, ' ')
            .split(/ |\B(?=[A-Z])/)
            .map(word => word.toLowerCase())
            .join('_');
          break;
        
        case 'kebab_case':
          result = input.text
            .replace(/\W+/g, ' ')
            .split(/ |\B(?=[A-Z])/)
            .map(word => word.toLowerCase())
            .join('-');
          break;
        
        case 'reverse':
          result = input.text.split('').reverse().join('');
          break;
        
        case 'remove_whitespace':
          result = input.text.replace(/\s/g, '');
          break;
        
        case 'normalize_whitespace':
          result = input.text.replace(/\s+/g, ' ').trim();
          break;
        
        case 'remove_punctuation':
          result = input.text.replace(/[^\w\s]/g, '');
          break;
        
        case 'extract_emails':
          const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
          result = input.text.match(emailRegex) || [];
          break;
        
        case 'extract_urls':
          const urlRegex = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g;
          result = input.text.match(urlRegex) || [];
          break;
        
        case 'extract_numbers':
          const numberRegex = /\b\d+(?:\.\d+)?\b/g;
          result = input.text.match(numberRegex) || [];
          break;
        
        default:
          return this.createErrorResult(`Unknown operation: ${input.operation}`);
      }

      const transformResult = {
        originalText: input.text,
        operation: input.operation,
        result,
        resultType: Array.isArray(result) ? 'array' : 'string',
        resultCount: Array.isArray(result) ? result.length : 1
      };

      return this.createSuccessResult(transformResult, [
        {
          type: 'text',
          text: this.formatTransformResult(transformResult)
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `Text transformation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private formatTransformResult(result: any): string {
    const lines = [`🔄 Text Transform: ${result.operation}`];
    
    if (result.resultType === 'array') {
      lines.push(`Found ${result.resultCount} matches:`);
      if (result.resultCount > 0) {
        result.result.forEach((item: string, index: number) => {
          lines.push(`${index + 1}. ${item}`);
        });
      } else {
        lines.push('No matches found.');
      }
    } else {
      lines.push(`Result: ${result.result}`);
    }

    return lines.join('\n');
  }
}
