/**
 * Task Management MCP Tool
 * 
 * Provides comprehensive task and project management capabilities including
 * task creation, tracking, scheduling, and progress monitoring.
 */

import { z } from 'zod';
import { BaseTool } from '../base.js';
import { 
  MCPTool, 
  MCPToolConfig, 
  MCPToolContext, 
  MCPToolExecutionResult,
  MCPCapabilities,
  MCPToolCategory
} from './types.js';

const TaskManagementInputSchema = z.object({
  operation: z.enum(['create', 'update', 'delete', 'list', 'search', 'complete', 'schedule', 'assign']).describe('Task operation to perform'),
  task: z.object({
    id: z.string().optional().describe('Task ID for update/delete operations'),
    title: z.string().optional().describe('Task title'),
    description: z.string().optional().describe('Task description'),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().describe('Task priority'),
    status: z.enum(['todo', 'in_progress', 'review', 'done', 'cancelled']).optional().describe('Task status'),
    dueDate: z.string().optional().describe('Due date (ISO string)'),
    assignee: z.string().optional().describe('Assigned person'),
    tags: z.array(z.string()).optional().describe('Task tags'),
    project: z.string().optional().describe('Project name'),
    estimatedHours: z.number().optional().describe('Estimated hours to complete'),
    dependencies: z.array(z.string()).optional().describe('Task dependencies (task IDs)')
  }).optional(),
  filters: z.object({
    status: z.array(z.string()).optional(),
    priority: z.array(z.string()).optional(),
    assignee: z.string().optional(),
    project: z.string().optional(),
    tags: z.array(z.string()).optional(),
    dueBefore: z.string().optional(),
    dueAfter: z.string().optional()
  }).optional(),
  options: z.object({
    limit: z.number().optional(),
    offset: z.number().default(0),
    sortBy: z.enum(['created', 'updated', 'dueDate', 'priority', 'title']).default('created'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    includeCompleted: z.boolean().default(false)
  }).optional()
});

const TaskManagementOutputSchema = z.object({
  operation: z.string(),
  success: z.boolean(),
  result: z.union([
    z.object({
      id: z.string(),
      title: z.string(),
      description: z.string().optional(),
      priority: z.string(),
      status: z.string(),
      created: z.string(),
      updated: z.string(),
      dueDate: z.string().optional(),
      assignee: z.string().optional(),
      tags: z.array(z.string()),
      project: z.string().optional(),
      estimatedHours: z.number().optional(),
      actualHours: z.number().optional(),
      dependencies: z.array(z.string()),
      progress: z.number().min(0).max(100)
    }),
    z.array(z.object({
      id: z.string(),
      title: z.string(),
      priority: z.string(),
      status: z.string(),
      dueDate: z.string().optional(),
      assignee: z.string().optional(),
      project: z.string().optional(),
      progress: z.number()
    })),
    z.object({
      affected: z.number(),
      details: z.string()
    })
  ]),
  metadata: z.object({
    totalTasks: z.number(),
    operationTime: z.number(),
    summary: z.object({
      byStatus: z.record(z.number()),
      byPriority: z.record(z.number()),
      overdue: z.number(),
      dueToday: z.number(),
      completed: z.number()
    }).optional()
  })
});

type TaskManagementInput = z.infer<typeof TaskManagementInputSchema>;
type TaskManagementOutput = z.infer<typeof TaskManagementOutputSchema>;

export class TaskManagementTool extends BaseTool<TaskManagementInput, TaskManagementOutput> implements MCPTool<TaskManagementInput, TaskManagementOutput> {
  
  private tasks: Map<string, any> = new Map();
  private nextId = 1;

  constructor() {
    const config: MCPToolConfig = {
      name: 'task_management',
      title: 'Task Management',
      description: 'Comprehensive task and project management with scheduling, tracking, and progress monitoring',
      category: MCPToolCategory.TASK,
      tags: ['tasks', 'project', 'management', 'scheduling', 'productivity'],
      version: '1.0.0',
      author: 'AG3NTIC Framework',
      capabilities: {
        callTool: true,
        listResources: true,
        logging: true
      },
      examples: [
        {
          description: 'Create a new task',
          input: {
            operation: 'create',
            task: {
              title: 'Implement user authentication',
              description: 'Add login and registration functionality',
              priority: 'high',
              dueDate: '2024-02-15T00:00:00Z',
              estimatedHours: 8,
              tags: ['backend', 'security']
            }
          }
        }
      ]
    };

    super(config, TaskManagementInputSchema, TaskManagementOutputSchema);
    this.initializeSampleTasks();
  }

  getCapabilities(): MCPCapabilities {
    return {
      callTool: true,
      listResources: true,
      logging: true,
      listTools: true
    };
  }

  private initializeSampleTasks(): void {
    const sampleTasks = [
      {
        title: 'Setup project structure',
        description: 'Initialize the project with proper folder structure',
        priority: 'high',
        status: 'done',
        project: 'AG3NTIC',
        tags: ['setup', 'infrastructure']
      },
      {
        title: 'Implement MCP tools',
        description: 'Create comprehensive MCP tool library',
        priority: 'high',
        status: 'in_progress',
        project: 'AG3NTIC',
        tags: ['development', 'mcp'],
        estimatedHours: 16
      },
      {
        title: 'Write documentation',
        description: 'Create user and developer documentation',
        priority: 'medium',
        status: 'todo',
        project: 'AG3NTIC',
        tags: ['documentation'],
        estimatedHours: 12
      }
    ];

    sampleTasks.forEach(task => {
      const id = `task_${this.nextId++}`;
      const now = new Date().toISOString();
      this.tasks.set(id, {
        id,
        ...task,
        created: now,
        updated: now,
        progress: task.status === 'done' ? 100 : task.status === 'in_progress' ? 50 : 0,
        dependencies: [],
        tags: task.tags || []
      });
    });
  }

  async execute(input: TaskManagementInput, context?: MCPToolContext): Promise<MCPToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      const result = await this.executeTaskOperation(input);
      const operationTime = Date.now() - startTime;
      
      const output: TaskManagementOutput = {
        operation: input.operation,
        success: true,
        result: result.data,
        metadata: {
          totalTasks: this.tasks.size,
          operationTime,
          summary: this.generateSummary()
        }
      };

      return {
        success: true,
        data: output,
        content: [
          {
            type: 'text',
            text: this.formatTaskResult(output)
          }
        ],
        metadata: {
          operation: input.operation,
          totalTasks: this.tasks.size,
          operationTime
        }
      };

    } catch (error) {
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Task operation failed',
        { 
          operation: input.operation,
          operationTime: Date.now() - startTime 
        }
      );
    }
  }

  private async executeTaskOperation(input: TaskManagementInput): Promise<{ data: any }> {
    switch (input.operation) {
      case 'create':
        return this.createTask(input.task!);
      case 'update':
        return this.updateTask(input.task!);
      case 'delete':
        return this.deleteTask(input.task!.id!);
      case 'list':
        return this.listTasks(input.filters, input.options);
      case 'search':
        return this.searchTasks(input.filters, input.options);
      case 'complete':
        return this.completeTask(input.task!.id!);
      case 'schedule':
        return this.scheduleTask(input.task!);
      case 'assign':
        return this.assignTask(input.task!.id!, input.task!.assignee!);
      default:
        throw new Error(`Unsupported operation: ${input.operation}`);
    }
  }

  private async createTask(taskData: any): Promise<{ data: any }> {
    if (!taskData.title) {
      throw new Error('Task title is required');
    }

    const id = `task_${this.nextId++}`;
    const now = new Date().toISOString();
    
    const task = {
      id,
      title: taskData.title,
      description: taskData.description || '',
      priority: taskData.priority || 'medium',
      status: taskData.status || 'todo',
      created: now,
      updated: now,
      dueDate: taskData.dueDate,
      assignee: taskData.assignee,
      tags: taskData.tags || [],
      project: taskData.project,
      estimatedHours: taskData.estimatedHours,
      actualHours: 0,
      dependencies: taskData.dependencies || [],
      progress: 0
    };

    this.tasks.set(id, task);
    return { data: task };
  }

  private async updateTask(taskData: any): Promise<{ data: any }> {
    if (!taskData.id) {
      throw new Error('Task ID is required for update');
    }

    const existingTask = this.tasks.get(taskData.id);
    if (!existingTask) {
      throw new Error(`Task with ID ${taskData.id} not found`);
    }

    const updatedTask = {
      ...existingTask,
      ...taskData,
      updated: new Date().toISOString()
    };

    // Update progress based on status
    if (taskData.status) {
      switch (taskData.status) {
        case 'done':
          updatedTask.progress = 100;
          break;
        case 'in_progress':
          updatedTask.progress = Math.max(updatedTask.progress, 25);
          break;
        case 'review':
          updatedTask.progress = Math.max(updatedTask.progress, 75);
          break;
      }
    }

    this.tasks.set(taskData.id, updatedTask);
    return { data: updatedTask };
  }

  private async deleteTask(taskId: string): Promise<{ data: any }> {
    const existed = this.tasks.has(taskId);
    this.tasks.delete(taskId);
    
    return {
      data: {
        affected: existed ? 1 : 0,
        details: existed ? `Task ${taskId} deleted` : `Task ${taskId} not found`
      }
    };
  }

  private async listTasks(filters?: any, options?: any): Promise<{ data: any }> {
    let tasks = Array.from(this.tasks.values());

    // Apply filters
    if (filters) {
      if (filters.status) {
        tasks = tasks.filter(task => filters.status.includes(task.status));
      }
      if (filters.priority) {
        tasks = tasks.filter(task => filters.priority.includes(task.priority));
      }
      if (filters.assignee) {
        tasks = tasks.filter(task => task.assignee === filters.assignee);
      }
      if (filters.project) {
        tasks = tasks.filter(task => task.project === filters.project);
      }
      if (filters.tags) {
        tasks = tasks.filter(task => 
          filters.tags.some((tag: string) => task.tags.includes(tag))
        );
      }
      if (filters.dueBefore) {
        tasks = tasks.filter(task => 
          task.dueDate && new Date(task.dueDate) < new Date(filters.dueBefore)
        );
      }
    }

    // Filter completed tasks if not requested
    if (!options?.includeCompleted) {
      tasks = tasks.filter(task => task.status !== 'done');
    }

    // Sort tasks
    const sortBy = options?.sortBy || 'created';
    const sortOrder = options?.sortOrder || 'desc';
    
    tasks.sort((a, b) => {
      let aVal = a[sortBy];
      let bVal = b[sortBy];
      
      if (sortBy === 'priority') {
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        aVal = priorityOrder[aVal as keyof typeof priorityOrder] || 0;
        bVal = priorityOrder[bVal as keyof typeof priorityOrder] || 0;
      } else if (sortBy === 'created' || sortBy === 'updated' || sortBy === 'dueDate') {
        aVal = new Date(aVal || 0);
        bVal = new Date(bVal || 0);
      }
      
      if (sortOrder === 'asc') {
        return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
      } else {
        return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
      }
    });

    // Apply pagination
    const offset = options?.offset || 0;
    const limit = options?.limit;
    const paginatedTasks = limit 
      ? tasks.slice(offset, offset + limit)
      : tasks.slice(offset);

    // Return simplified task objects for list view
    const simplifiedTasks = paginatedTasks.map(task => ({
      id: task.id,
      title: task.title,
      priority: task.priority,
      status: task.status,
      dueDate: task.dueDate,
      assignee: task.assignee,
      project: task.project,
      progress: task.progress
    }));

    return { data: simplifiedTasks };
  }

  private async searchTasks(filters?: any, options?: any): Promise<{ data: any }> {
    // For now, search is the same as list with filters
    return this.listTasks(filters, options);
  }

  private async completeTask(taskId: string): Promise<{ data: any }> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task with ID ${taskId} not found`);
    }

    const updatedTask = {
      ...task,
      status: 'done',
      progress: 100,
      updated: new Date().toISOString()
    };

    this.tasks.set(taskId, updatedTask);
    return { data: updatedTask };
  }

  private async scheduleTask(taskData: any): Promise<{ data: any }> {
    if (!taskData.id || !taskData.dueDate) {
      throw new Error('Task ID and due date are required for scheduling');
    }

    return this.updateTask({
      id: taskData.id,
      dueDate: taskData.dueDate
    });
  }

  private async assignTask(taskId: string, assignee: string): Promise<{ data: any }> {
    return this.updateTask({
      id: taskId,
      assignee
    });
  }

  private generateSummary(): any {
    const tasks = Array.from(this.tasks.values());
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    const byStatus = tasks.reduce((acc, task) => {
      acc[task.status] = (acc[task.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byPriority = tasks.reduce((acc, task) => {
      acc[task.priority] = (acc[task.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const overdue = tasks.filter(task => 
      task.dueDate && 
      new Date(task.dueDate) < now && 
      task.status !== 'done'
    ).length;

    const dueToday = tasks.filter(task => 
      task.dueDate && 
      new Date(task.dueDate).toDateString() === today.toDateString() &&
      task.status !== 'done'
    ).length;

    const completed = tasks.filter(task => task.status === 'done').length;

    return {
      byStatus,
      byPriority,
      overdue,
      dueToday,
      completed
    };
  }

  private formatTaskResult(output: TaskManagementOutput): string {
    let result = `# Task Management Result\n\n`;
    result += `**Operation:** ${output.operation}\n`;
    result += `**Success:** ${output.success}\n`;
    result += `**Total Tasks:** ${output.metadata.totalTasks}\n`;
    result += `**Operation Time:** ${output.metadata.operationTime}ms\n\n`;

    if (output.metadata.summary) {
      result += `## Summary\n\n`;
      result += `**By Status:**\n`;
      Object.entries(output.metadata.summary.byStatus).forEach(([status, count]) => {
        result += `- ${status}: ${count}\n`;
      });
      result += `\n**Overdue:** ${output.metadata.summary.overdue}\n`;
      result += `**Due Today:** ${output.metadata.summary.dueToday}\n`;
      result += `**Completed:** ${output.metadata.summary.completed}\n\n`;
    }

    if (Array.isArray(output.result)) {
      result += `## Tasks (${output.result.length})\n\n`;
      output.result.forEach((task, index) => {
        result += `### ${index + 1}. ${task.title}\n`;
        result += `**Status:** ${task.status} | **Priority:** ${task.priority} | **Progress:** ${task.progress}%\n`;
        if (task.dueDate) {
          result += `**Due:** ${new Date(task.dueDate).toLocaleDateString()}\n`;
        }
        if (task.assignee) {
          result += `**Assignee:** ${task.assignee}\n`;
        }
        result += `\n`;
      });
    } else if (output.result && typeof output.result === 'object') {
      if ('title' in output.result) {
        // Single task result
        result += `## Task Details\n\n`;
        result += `**Title:** ${output.result.title}\n`;
        result += `**Status:** ${output.result.status}\n`;
        result += `**Priority:** ${output.result.priority}\n`;
        result += `**Progress:** ${output.result.progress}%\n`;
        if (output.result.description) {
          result += `**Description:** ${output.result.description}\n`;
        }
      } else if ('affected' in output.result) {
        // Bulk operation result
        result += `## Operation Result\n\n`;
        result += `**Affected:** ${output.result.affected}\n`;
        result += `**Details:** ${output.result.details}\n`;
      }
    }

    return result;
  }

  async listResources(): Promise<any[]> {
    return [
      {
        uri: 'tasks://projects',
        name: 'Projects',
        description: 'List of all projects',
        mimeType: 'application/json'
      },
      {
        uri: 'tasks://summary',
        name: 'Task Summary',
        description: 'Overall task statistics',
        mimeType: 'application/json'
      }
    ];
  }
}
