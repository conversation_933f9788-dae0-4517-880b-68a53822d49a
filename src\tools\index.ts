// Core tool types and interfaces
export * from './types.js';
export * from './base.js';
export * from './registry.js';

// Common tools
export * from './common/web.js';
export * from './common/file.js';
export * from './common/text.js';

// Tool creation utilities
import { ToolFactory as ToolFactoryClass } from './base.js';
import { globalToolRegistry, registerTool } from './registry.js';

// Re-export ToolFactory as both type and value
export { ToolFactory } from './base.js';

// Common tool instances
import { WebSearchTool, UrlFetchTool } from './common/web.js';
import { FileReadTool, FileWriteTool, DirectoryListTool } from './common/file.js';
import { TextAnalysisTool, TextTransformTool } from './common/text.js';

/**
 * Initialize and register all common tools
 */
export function initializeCommonTools(): void {
  // Web tools
  registerTool(new WebSearchTool());
  registerTool(new UrlFetchTool());

  // File tools
  registerTool(new FileReadTool());
  registerTool(new FileWriteTool());
  registerTool(new DirectoryListTool());

  // Text tools
  registerTool(new TextAnalysisTool());
  registerTool(new TextTransformTool());
}

/**
 * Get the global tool registry
 */
export function getToolRegistry() {
  return globalToolRegistry;
}

/**
 * Create a tool factory instance
 */
export function createToolFactory() {
  return ToolFactoryClass;
}

/**
 * Convenience function to create and register a simple tool
 */
export function createTool<TInput = any, TOutput = any>(
  config: import('./types.js').ToolConfig,
  inputSchema: import('zod').ZodSchema<TInput>,
  handler: import('./types.js').ToolHandler<TInput, TOutput>,
  outputSchema?: import('zod').ZodSchema<TOutput>
) {
  const tool = ToolFactoryClass.create(config, inputSchema, handler, outputSchema);
  registerTool(tool);
  return tool;
}

/**
 * Integration with AG3NTIC core helpers
 */
export function createToolNodeFromTool(
  tool: import('./types.js').Tool,
  options?: {
    validateInput?: boolean;
    validateOutput?: boolean;
    timeout?: number;
  }
): (state: any) => Promise<any> {
  return async (state: any) => {
    // Simple implementation - in a real scenario, this would integrate with the actual AG3NTIC helpers
    // For now, we'll just execute the tool with the state and return the result
    try {
      const result = await tool.execute({}, { state });

      return {
        ...state,
        messages: [
          ...state.messages,
          {
            role: 'assistant' as const,
            content: result.content?.[0]?.text || JSON.stringify(result.data),
            tool_calls: undefined
          }
        ]
      };
    } catch (error) {
      return {
        ...state,
        messages: [
          ...state.messages,
          {
            role: 'assistant' as const,
            content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            tool_calls: undefined
          }
        ]
      };
    }
  };
}

/**
 * Convert AG3NTIC tool to our tool format
 */
export function convertAG3NTICTool(
  name: string,
  ag3nticTool: any // Legacy AG3NTIC tool type
): import('./types.js').Tool {
  const { z } = require('zod');
  const { SimpleTool } = require('./base.js');
  
  return new SimpleTool(
    {
      name,
      title: name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: `AG3NTIC tool: ${name}`,
      category: 'custom'
    },
    z.any(), // AG3NTIC tools don't have schemas by default
    async (input, context) => {
      try {
        const result = await ag3nticTool(input);
        return {
          success: true,
          data: result,
          content: [{ type: 'text', text: JSON.stringify(result, null, 2) }]
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          content: [{ type: 'text', text: `Error: ${error}` }]
        };
      }
    }
  );
}

// Auto-initialize common tools when module is imported
initializeCommonTools();
