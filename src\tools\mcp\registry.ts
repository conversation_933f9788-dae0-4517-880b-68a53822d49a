/**
 * MCP Tools Registry
 * 
 * Central registry for all MCP tools with discovery, initialization,
 * and management capabilities.
 */

import { MCPTool, MCPToolCollection, MCPServerConfig, MCPCapabilities, MCPToolCategory } from './types.js';
import { SequentialThinkingTool } from './sequential-thinking.js';
import { Context7Tool } from './context7.js';
import { WebSearchTool } from './web-search.js';
import { FileOperationsTool } from './file-operations.js';
import { MemoryManagementTool } from './memory-management.js';
import { CodeAnalysisTool } from './code-analysis.js';
import { TaskManagementTool } from './task-management.js';
import { APIClientTool } from './api-client.js';
import { DataProcessingTool } from './data-processing.js';
import { TextProcessingTool } from './text-processing.js';

/**
 * MCP Tools Registry class
 */
export class MCPToolsRegistry {
  private tools: Map<string, MCPTool> = new Map();
  private collections: Map<string, MCPToolCollection> = new Map();
  private initialized = false;

  constructor() {
    this.initializeDefaultTools();
    this.initializeCollections();
  }

  /**
   * Initialize default MCP tools
   */
  private initializeDefaultTools(): void {
    const defaultTools: MCPTool[] = [
      new SequentialThinkingTool(),
      new Context7Tool(),
      new WebSearchTool(),
      new FileOperationsTool(),
      new MemoryManagementTool(),
      new CodeAnalysisTool(),
      new TaskManagementTool(),
      new APIClientTool(),
      new DataProcessingTool(),
      new TextProcessingTool()
    ];

    defaultTools.forEach(tool => {
      this.tools.set(tool.config.name, tool);
    });

    this.initialized = true;
  }

  /**
   * Initialize tool collections
   */
  private initializeCollections(): void {
    // Core thinking and analysis tools
    this.collections.set('thinking', {
      name: 'Thinking & Analysis',
      description: 'Tools for structured thinking, analysis, and problem-solving',
      tools: [
        this.tools.get('sequential_thinking')!,
        this.tools.get('code_analysis')!,
        this.tools.get('text_processing')!
      ],
      capabilities: {
        callTool: true,
        logging: true,
        listTools: true
      },
      version: '1.0.0'
    });

    // Information and context tools
    this.collections.set('information', {
      name: 'Information & Context',
      description: 'Tools for retrieving and managing information and context',
      tools: [
        this.tools.get('context7')!,
        this.tools.get('web_search')!,
        this.tools.get('memory_management')!
      ],
      capabilities: {
        callTool: true,
        listResources: true,
        readResource: true,
        logging: true
      },
      version: '1.0.0'
    });

    // Development and productivity tools
    this.collections.set('development', {
      name: 'Development & Productivity',
      description: 'Tools for software development and productivity',
      tools: [
        this.tools.get('file_operations')!,
        this.tools.get('task_management')!,
        this.tools.get('api_client')!,
        this.tools.get('data_processing')!
      ],
      capabilities: {
        callTool: true,
        listResources: true,
        logging: true
      },
      version: '1.0.0'
    });

    // Complete toolkit
    this.collections.set('complete', {
      name: 'Complete MCP Toolkit',
      description: 'All available MCP tools for comprehensive AI agent capabilities',
      tools: Array.from(this.tools.values()),
      capabilities: {
        callTool: true,
        listTools: true,
        listResources: true,
        readResource: true,
        logging: true,
        sampling: true
      },
      version: '1.0.0'
    });
  }

  /**
   * Get a tool by name
   */
  getTool(name: string): MCPTool | undefined {
    return this.tools.get(name);
  }

  /**
   * Get all tools
   */
  getAllTools(): MCPTool[] {
    return Array.from(this.tools.values());
  }

  /**
   * Get tools by category
   */
  getToolsByCategory(category: MCPToolCategory): MCPTool[] {
    return Array.from(this.tools.values()).filter(
      tool => tool.config.category === category
    );
  }

  /**
   * Get tools by tags
   */
  getToolsByTags(tags: string[]): MCPTool[] {
    return Array.from(this.tools.values()).filter(tool =>
      tool.config.tags?.some(tag => tags.includes(tag))
    );
  }

  /**
   * Search tools by name or description
   */
  searchTools(query: string): MCPTool[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.tools.values()).filter(tool =>
      tool.config.name.toLowerCase().includes(lowerQuery) ||
      tool.config.title.toLowerCase().includes(lowerQuery) ||
      tool.config.description.toLowerCase().includes(lowerQuery) ||
      tool.config.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * Register a new tool
   */
  registerTool(tool: MCPTool): void {
    this.tools.set(tool.config.name, tool);
  }

  /**
   * Unregister a tool
   */
  unregisterTool(name: string): boolean {
    return this.tools.delete(name);
  }

  /**
   * Get a tool collection
   */
  getCollection(name: string): MCPToolCollection | undefined {
    return this.collections.get(name);
  }

  /**
   * Get all collections
   */
  getAllCollections(): MCPToolCollection[] {
    return Array.from(this.collections.values());
  }

  /**
   * Register a new collection
   */
  registerCollection(name: string, collection: MCPToolCollection): void {
    this.collections.set(name, collection);
  }

  /**
   * Get server configuration for MCP compliance
   */
  getServerConfig(): MCPServerConfig {
    const allCapabilities = this.aggregateCapabilities();
    
    return {
      name: 'AG3NTIC MCP Tools Server',
      version: '1.0.0',
      capabilities: allCapabilities,
      tools: Array.from(this.tools.keys()),
      resources: this.getAllResources(),
      prompts: this.getAllPrompts()
    };
  }

  /**
   * Aggregate capabilities from all tools
   */
  private aggregateCapabilities(): MCPCapabilities {
    const capabilities: MCPCapabilities = {
      listTools: true,
      callTool: true,
      logging: true
    };

    for (const tool of this.tools.values()) {
      const toolCapabilities = tool.getCapabilities();
      
      if (toolCapabilities.listResources) capabilities.listResources = true;
      if (toolCapabilities.readResource) capabilities.readResource = true;
      if (toolCapabilities.subscribeResource) capabilities.subscribeResource = true;
      if (toolCapabilities.listPrompts) capabilities.listPrompts = true;
      if (toolCapabilities.getPrompt) capabilities.getPrompt = true;
      if (toolCapabilities.sampling) capabilities.sampling = true;
    }

    return capabilities;
  }

  /**
   * Get all available resources from tools
   */
  private getAllResources(): any[] {
    const resources: any[] = [];
    
    for (const tool of this.tools.values()) {
      if (tool.listResources) {
        try {
          // Note: In a real implementation, this would be async
          // resources.push(...await tool.listResources());
        } catch (error) {
          console.warn(`Failed to list resources for tool ${tool.config.name}:`, error);
        }
      }
    }

    return resources;
  }

  /**
   * Get all available prompts from tools
   */
  private getAllPrompts(): any[] {
    const prompts: any[] = [];
    
    for (const tool of this.tools.values()) {
      if (tool.listPrompts) {
        try {
          // Note: In a real implementation, this would be async
          // prompts.push(...await tool.listPrompts());
        } catch (error) {
          console.warn(`Failed to list prompts for tool ${tool.config.name}:`, error);
        }
      }
    }

    return prompts;
  }

  /**
   * Get tool statistics
   */
  getStatistics(): {
    totalTools: number;
    toolsByCategory: Record<string, number>;
    toolsByCapability: Record<string, number>;
    collections: number;
  } {
    const toolsByCategory: Record<string, number> = {};
    const toolsByCapability: Record<string, number> = {};

    for (const tool of this.tools.values()) {
      // Count by category
      const category = tool.config.category || 'unknown';
      toolsByCategory[category] = (toolsByCategory[category] || 0) + 1;

      // Count by capabilities
      const capabilities = tool.getCapabilities();
      Object.entries(capabilities).forEach(([capability, enabled]) => {
        if (enabled) {
          toolsByCapability[capability] = (toolsByCapability[capability] || 0) + 1;
        }
      });
    }

    return {
      totalTools: this.tools.size,
      toolsByCategory,
      toolsByCapability,
      collections: this.collections.size
    };
  }

  /**
   * Validate tool compatibility
   */
  validateTool(tool: MCPTool): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required properties
    if (!tool.config.name) errors.push('Tool name is required');
    if (!tool.config.title) errors.push('Tool title is required');
    if (!tool.config.description) errors.push('Tool description is required');
    if (!tool.inputSchema) errors.push('Tool input schema is required');
    if (!tool.execute) errors.push('Tool execute method is required');
    if (!tool.getCapabilities) errors.push('Tool getCapabilities method is required');

    // Check for name conflicts
    if (this.tools.has(tool.config.name)) {
      errors.push(`Tool with name '${tool.config.name}' already exists`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Export registry configuration
   */
  exportConfig(): {
    tools: any[];
    collections: any[];
    serverConfig: MCPServerConfig;
    statistics: any;
  } {
    return {
      tools: Array.from(this.tools.values()).map(tool => ({
        name: tool.config.name,
        title: tool.config.title,
        description: tool.config.description,
        category: tool.config.category,
        tags: tool.config.tags,
        version: tool.config.version,
        capabilities: tool.getCapabilities()
      })),
      collections: Array.from(this.collections.values()).map(collection => ({
        name: collection.name,
        description: collection.description,
        toolCount: collection.tools.length,
        capabilities: collection.capabilities,
        version: collection.version
      })),
      serverConfig: this.getServerConfig(),
      statistics: this.getStatistics()
    };
  }

  /**
   * Check if registry is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }
}

/**
 * Global MCP tools registry instance
 */
export const mcpToolsRegistry = new MCPToolsRegistry();

/**
 * Utility functions for working with MCP tools
 */
export const MCPUtils = {
  /**
   * Get tools by category
   */
  getToolsByCategory: (category: MCPToolCategory) => mcpToolsRegistry.getToolsByCategory(category),

  /**
   * Get thinking tools
   */
  getThinkingTools: () => mcpToolsRegistry.getCollection('thinking')?.tools || [],

  /**
   * Get information tools
   */
  getInformationTools: () => mcpToolsRegistry.getCollection('information')?.tools || [],

  /**
   * Get development tools
   */
  getDevelopmentTools: () => mcpToolsRegistry.getCollection('development')?.tools || [],

  /**
   * Get complete toolkit
   */
  getCompleteToolkit: () => mcpToolsRegistry.getCollection('complete')?.tools || [],

  /**
   * Search for tools
   */
  searchTools: (query: string) => mcpToolsRegistry.searchTools(query),

  /**
   * Get registry statistics
   */
  getStatistics: () => mcpToolsRegistry.getStatistics(),

  /**
   * Export registry configuration
   */
  exportConfig: () => mcpToolsRegistry.exportConfig()
};
