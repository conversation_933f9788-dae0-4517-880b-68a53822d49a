/**
 * Tool Runnable Integration for AG3NTIC
 * 
 * This module provides integration between the AG3NTIC tool system
 * and the new Runnable interface, enabling tools to be used in
 * composition chains and benefit from the enhanced callback system.
 */

import { ComposableBaseRunnable, RunnableConfig } from './runnable.js';
import { Tool, ToolExecutionResult } from '../tools/types.js';
import { AgentState, MCPMessage } from './types.js';

/**
 * Input type for tool execution in agent context
 */
export interface ToolInput {
  /** The tool input parameters */
  input: any;
  /** Optional tool name (if not provided, uses the tool's name) */
  toolName?: string;
  /** Optional context for tool execution */
  context?: any;
}

/**
 * Output type for tool execution in agent context
 */
export interface ToolOutput {
  /** The tool execution result */
  result: ToolExecutionResult;
  /** Updated agent state with tool result */
  state: Partial<AgentState>;
}

/**
 * Runnable wrapper for AG3NTIC tools
 */
export class ToolRunnable extends ComposableBaseRunnable<ToolInput, ToolOutput> {
  constructor(private tool: Tool) {
    super(`Tool[${tool.config.name}]`);
  }

  async invoke(input: ToolInput, config?: RunnableConfig): Promise<ToolOutput> {
    return this.executeWithCallbacks(async () => {
      // Execute the tool
      const result = await this.tool.execute(input.input, input.context);
      
      // Create updated agent state
      const state: Partial<AgentState> = {
        messages: [
          {
            role: 'tool' as const,
            tool_call_id: `tool_${Date.now()}`,
            content: result.success 
              ? JSON.stringify(result.data)
              : `Error: ${result.error}`
          }
        ]
      };

      return { result, state };
    }, input, config);
  }

  async *stream(input: ToolInput, config?: RunnableConfig): AsyncGenerator<ToolOutput, void, unknown> {
    // Tools typically don't stream, so we yield the complete result
    const result = await this.invoke(input, config);
    yield result;
  }

  /**
   * Get the underlying tool
   */
  getTool(): Tool {
    return this.tool;
  }
}

/**
 * Runnable that executes a tool and updates agent state
 */
export class ToolExecutorRunnable extends ComposableBaseRunnable<AgentState, AgentState> {
  constructor(private tool: Tool) {
    super(`ToolExecutor[${tool.config.name}]`);
  }

  async invoke(input: AgentState, config?: RunnableConfig): Promise<AgentState> {
    return this.executeWithCallbacks(async () => {
      // Find the last assistant message with tool calls
      const lastMessage = input.messages[input.messages.length - 1];
      
      if (lastMessage?.role !== 'assistant' || !lastMessage.tool_calls) {
        throw new Error('No tool calls found in the last assistant message');
      }

      // Execute each tool call
      const newMessages: MCPMessage[] = [];
      
      for (const toolCall of lastMessage.tool_calls) {
        if (toolCall.function.name === this.tool.config.name) {
          try {
            const toolInput = JSON.parse(toolCall.function.arguments);
            const result = await this.tool.execute(toolInput);
            
            newMessages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: result.success 
                ? JSON.stringify(result.data)
                : `Error: ${result.error}`
            });
          } catch (error) {
            newMessages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: `Error executing tool: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
          }
        }
      }

      return {
        ...input,
        messages: [...input.messages, ...newMessages]
      };
    }, input, config);
  }
}

/**
 * Runnable that can execute multiple tools based on tool calls in the state
 */
export class MultiToolExecutorRunnable extends ComposableBaseRunnable<AgentState, AgentState> {
  private tools: Map<string, Tool> = new Map();

  constructor(tools: Tool[], name?: string) {
    super(name || 'MultiToolExecutor');
    
    for (const tool of tools) {
      this.tools.set(tool.config.name, tool);
    }
  }

  async invoke(input: AgentState, config?: RunnableConfig): Promise<AgentState> {
    return this.executeWithCallbacks(async () => {
      // Find the last assistant message with tool calls
      const lastMessage = input.messages[input.messages.length - 1];
      
      if (lastMessage?.role !== 'assistant' || !lastMessage.tool_calls) {
        // No tool calls to execute, return state unchanged
        return input;
      }

      // Execute each tool call
      const newMessages: MCPMessage[] = [];
      
      for (const toolCall of lastMessage.tool_calls) {
        const tool = this.tools.get(toolCall.function.name);
        
        if (tool) {
          try {
            const toolInput = JSON.parse(toolCall.function.arguments);
            const result = await tool.execute(toolInput);
            
            newMessages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: result.success 
                ? JSON.stringify(result.data)
                : `Error: ${result.error}`
            });
          } catch (error) {
            newMessages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: `Error executing tool ${toolCall.function.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
          }
        } else {
          newMessages.push({
            role: 'tool',
            tool_call_id: toolCall.id,
            content: `Error: Tool '${toolCall.function.name}' not found`
          });
        }
      }

      return {
        ...input,
        messages: [...input.messages, ...newMessages]
      };
    }, input, config);
  }

  /**
   * Add a tool to the executor
   */
  addTool(tool: Tool): void {
    this.tools.set(tool.config.name, tool);
  }

  /**
   * Remove a tool from the executor
   */
  removeTool(toolName: string): void {
    this.tools.delete(toolName);
  }

  /**
   * Get all available tools
   */
  getTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  /**
   * Check if a tool is available
   */
  hasTool(toolName: string): boolean {
    return this.tools.has(toolName);
  }
}

/**
 * Utility functions for creating tool runnables
 */
export const ToolRunnableUtils = {
  /**
   * Create a tool runnable from a tool
   */
  fromTool(tool: Tool): ToolRunnable {
    return new ToolRunnable(tool);
  },

  /**
   * Create a tool executor runnable for a single tool
   */
  executor(tool: Tool): ToolExecutorRunnable {
    return new ToolExecutorRunnable(tool);
  },

  /**
   * Create a multi-tool executor runnable
   */
  multiExecutor(tools: Tool[], name?: string): MultiToolExecutorRunnable {
    return new MultiToolExecutorRunnable(tools, name);
  },

  /**
   * Create a tool executor from the global tool registry
   */
  fromRegistry(toolNames: string[]): MultiToolExecutorRunnable {
    // This would integrate with the global tool registry
    // For now, we'll return an empty executor
    return new MultiToolExecutorRunnable([], 'RegistryToolExecutor');
  },

  /**
   * Create a conditional tool executor that only executes if certain conditions are met
   */
  conditional(
    tool: Tool,
    condition: (state: AgentState) => boolean
  ): ComposableBaseRunnable<AgentState, AgentState> {
    return new (class extends ComposableBaseRunnable<AgentState, AgentState> {
      constructor() {
        super(`ConditionalTool[${tool.config.name}]`);
      }

      async invoke(input: AgentState, config?: RunnableConfig): Promise<AgentState> {
        return this.executeWithCallbacks(async () => {
          if (condition(input)) {
            const executor = new ToolExecutorRunnable(tool);
            return executor.invoke(input, config);
          }
          return input;
        }, input, config);
      }
    })();
  }
};

/**
 * Type guards for tool-related types
 */
export const ToolTypeGuards = {
  /**
   * Check if a message contains tool calls
   */
  hasToolCalls(message: MCPMessage): message is MCPMessage & { tool_calls: NonNullable<MCPMessage['tool_calls']> } {
    return message.role === 'assistant' && Array.isArray(message.tool_calls) && message.tool_calls.length > 0;
  },

  /**
   * Check if a state has pending tool calls
   */
  hasPendingToolCalls(state: AgentState): boolean {
    const lastMessage = state.messages[state.messages.length - 1];
    return this.hasToolCalls(lastMessage);
  },

  /**
   * Check if a tool call is for a specific tool
   */
  isToolCall(toolCall: any, toolName: string): boolean {
    return toolCall && 
           typeof toolCall === 'object' && 
           toolCall.function && 
           toolCall.function.name === toolName;
  }
};
