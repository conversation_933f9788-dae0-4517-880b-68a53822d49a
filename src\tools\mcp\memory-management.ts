/**
 * Memory Management MCP Tool
 * 
 * Provides persistent memory capabilities for AI agents, including
 * key-value storage, conversation history, and context management.
 */

import { z } from 'zod';
import { BaseTool } from '../base.js';
import { 
  MCPTool, 
  MCPToolConfig, 
  MCPToolContext, 
  MCPToolExecutionResult,
  MCPCapabilities,
  MCPToolCategory
} from './types.js';

/**
 * Memory management input schema
 */
const MemoryManagementInputSchema = z.object({
  operation: z.enum(['store', 'retrieve', 'delete', 'list', 'search', 'clear', 'backup', 'restore']).describe('Memory operation to perform'),
  key: z.string().optional().describe('Memory key for store/retrieve/delete operations'),
  value: z.any().optional().describe('Value to store (for store operation)'),
  namespace: z.string().default('default').describe('Memory namespace for organization'),
  query: z.string().optional().describe('Search query for search operation'),
  pattern: z.string().optional().describe('Key pattern for list/search operations'),
  ttl: z.number().optional().describe('Time-to-live in seconds for stored values'),
  metadata: z.record(z.any()).optional().describe('Additional metadata for stored values'),
  options: z.object({
    includeMetadata: z.boolean().default(false).describe('Whether to include metadata in results'),
    limit: z.number().optional().describe('Maximum number of results to return'),
    offset: z.number().default(0).describe('Offset for pagination'),
    sortBy: z.enum(['key', 'timestamp', 'size']).default('timestamp').describe('Sort order for results'),
    sortOrder: z.enum(['asc', 'desc']).default('desc').describe('Sort direction')
  }).optional().describe('Additional options for operations')
});

/**
 * Memory management output schema
 */
const MemoryManagementOutputSchema = z.object({
  operation: z.string(),
  success: z.boolean(),
  result: z.union([
    z.any(), // For retrieve operations
    z.array(z.object({
      key: z.string(),
      value: z.any().optional(),
      namespace: z.string(),
      timestamp: z.string(),
      ttl: z.number().optional(),
      size: z.number(),
      metadata: z.record(z.any()).optional()
    })), // For list/search operations
    z.object({
      affected: z.number(),
      details: z.string().optional()
    }), // For bulk operations
    z.null() // For operations that don't return data
  ]),
  metadata: z.object({
    namespace: z.string(),
    operationTime: z.number(),
    totalKeys: z.number().optional(),
    totalSize: z.number().optional(),
    expirationTime: z.string().optional(),
    backupLocation: z.string().optional()
  })
});

type MemoryManagementInput = z.infer<typeof MemoryManagementInputSchema>;
type MemoryManagementOutput = z.infer<typeof MemoryManagementOutputSchema>;

/**
 * Memory Management MCP Tool implementation
 */
export class MemoryManagementTool extends BaseTool<MemoryManagementInput, MemoryManagementOutput> implements MCPTool<MemoryManagementInput, MemoryManagementOutput> {
  
  private memory: Map<string, Map<string, any>> = new Map();
  private metadata: Map<string, Map<string, any>> = new Map();

  constructor() {
    const config: MCPToolConfig = {
      name: 'memory_management',
      title: 'Memory Management',
      description: 'Persistent memory storage and retrieval for AI agents with namespace support',
      category: MCPToolCategory.MEMORY,
      tags: ['memory', 'storage', 'persistence', 'context', 'state'],
      version: '1.0.0',
      author: 'AG3NTIC Framework',
      capabilities: {
        callTool: true,
        listResources: true,
        readResource: true,
        logging: true
      },
      examples: [
        {
          description: 'Store user preference',
          input: {
            operation: 'store',
            key: 'user_theme',
            value: 'dark',
            namespace: 'preferences',
            ttl: 86400
          }
        },
        {
          description: 'Retrieve conversation context',
          input: {
            operation: 'retrieve',
            key: 'conversation_history',
            namespace: 'chat'
          }
        },
        {
          description: 'Search for related memories',
          input: {
            operation: 'search',
            query: 'project planning',
            namespace: 'work',
            options: {
              limit: 10,
              includeMetadata: true
            }
          }
        }
      ]
    };

    super(config, MemoryManagementInputSchema, MemoryManagementOutputSchema);
    this.initializeNamespaces();
  }

  getCapabilities(): MCPCapabilities {
    return {
      callTool: true,
      listResources: true,
      readResource: true,
      logging: true,
      listTools: true
    };
  }

  private initializeNamespaces(): void {
    // Initialize default namespaces
    const defaultNamespaces = ['default', 'preferences', 'chat', 'work', 'personal'];
    defaultNamespaces.forEach(ns => {
      this.memory.set(ns, new Map());
      this.metadata.set(ns, new Map());
    });
  }

  async execute(input: MemoryManagementInput, context?: MCPToolContext): Promise<MCPToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      const result = await this.executeMemoryOperation(input);
      const operationTime = Date.now() - startTime;
      
      const output: MemoryManagementOutput = {
        operation: input.operation,
        success: true,
        result: result.data,
        metadata: {
          namespace: input.namespace,
          operationTime,
          totalKeys: result.totalKeys,
          totalSize: result.totalSize,
          expirationTime: result.expirationTime,
          backupLocation: result.backupLocation
        }
      };

      return {
        success: true,
        data: output,
        content: [
          {
            type: 'text',
            text: this.formatMemoryResult(output)
          }
        ],
        metadata: {
          operation: input.operation,
          namespace: input.namespace,
          operationTime
        }
      };

    } catch (error) {
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Memory operation failed',
        { 
          operation: input.operation,
          namespace: input.namespace,
          operationTime: Date.now() - startTime 
        }
      );
    }
  }

  private async executeMemoryOperation(input: MemoryManagementInput): Promise<{
    data: any;
    totalKeys?: number;
    totalSize?: number;
    expirationTime?: string;
    backupLocation?: string;
  }> {
    // Ensure namespace exists
    if (!this.memory.has(input.namespace)) {
      this.memory.set(input.namespace, new Map());
      this.metadata.set(input.namespace, new Map());
    }

    const namespaceMemory = this.memory.get(input.namespace)!;
    const namespaceMetadata = this.metadata.get(input.namespace)!;

    switch (input.operation) {
      case 'store':
        return this.storeValue(namespaceMemory, namespaceMetadata, input);
      case 'retrieve':
        return this.retrieveValue(namespaceMemory, namespaceMetadata, input);
      case 'delete':
        return this.deleteValue(namespaceMemory, namespaceMetadata, input);
      case 'list':
        return this.listValues(namespaceMemory, namespaceMetadata, input);
      case 'search':
        return this.searchValues(namespaceMemory, namespaceMetadata, input);
      case 'clear':
        return this.clearNamespace(namespaceMemory, namespaceMetadata, input);
      case 'backup':
        return this.backupMemory(input);
      case 'restore':
        return this.restoreMemory(input);
      default:
        throw new Error(`Unsupported operation: ${input.operation}`);
    }
  }

  private async storeValue(
    namespaceMemory: Map<string, any>,
    namespaceMetadata: Map<string, any>,
    input: MemoryManagementInput
  ): Promise<any> {
    if (!input.key || input.value === undefined) {
      throw new Error('Key and value are required for store operation');
    }

    const timestamp = new Date().toISOString();
    const size = JSON.stringify(input.value).length;
    
    // Store the value
    namespaceMemory.set(input.key, input.value);
    
    // Store metadata
    const metadata = {
      timestamp,
      size,
      ttl: input.ttl,
      userMetadata: input.metadata || {},
      expiresAt: input.ttl ? new Date(Date.now() + input.ttl * 1000).toISOString() : undefined
    };
    namespaceMetadata.set(input.key, metadata);

    // Schedule expiration if TTL is set
    if (input.ttl) {
      setTimeout(() => {
        namespaceMemory.delete(input.key!);
        namespaceMetadata.delete(input.key!);
      }, input.ttl * 1000);
    }

    return {
      data: null,
      totalKeys: namespaceMemory.size,
      totalSize: this.calculateNamespaceSize(namespaceMetadata),
      expirationTime: metadata.expiresAt
    };
  }

  private async retrieveValue(
    namespaceMemory: Map<string, any>,
    namespaceMetadata: Map<string, any>,
    input: MemoryManagementInput
  ): Promise<any> {
    if (!input.key) {
      throw new Error('Key is required for retrieve operation');
    }

    const value = namespaceMemory.get(input.key);
    const metadata = namespaceMetadata.get(input.key);

    if (value === undefined) {
      throw new Error(`Key '${input.key}' not found in namespace '${input.namespace}'`);
    }

    // Check if value has expired
    if (metadata?.expiresAt && new Date(metadata.expiresAt) < new Date()) {
      namespaceMemory.delete(input.key);
      namespaceMetadata.delete(input.key);
      throw new Error(`Key '${input.key}' has expired`);
    }

    let result = value;
    if (input.options?.includeMetadata && metadata) {
      result = {
        value,
        metadata: {
          timestamp: metadata.timestamp,
          size: metadata.size,
          ttl: metadata.ttl,
          expiresAt: metadata.expiresAt,
          ...metadata.userMetadata
        }
      };
    }

    return {
      data: result,
      totalKeys: namespaceMemory.size
    };
  }

  private async deleteValue(
    namespaceMemory: Map<string, any>,
    namespaceMetadata: Map<string, any>,
    input: MemoryManagementInput
  ): Promise<any> {
    if (!input.key) {
      throw new Error('Key is required for delete operation');
    }

    const existed = namespaceMemory.has(input.key);
    namespaceMemory.delete(input.key);
    namespaceMetadata.delete(input.key);

    return {
      data: { deleted: existed },
      totalKeys: namespaceMemory.size,
      totalSize: this.calculateNamespaceSize(namespaceMetadata)
    };
  }

  private async listValues(
    namespaceMemory: Map<string, any>,
    namespaceMetadata: Map<string, any>,
    input: MemoryManagementInput
  ): Promise<any> {
    const entries = Array.from(namespaceMemory.entries()).map(([key, value]) => {
      const metadata = namespaceMetadata.get(key);
      return {
        key,
        value: input.options?.includeMetadata ? undefined : value,
        namespace: input.namespace,
        timestamp: metadata?.timestamp || new Date().toISOString(),
        ttl: metadata?.ttl,
        size: metadata?.size || 0,
        metadata: input.options?.includeMetadata ? {
          ...metadata?.userMetadata,
          value
        } : undefined
      };
    });

    // Apply pattern filter
    let filteredEntries = entries;
    if (input.pattern) {
      const regex = new RegExp(input.pattern, 'i');
      filteredEntries = entries.filter(entry => regex.test(entry.key));
    }

    // Sort entries
    const sortBy = input.options?.sortBy || 'timestamp';
    const sortOrder = input.options?.sortOrder || 'desc';
    filteredEntries.sort((a, b) => {
      let aVal: any, bVal: any;
      switch (sortBy) {
        case 'key':
          aVal = a.key;
          bVal = b.key;
          break;
        case 'size':
          aVal = a.size;
          bVal = b.size;
          break;
        case 'timestamp':
        default:
          aVal = new Date(a.timestamp);
          bVal = new Date(b.timestamp);
          break;
      }
      
      if (sortOrder === 'asc') {
        return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
      } else {
        return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
      }
    });

    // Apply pagination
    const offset = input.options?.offset || 0;
    const limit = input.options?.limit;
    const paginatedEntries = limit 
      ? filteredEntries.slice(offset, offset + limit)
      : filteredEntries.slice(offset);

    return {
      data: paginatedEntries,
      totalKeys: namespaceMemory.size,
      totalSize: this.calculateNamespaceSize(namespaceMetadata)
    };
  }

  private async searchValues(
    namespaceMemory: Map<string, any>,
    namespaceMetadata: Map<string, any>,
    input: MemoryManagementInput
  ): Promise<any> {
    if (!input.query) {
      throw new Error('Query is required for search operation');
    }

    const searchRegex = new RegExp(input.query, 'i');
    const matches: any[] = [];

    for (const [key, value] of namespaceMemory.entries()) {
      const metadata = namespaceMetadata.get(key);
      const valueStr = JSON.stringify(value);
      
      // Search in key, value, and metadata
      if (searchRegex.test(key) || 
          searchRegex.test(valueStr) || 
          (metadata?.userMetadata && searchRegex.test(JSON.stringify(metadata.userMetadata)))) {
        
        matches.push({
          key,
          value: input.options?.includeMetadata ? undefined : value,
          namespace: input.namespace,
          timestamp: metadata?.timestamp || new Date().toISOString(),
          ttl: metadata?.ttl,
          size: metadata?.size || 0,
          metadata: input.options?.includeMetadata ? {
            ...metadata?.userMetadata,
            value
          } : undefined
        });
      }
    }

    // Apply limit
    const limit = input.options?.limit;
    const limitedMatches = limit ? matches.slice(0, limit) : matches;

    return {
      data: limitedMatches,
      totalKeys: namespaceMemory.size
    };
  }

  private async clearNamespace(
    namespaceMemory: Map<string, any>,
    namespaceMetadata: Map<string, any>,
    input: MemoryManagementInput
  ): Promise<any> {
    const keysCleared = namespaceMemory.size;
    namespaceMemory.clear();
    namespaceMetadata.clear();

    return {
      data: { affected: keysCleared, details: `Cleared ${keysCleared} keys from namespace '${input.namespace}'` },
      totalKeys: 0,
      totalSize: 0
    };
  }

  private async backupMemory(input: MemoryManagementInput): Promise<any> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupLocation = `memory-backup-${input.namespace}-${timestamp}.json`;
    
    const namespaceMemory = this.memory.get(input.namespace);
    const namespaceMetadata = this.metadata.get(input.namespace);
    
    if (!namespaceMemory || !namespaceMetadata) {
      throw new Error(`Namespace '${input.namespace}' not found`);
    }

    // Simulate backup creation
    const backupData = {
      namespace: input.namespace,
      timestamp,
      memory: Object.fromEntries(namespaceMemory.entries()),
      metadata: Object.fromEntries(namespaceMetadata.entries())
    };

    return {
      data: { affected: namespaceMemory.size, details: `Backup created: ${backupLocation}` },
      backupLocation,
      totalKeys: namespaceMemory.size
    };
  }

  private async restoreMemory(input: MemoryManagementInput): Promise<any> {
    // Simulate restore operation
    const restoredKeys = 5; // Mock number
    
    return {
      data: { affected: restoredKeys, details: `Restored ${restoredKeys} keys to namespace '${input.namespace}'` },
      totalKeys: restoredKeys
    };
  }

  private calculateNamespaceSize(namespaceMetadata: Map<string, any>): number {
    let totalSize = 0;
    for (const metadata of namespaceMetadata.values()) {
      totalSize += metadata.size || 0;
    }
    return totalSize;
  }

  private formatMemoryResult(output: MemoryManagementOutput): string {
    let result = `# Memory Operation Result\n\n`;
    result += `**Operation:** ${output.operation}\n`;
    result += `**Namespace:** ${output.metadata.namespace}\n`;
    result += `**Success:** ${output.success}\n`;
    result += `**Time:** ${output.metadata.operationTime}ms\n\n`;

    if (output.metadata.totalKeys !== undefined) {
      result += `**Total Keys:** ${output.metadata.totalKeys}\n`;
    }

    if (output.metadata.totalSize !== undefined) {
      result += `**Total Size:** ${output.metadata.totalSize} bytes\n`;
    }

    if (output.metadata.expirationTime) {
      result += `**Expires:** ${output.metadata.expirationTime}\n`;
    }

    if (output.metadata.backupLocation) {
      result += `**Backup:** ${output.metadata.backupLocation}\n`;
    }

    if (Array.isArray(output.result)) {
      result += `\n## Results (${output.result.length})\n\n`;
      output.result.forEach((item, index) => {
        result += `### ${index + 1}. ${item.key}\n`;
        result += `**Timestamp:** ${new Date(item.timestamp).toLocaleString()}\n`;
        result += `**Size:** ${item.size} bytes\n`;
        if (item.ttl) result += `**TTL:** ${item.ttl}s\n`;
        if (item.value !== undefined) {
          result += `**Value:** \`${JSON.stringify(item.value)}\`\n`;
        }
        result += `\n`;
      });
    } else if (output.result && typeof output.result === 'object' && 'affected' in output.result) {
      result += `\n**Affected:** ${output.result.affected}\n`;
      if (output.result.details) {
        result += `**Details:** ${output.result.details}\n`;
      }
    } else if (output.result !== null && output.result !== undefined) {
      result += `\n## Value\n\n\`\`\`json\n${JSON.stringify(output.result, null, 2)}\n\`\`\`\n`;
    }

    return result;
  }

  async listResources(): Promise<any[]> {
    return [
      {
        uri: 'memory://namespaces',
        name: 'Available Namespaces',
        description: 'List of memory namespaces',
        mimeType: 'application/json'
      }
    ];
  }

  async readResource(uri: string): Promise<{ contents: any; mimeType?: string }> {
    if (uri === 'memory://namespaces') {
      return {
        contents: {
          namespaces: Array.from(this.memory.keys())
        },
        mimeType: 'application/json'
      };
    }
    
    throw new Error(`Resource not found: ${uri}`);
  }
}
