# AG3NTIC Tool & MCP Libraries

This document describes the new Tool Library and MCP (Model Context Protocol) integration that has been added to the AG3NTIC framework.

## Overview

The AG3NTIC framework now includes two powerful new libraries:

1. **Tool Library** - A comprehensive system for creating, managing, and executing tools
2. **MCP Library** - Integration with the Model Context Protocol for interoperability

## Tool Library

### Features

- **Structured Tool Definitions**: Define tools with strong typing using Zod schemas
- **Tool Registry**: Centralized discovery and management of tools
- **Input/Output Validation**: Automatic validation using Zod schemas
- **Error Handling**: Comprehensive error handling and reporting
- **Extensible Architecture**: Easy to extend with custom tool types
- **Category Organization**: Organize tools by category for better discovery

### Basic Usage

```typescript
import { ToolFactory, globalToolRegistry, z } from 'ag3ntic/tools';

// Create a simple tool
const textTool = ToolFactory.create(
  {
    name: 'text_transform',
    title: 'Text Transform',
    description: 'Transform text using various operations',
    category: 'text'
  },
  z.object({
    text: z.string(),
    operation: z.enum(['uppercase', 'lowercase', 'reverse'])
  }),
  async (input) => {
    const result = input.operation === 'uppercase' 
      ? input.text.toUpperCase()
      : input.operation === 'lowercase'
      ? input.text.toLowerCase()
      : input.text.split('').reverse().join('');
    
    return {
      success: true,
      data: { result },
      content: [{ type: 'text', text: result }]
    };
  }
);

// Register the tool
globalToolRegistry.register(textTool);

// Execute the tool
const result = await globalToolRegistry.execute('text_transform', {
  text: 'Hello World',
  operation: 'uppercase'
});
```

### Common Tools

The library includes several pre-built common tools:

- **Web Search Tool**: Search the web for information
- **URL Fetch Tool**: Fetch content from URLs
- **File Read/Write Tools**: File system operations
- **Directory List Tool**: List directory contents
- **Text Analysis Tool**: Analyze text for metrics
- **Text Transform Tool**: Transform text with various operations

### Tool Categories

- `WEB` - Web-related tools (search, fetch, etc.)
- `FILE` - File system operations
- `TEXT` - Text processing and analysis
- `API` - API integration tools
- `DATA` - Data processing tools
- `SYSTEM` - System operations
- `UTILITY` - General utility tools
- `AI` - AI-specific tools
- `CUSTOM` - Custom user tools

## MCP Library

### Features

- **MCP Server Implementation**: Create MCP-compliant servers
- **MCP Client Implementation**: Connect to external MCP servers
- **Transport Abstractions**: Support for stdio, HTTP, and SSE transports
- **AG3NTIC Integration**: Bidirectional conversion between AG3NTIC and MCP
- **Tool Exposure**: Expose AG3NTIC tools as MCP tools
- **External Tool Integration**: Use external MCP tools in AG3NTIC graphs

### Basic Usage

```typescript
import { MCP, createMCPServer } from 'ag3ntic/mcp';

// Create an MCP server
const server = createMCPServer('my-server', '1.0.0', 'stdio');

// Register a tool
server.registerTool({
  name: 'calculator',
  description: 'Perform basic math operations',
  inputSchema: {
    type: 'object',
    properties: {
      a: { type: 'number' },
      b: { type: 'number' },
      operation: { type: 'string', enum: ['add', 'subtract', 'multiply', 'divide'] }
    },
    required: ['a', 'b', 'operation']
  },
  handler: async (args) => {
    let result;
    switch (args.operation) {
      case 'add': result = args.a + args.b; break;
      case 'subtract': result = args.a - args.b; break;
      case 'multiply': result = args.a * args.b; break;
      case 'divide': result = args.a / args.b; break;
    }
    
    return {
      content: [{
        type: 'text',
        text: `${args.a} ${args.operation} ${args.b} = ${result}`
      }]
    };
  }
});

// Start the server
await server.start();
```

### AG3NTIC to MCP Integration

```typescript
import { createMCPServerFromAG3NTICTools } from 'ag3ntic/mcp';
import { globalToolRegistry } from 'ag3ntic/tools';

// Convert AG3NTIC tools to MCP server
const tools = globalToolRegistry.list();
const mcpServer = await createMCPServerFromAG3NTICTools(
  tools,
  'ag3ntic-tools-server',
  '1.0.0'
);

await mcpServer.start();
```

### MCP to AG3NTIC Integration

```typescript
import { createMCPClient, createAG3NTICToolsFromMCPClient } from 'ag3ntic/mcp';

// Connect to external MCP server
const client = createMCPClient('external-client', 'external-mcp-server');
await client.connect();

// Convert MCP tools to AG3NTIC tools
const ag3nticTools = await createAG3NTICToolsFromMCPClient(client);

// Register in AG3NTIC tool registry
ag3nticTools.forEach(tool => globalToolRegistry.register(tool));
```

## Integration with AG3NTIC Graphs

### Using Tools in Graph Nodes

```typescript
import { createToolNodeFromTool } from 'ag3ntic/tools';
import { Graph } from 'ag3ntic/core';

// Create a graph node from a tool
const textTransformNode = createToolNodeFromTool(textTool);

// Add to graph
const graph = new Graph();
graph.addNode('transform_text', textTransformNode);
```

### Using MCP Tools in Graphs

```typescript
import { createGraphNodesFromMCPClient } from 'ag3ntic/mcp';

// Create graph nodes from MCP client tools
const mcpNodes = await createGraphNodesFromMCPClient(client);

// Add to graph
Object.entries(mcpNodes).forEach(([name, node]) => {
  graph.addNode(name, node);
});
```

## Configuration

### Tool Configuration

```typescript
interface ToolConfig {
  name: string;           // Unique tool identifier
  title: string;          // Human-readable title
  description: string;    // Tool description
  category?: string;      // Tool category
  tags?: string[];        // Searchable tags
  enabled?: boolean;      // Whether tool is enabled
  version?: string;       // Tool version
  author?: string;        // Tool author
  examples?: ToolExample[]; // Usage examples
}
```

### MCP Server Configuration

```typescript
interface MCPServerConfig {
  name: string;
  version: string;
  description?: string;
  transport: {
    type: 'stdio' | 'http' | 'sse';
    options?: {
      // Transport-specific options
      command?: string;     // For stdio
      args?: string[];      // For stdio
      url?: string;         // For HTTP/SSE
      headers?: Record<string, string>;
    };
  };
  capabilities?: {
    tools?: boolean;
    resources?: boolean;
    prompts?: boolean;
    logging?: boolean;
  };
}
```

## Examples

See the `examples/` directory for complete working examples:

- `tool-library-demo.ts` - Tool library demonstration
- `mcp-integration-demo.ts` - MCP integration demonstration  
- `combined-demo.ts` - Combined demonstration of both libraries

## Running the Examples

```bash
# Run tool library demo
npx ts-node examples/tool-library-demo.ts

# Run MCP integration demo
npx ts-node examples/mcp-integration-demo.ts

# Run combined demo
npx ts-node examples/combined-demo.ts
```

## Architecture

### Tool Library Architecture

```
Tool Library
├── types.ts          # Core type definitions
├── base.ts           # Base tool classes
├── registry.ts       # Tool registry implementation
├── common/           # Common tool implementations
│   ├── web.ts        # Web-related tools
│   ├── file.ts       # File operations
│   └── text.ts       # Text processing
└── index.ts          # Main exports
```

### MCP Library Architecture

```
MCP Library
├── types.ts          # MCP type definitions
├── server.ts         # MCP server implementation
├── client.ts         # MCP client implementation
├── adapters.ts       # AG3NTIC ↔ MCP adapters
└── index.ts          # Main exports
```

## Benefits

### Tool Library Benefits

- **Standardization**: Consistent interface for all tools
- **Validation**: Automatic input/output validation
- **Discovery**: Easy tool discovery and management
- **Reusability**: Tools can be shared across projects
- **Extensibility**: Easy to add new tool types

### MCP Integration Benefits

- **Interoperability**: Work with any MCP-compliant system
- **Ecosystem**: Access to broader tool ecosystem
- **Standardization**: Industry-standard protocol
- **Scalability**: Distributed tool execution
- **Flexibility**: Multiple transport options

## Future Enhancements

- Real MCP transport implementations (stdio, HTTP, SSE)
- Tool marketplace and discovery features
- Advanced tool validation and testing
- Observability and logging capabilities
- Tool versioning and dependency management
- Performance optimization and caching
- Security and authentication features

## Contributing

To contribute new tools or MCP integrations:

1. Follow the existing patterns in `src/tools/common/`
2. Add comprehensive tests
3. Update documentation
4. Ensure TypeScript compliance
5. Add usage examples

The AG3NTIC Tool and MCP libraries provide a powerful foundation for building extensible, interoperable AI agent systems.
