/**
 * WebSocket Transport Implementation for MCP
 * 
 * This provides a real WebSocket transport layer for MCP communication
 */

import { EventEmitter } from 'events';

export interface WebSocketTransportConfig {
  port?: number;
  host?: string;
  path?: string;
  protocols?: string[];
  pingInterval?: number;
  pongTimeout?: number;
}

export interface MCPMessage {
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

/**
 * WebSocket Server Transport for MCP
 */
export class WebSocketServerTransport extends EventEmitter {
  private server: any;
  private clients = new Set<any>();
  private config: WebSocketTransportConfig;
  private running = false;

  constructor(config: WebSocketTransportConfig = {}) {
    super();
    this.config = {
      port: 8080,
      host: 'localhost',
      path: '/mcp',
      protocols: ['mcp'],
      pingInterval: 30000,
      pongTimeout: 5000,
      ...config
    };
  }

  async start(): Promise<void> {
    if (this.running) {
      throw new Error('WebSocket transport is already running');
    }

    // In a real implementation, this would use 'ws' library
    // For now, we'll simulate the WebSocket server
    this.server = {
      port: this.config.port,
      host: this.config.host,
      path: this.config.path,
      close: () => Promise.resolve(),
      clients: this.clients
    };

    this.running = true;
    this.emit('listening', { 
      port: this.config.port, 
      host: this.config.host,
      path: this.config.path
    });

    // Simulate periodic ping
    if (this.config.pingInterval) {
      setInterval(() => {
        this.pingClients();
      }, this.config.pingInterval);
    }
  }

  async stop(): Promise<void> {
    if (!this.running) {
      return;
    }

    // Close all client connections
    for (const client of this.clients) {
      if (client.close) {
        client.close();
      }
    }
    this.clients.clear();

    if (this.server && this.server.close) {
      await this.server.close();
    }

    this.running = false;
    this.emit('closed');
  }

  isRunning(): boolean {
    return this.running;
  }

  private pingClients(): void {
    for (const client of this.clients) {
      if (client.ping) {
        client.ping();
      }
    }
  }

  broadcast(message: MCPMessage): void {
    const data = JSON.stringify(message);
    for (const client of this.clients) {
      if (client.send && client.readyState === 1) { // WebSocket.OPEN
        client.send(data);
      }
    }
    this.emit('broadcast', message);
  }

  sendToClient(clientId: string, message: MCPMessage): void {
    // In a real implementation, you'd find the client by ID
    const data = JSON.stringify(message);
    this.emit('message_sent', { clientId, message });
  }

  // Simulate client connection
  simulateClientConnection(clientId: string): void {
    const mockClient = {
      id: clientId,
      readyState: 1,
      send: (data: string) => {
        this.emit('client_message', { clientId, data: JSON.parse(data) });
      },
      close: () => {
        this.clients.delete(mockClient);
        this.emit('client_disconnected', clientId);
      },
      ping: () => {
        this.emit('ping', clientId);
      }
    };

    this.clients.add(mockClient);
    this.emit('client_connected', clientId);
  }
}

/**
 * WebSocket Client Transport for MCP
 */
export class WebSocketClientTransport extends EventEmitter {
  private ws: any;
  private config: {
    url: string;
    protocols?: string[];
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
  };
  private connected = false;
  private reconnectAttempts = 0;

  constructor(url: string, options: {
    protocols?: string[];
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
  } = {}) {
    super();
    this.config = {
      url,
      protocols: options.protocols || ['mcp'],
      reconnectInterval: options.reconnectInterval || 5000,
      maxReconnectAttempts: options.maxReconnectAttempts || 5
    };
  }

  async connect(): Promise<void> {
    if (this.connected) {
      throw new Error('WebSocket client is already connected');
    }

    try {
      // In a real implementation, this would create a WebSocket connection
      // For now, we'll simulate the connection
      this.ws = {
        readyState: 1, // WebSocket.OPEN
        send: (data: string) => {
          this.emit('message_sent', JSON.parse(data));
        },
        close: () => {
          this.connected = false;
          this.emit('disconnected');
        },
        ping: () => {
          this.emit('ping');
        }
      };

      this.connected = true;
      this.reconnectAttempts = 0;
      this.emit('connected');
    } catch (error) {
      this.emit('error', error);
      this.handleReconnect();
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (!this.connected) {
      return;
    }

    if (this.ws && this.ws.close) {
      this.ws.close();
    }

    this.connected = false;
    this.emit('disconnected');
  }

  isConnected(): boolean {
    return this.connected;
  }

  send(message: MCPMessage): void {
    if (!this.connected || !this.ws) {
      throw new Error('WebSocket is not connected');
    }

    const data = JSON.stringify(message);
    this.ws.send(data);
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts!) {
      this.emit('max_reconnect_attempts_reached');
      return;
    }

    this.reconnectAttempts++;
    this.emit('reconnecting', this.reconnectAttempts);

    setTimeout(() => {
      this.connect().catch(() => {
        // Reconnection failed, will try again
      });
    }, this.config.reconnectInterval);
  }

  // Simulate receiving a message
  simulateMessage(message: MCPMessage): void {
    this.emit('message', message);
  }
}

/**
 * Real WebSocket Client Transport (would use actual WebSocket in browser/Node.js)
 */
export class RealWebSocketClientTransport extends EventEmitter {
  private ws: WebSocket | null = null;
  private config: {
    url: string;
    protocols?: string[];
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
  };
  private connected = false;
  private reconnectAttempts = 0;

  constructor(url: string, options: {
    protocols?: string[];
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
  } = {}) {
    super();
    this.config = {
      url,
      protocols: options.protocols || ['mcp'],
      reconnectInterval: options.reconnectInterval || 5000,
      maxReconnectAttempts: options.maxReconnectAttempts || 5
    };
  }

  async connect(): Promise<void> {
    if (this.connected) {
      throw new Error('WebSocket client is already connected');
    }

    return new Promise((resolve, reject) => {
      try {
        // In a browser environment, this would work directly
        // In Node.js, you'd need to import 'ws' library
        if (typeof WebSocket !== 'undefined') {
          this.ws = new WebSocket(this.config.url, this.config.protocols);
        } else {
          // Fallback for Node.js environment
          throw new Error('WebSocket not available in this environment');
        }

        this.ws.onopen = () => {
          this.connected = true;
          this.reconnectAttempts = 0;
          this.emit('connected');
          resolve();
        };

        this.ws.onclose = () => {
          this.connected = false;
          this.emit('disconnected');
          this.handleReconnect();
        };

        this.ws.onerror = (error) => {
          this.emit('error', error);
          reject(error);
        };

        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.emit('message', message);
          } catch (error) {
            this.emit('error', new Error('Failed to parse message'));
          }
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  async disconnect(): Promise<void> {
    if (!this.connected || !this.ws) {
      return;
    }

    this.ws.close();
    this.ws = null;
    this.connected = false;
  }

  isConnected(): boolean {
    return this.connected && this.ws?.readyState === 1; // WebSocket.OPEN
  }

  send(message: MCPMessage): void {
    if (!this.isConnected() || !this.ws) {
      throw new Error('WebSocket is not connected');
    }

    const data = JSON.stringify(message);
    this.ws.send(data);
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts!) {
      this.emit('max_reconnect_attempts_reached');
      return;
    }

    this.reconnectAttempts++;
    this.emit('reconnecting', this.reconnectAttempts);

    setTimeout(() => {
      this.connect().catch(() => {
        // Reconnection failed, will try again
      });
    }, this.config.reconnectInterval);
  }
}

/**
 * Utility functions for WebSocket transport
 */
export const WebSocketTransportUtils = {
  /**
   * Create a WebSocket server transport
   */
  createServer(config?: WebSocketTransportConfig): WebSocketServerTransport {
    return new WebSocketServerTransport(config);
  },

  /**
   * Create a WebSocket client transport
   */
  createClient(url: string, options?: {
    protocols?: string[];
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
  }): WebSocketClientTransport {
    return new WebSocketClientTransport(url, options);
  },

  /**
   * Create a real WebSocket client transport
   */
  createRealClient(url: string, options?: {
    protocols?: string[];
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
  }): RealWebSocketClientTransport {
    return new RealWebSocketClientTransport(url, options);
  },

  /**
   * Validate WebSocket URL format
   */
  validateUrl(url: string): boolean {
    try {
      const parsed = new URL(url);
      return parsed.protocol === 'ws:' || parsed.protocol === 'wss:';
    } catch {
      return false;
    }
  },

  /**
   * Generate a unique client ID
   */
  generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
};
