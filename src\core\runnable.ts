/**
 * Universal Runnable Interface for AG3NTIC
 * 
 * Inspired by <PERSON><PERSON><PERSON><PERSON>'s Runnable interface, this provides a unified
 * interface for all components in the AG3NTIC framework, enabling
 * composition, streaming, and configuration propagation.
 */



/**
 * Configuration object that propagates through the execution chain
 */
export interface RunnableConfig {
  /** Callbacks for monitoring execution */
  callbacks?: CallbackHandler[];
  /** Tags for filtering and identification */
  tags?: string[];
  /** Metadata for additional context */
  metadata?: Record<string, any>;
  /** Maximum concurrency for batch operations */
  maxConcurrency?: number;
  /** Timeout for operations in milliseconds */
  timeout?: number;
  /** Thread ID for conversation persistence */
  threadId?: string;
  /** Checkpoint ID for state restoration */
  checkpointId?: string;
  /** Custom configuration values */
  [key: string]: any;
}

/**
 * Callback handler interface for monitoring execution
 */
export interface CallbackHandler {
  onStart?(run: RunInfo): void | Promise<void>;
  onEnd?(run: RunInfo): void | Promise<void>;
  onError?(run: RunInfo, error: Error): void | Promise<void>;
  onStream?(run: RunInfo, chunk: any): void | Promise<void>;
}

/**
 * Information about a running operation
 */
export interface RunInfo {
  runId: string;
  name: string;
  type: string;
  startTime: number;
  endTime?: number;
  input: any;
  output?: any;
  error?: Error;
  tags: string[];
  metadata: Record<string, any>;
  parentRunId?: string;
}

/**
 * Universal Runnable interface that all AG3NTIC components implement
 */
export interface Runnable<TInput = any, TOutput = any> {
  /** Unique name for this runnable */
  name?: string;
  
  /** Execute with a single input */
  invoke(input: TInput, config?: RunnableConfig): Promise<TOutput>;
  
  /** Stream output as it's generated */
  stream(input: TInput, config?: RunnableConfig): AsyncGenerator<TOutput, void, unknown>;
  
  /** Process multiple inputs in parallel */
  batch(inputs: TInput[], config?: RunnableConfig): Promise<TOutput[]>;
  
  /** Compose with another runnable */
  pipe<TNext>(next: Runnable<TOutput, TNext>): RunnableSequence<TInput, TNext>;
  
  /** Add configuration to this runnable */
  withConfig(config: RunnableConfig): Runnable<TInput, TOutput>;
  
  /** Add callbacks to this runnable */
  withCallbacks(callbacks: CallbackHandler[]): Runnable<TInput, TOutput>;
  
  /** Add tags to this runnable */
  withTags(tags: string[]): Runnable<TInput, TOutput>;
  
  /** Add metadata to this runnable */
  withMetadata(metadata: Record<string, any>): Runnable<TInput, TOutput>;
}

/**
 * Base implementation of the Runnable interface
 */
export abstract class BaseRunnable<TInput = any, TOutput = any> implements Runnable<TInput, TOutput> {
  public name?: string;
  protected config: RunnableConfig;

  constructor(name?: string, config: RunnableConfig = {}) {
    if (name) {
      this.name = name;
    }
    this.config = config;
  }

  abstract invoke(input: TInput, config?: RunnableConfig): Promise<TOutput>;

  async *stream(input: TInput, config?: RunnableConfig): AsyncGenerator<TOutput, void, unknown> {
    // Default implementation: yield the full result
    // Subclasses can override for true streaming
    const result = await this.invoke(input, config);
    yield result;
  }

  async batch(inputs: TInput[], config?: RunnableConfig): Promise<TOutput[]> {
    const mergedConfig = this.mergeConfig(config);
    const maxConcurrency = mergedConfig.maxConcurrency || 10;
    
    const results: TOutput[] = [];
    const semaphore = new Semaphore(maxConcurrency);
    
    const promises = inputs.map(async (input, index) => {
      await semaphore.acquire();
      try {
        const result = await this.invoke(input, mergedConfig);
        results[index] = result;
      } finally {
        semaphore.release();
      }
    });
    
    await Promise.all(promises);
    return results;
  }

  pipe<TNext>(next: Runnable<TOutput, TNext>): RunnableSequence<TInput, TNext> {
    return new RunnableSequence([this, next]);
  }

  withConfig(config: RunnableConfig): Runnable<TInput, TOutput> {
    return new ConfiguredRunnable(this, config);
  }

  withCallbacks(callbacks: CallbackHandler[]): Runnable<TInput, TOutput> {
    return this.withConfig({ callbacks });
  }

  withTags(tags: string[]): Runnable<TInput, TOutput> {
    return this.withConfig({ tags });
  }

  withMetadata(metadata: Record<string, any>): Runnable<TInput, TOutput> {
    return this.withConfig({ metadata });
  }

  protected mergeConfig(config?: RunnableConfig): RunnableConfig {
    return {
      ...this.config,
      ...config,
      callbacks: [...(this.config.callbacks || []), ...(config?.callbacks || [])],
      tags: [...(this.config.tags || []), ...(config?.tags || [])],
      metadata: { ...this.config.metadata, ...config?.metadata }
    };
  }

  protected async executeWithCallbacks<T>(
    operation: () => Promise<T>,
    input: TInput,
    config?: RunnableConfig
  ): Promise<T> {
    const mergedConfig = this.mergeConfig(config);
    const runInfo: RunInfo = {
      runId: generateRunId(),
      name: this.name || this.constructor.name,
      type: 'runnable',
      startTime: Date.now(),
      input,
      tags: mergedConfig.tags || [],
      metadata: mergedConfig.metadata || {}
    };

    // Call onStart callbacks
    await this.callCallbacks('onStart', runInfo, mergedConfig);

    try {
      const result = await operation();
      runInfo.output = result;
      runInfo.endTime = Date.now();
      
      // Call onEnd callbacks
      await this.callCallbacks('onEnd', runInfo, mergedConfig);
      
      return result;
    } catch (error) {
      runInfo.error = error as Error;
      runInfo.endTime = Date.now();
      
      // Call onError callbacks
      await this.callCallbacks('onError', runInfo, mergedConfig, error as Error);
      
      throw error;
    }
  }

  private async callCallbacks(
    method: keyof CallbackHandler,
    runInfo: RunInfo,
    config: RunnableConfig,
    error?: Error
  ): Promise<void> {
    const callbacks = config.callbacks || [];
    
    await Promise.all(
      callbacks.map(async (callback) => {
        try {
          if (method === 'onError' && callback.onError) {
            await callback.onError(runInfo, error!);
          } else if (method === 'onStart' && callback.onStart) {
            await callback.onStart(runInfo);
          } else if (method === 'onEnd' && callback.onEnd) {
            await callback.onEnd(runInfo);
          }
        } catch (callbackError) {
          console.error(`Callback error in ${method}:`, callbackError);
        }
      })
    );
  }
}

/**
 * Runnable that wraps another runnable with additional configuration
 */
class ConfiguredRunnable<TInput, TOutput> extends BaseRunnable<TInput, TOutput> {
  constructor(
    private runnable: Runnable<TInput, TOutput>,
    config: RunnableConfig
  ) {
    super(runnable.name, config);
  }

  async invoke(input: TInput, config?: RunnableConfig): Promise<TOutput> {
    const mergedConfig = this.mergeConfig(config);
    return this.runnable.invoke(input, mergedConfig);
  }

  override async *stream(input: TInput, config?: RunnableConfig): AsyncGenerator<TOutput, void, unknown> {
    const mergedConfig = this.mergeConfig(config);
    yield* this.runnable.stream(input, mergedConfig);
  }
}

/**
 * Simple semaphore for controlling concurrency
 */
class Semaphore {
  private permits: number;
  private waiting: (() => void)[] = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return;
    }

    return new Promise((resolve) => {
      this.waiting.push(resolve);
    });
  }

  release(): void {
    this.permits++;
    const next = this.waiting.shift();
    if (next) {
      this.permits--;
      next();
    }
  }
}

/**
 * Generate a unique run ID
 */
function generateRunId(): string {
  return `run_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Runnable sequence for chaining operations
 */
export class RunnableSequence<TInput, TOutput> extends BaseRunnable<TInput, TOutput> {
  constructor(private runnables: Runnable<any, any>[]) {
    super(`RunnableSequence[${runnables.map(r => r.name || 'unnamed').join(' | ')}]`);
  }

  async invoke(input: TInput, config?: RunnableConfig): Promise<TOutput> {
    return this.executeWithCallbacks(async () => {
      let current: any = input;
      
      for (const runnable of this.runnables) {
        current = await runnable.invoke(current, config);
      }
      
      return current;
    }, input, config);
  }

  override async *stream(input: TInput, config?: RunnableConfig): AsyncGenerator<TOutput, void, unknown> {
    let current: any = input;
    
    // Execute all but the last runnable normally
    for (let i = 0; i < this.runnables.length - 1; i++) {
      current = await this.runnables[i].invoke(current, config);
    }
    
    // Stream the final runnable
    if (this.runnables.length > 0) {
      const lastRunnable = this.runnables[this.runnables.length - 1];
      yield* lastRunnable.stream(current, config);
    }
  }
}


