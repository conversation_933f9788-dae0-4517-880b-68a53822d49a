/**
 * AG3NTIC MCP Tools Demo
 * 
 * Comprehensive demonstration of the MCP tools library including
 * individual tool usage, collections, and real-world workflows.
 */

import { mcpToolsRegistry, MCPUtils } from '../src/tools/mcp/registry.js';
import { 
  createThinkingToolkit, 
  createResearchToolkit, 
  createDevelopmentToolkit,
  createAIAssistantToolkit 
} from '../src/tools/mcp/collections.js';
import { MCPToolCategory } from '../src/tools/mcp/types.js';

async function demonstrateSequentialThinking() {
  console.log('\n🧠 Sequential Thinking Tool Demo');
  console.log('==================================');

  const tool = mcpToolsRegistry.getTool('sequential_thinking');
  if (!tool) {
    console.log('❌ Sequential thinking tool not found');
    return;
  }

  const input = {
    problem: 'How can we improve the performance of our web application?',
    steps: 5,
    approach: 'analytical' as const,
    depth: 'medium' as const
  };

  console.log('🔍 Problem:', input.problem);
  console.log('📊 Analysis approach:', input.approach);
  console.log('🎯 Number of steps:', input.steps);

  try {
    const result = await tool.execute(input);
    
    if (result.success) {
      console.log('\n✅ Analysis completed successfully!');
      console.log('⏱️  Processing time:', result.data.metadata.processingTime + 'ms');
      console.log('🎯 Overall confidence:', (result.data.metadata.overallConfidence * 100).toFixed(1) + '%');
      
      console.log('\n📝 Thinking Steps:');
      result.data.thoughts.forEach((thought: any, index: number) => {
        console.log(`\n${index + 1}. ${thought.title}`);
        console.log(`   Confidence: ${(thought.confidence * 100).toFixed(1)}%`);
        console.log(`   Insights: ${thought.insights?.length || 0}`);
      });

      console.log('\n🎯 Key Insights:');
      result.data.synthesis.keyInsights.forEach((insight: string) => {
        console.log(`   • ${insight}`);
      });

      console.log('\n💡 Recommendations:');
      result.data.synthesis.recommendations?.forEach((rec: string) => {
        console.log(`   • ${rec}`);
      });
    } else {
      console.log('❌ Analysis failed:', result.error);
    }
  } catch (error) {
    console.log('❌ Error:', error);
  }
}

async function demonstrateContext7() {
  console.log('\n📚 Context7 Documentation Tool Demo');
  console.log('====================================');

  const tool = mcpToolsRegistry.getTool('context7');
  if (!tool) {
    console.log('❌ Context7 tool not found');
    return;
  }

  const input = {
    libraryName: 'react',
    topic: 'hooks',
    tokens: 15000,
    includeExamples: true,
    includeAPI: true
  };

  console.log('📖 Library:', input.libraryName);
  console.log('🎯 Topic:', input.topic);
  console.log('📊 Max tokens:', input.tokens);

  try {
    const result = await tool.execute(input);
    
    if (result.success) {
      console.log('\n✅ Documentation retrieved successfully!');
      console.log('📊 Tokens used:', result.data.metadata.tokensUsed);
      console.log('⏱️  Retrieval time:', result.data.metadata.retrievalTime + 'ms');
      console.log('🎯 Confidence:', (result.data.metadata.confidence * 100).toFixed(1) + '%');
      
      console.log('\n📋 Coverage:');
      const coverage = result.data.metadata.coverage;
      console.log(`   Overview: ${coverage.overview ? '✅' : '❌'}`);
      console.log(`   API: ${coverage.api ? '✅' : '❌'}`);
      console.log(`   Examples: ${coverage.examples ? '✅' : '❌'}`);
      console.log(`   Guides: ${coverage.guides ? '✅' : '❌'}`);

      if (result.data.documentation.examples) {
        console.log(`\n💡 Examples found: ${result.data.documentation.examples.length}`);
      }

      if (result.data.documentation.apiReference) {
        console.log(`📚 API references: ${result.data.documentation.apiReference.length}`);
      }
    } else {
      console.log('❌ Documentation retrieval failed:', result.error);
    }
  } catch (error) {
    console.log('❌ Error:', error);
  }
}

async function demonstrateWebSearch() {
  console.log('\n🔍 Web Search Tool Demo');
  console.log('========================');

  const tool = mcpToolsRegistry.getTool('web_search');
  if (!tool) {
    console.log('❌ Web search tool not found');
    return;
  }

  const input = {
    query: 'AI agent frameworks 2024',
    maxResults: 5,
    searchType: 'web' as const,
    includeSummary: true,
    timeRange: 'month' as const
  };

  console.log('🔍 Query:', input.query);
  console.log('📊 Max results:', input.maxResults);
  console.log('📅 Time range:', input.timeRange);

  try {
    const result = await tool.execute(input);
    
    if (result.success) {
      console.log('\n✅ Search completed successfully!');
      console.log('📊 Results found:', result.data.results.length);
      console.log('⏱️  Search time:', result.data.metadata.searchTime + 'ms');
      
      console.log('\n🔍 Top Results:');
      result.data.results.slice(0, 3).forEach((item: any, index: number) => {
        console.log(`\n${index + 1}. ${item.title}`);
        console.log(`   URL: ${item.url}`);
        console.log(`   Relevance: ${(item.score * 100).toFixed(1)}%`);
        if (item.snippet) {
          console.log(`   Snippet: ${item.snippet.substring(0, 100)}...`);
        }
      });

      if (result.data.summary) {
        console.log('\n📋 Summary:');
        console.log(`   ${result.data.summary.overview}`);
        console.log(`   Key points: ${result.data.summary.keyPoints.length}`);
        console.log(`   Related topics: ${result.data.summary.relatedTopics.length}`);
      }
    } else {
      console.log('❌ Search failed:', result.error);
    }
  } catch (error) {
    console.log('❌ Error:', error);
  }
}

async function demonstrateMemoryManagement() {
  console.log('\n🧠 Memory Management Tool Demo');
  console.log('===============================');

  const tool = mcpToolsRegistry.getTool('memory_management');
  if (!tool) {
    console.log('❌ Memory management tool not found');
    return;
  }

  console.log('💾 Storing conversation context...');

  // Store some data
  const storeInput = {
    operation: 'store' as const,
    key: 'user_preferences',
    value: {
      theme: 'dark',
      language: 'en',
      notifications: true
    },
    namespace: 'demo',
    ttl: 3600, // 1 hour
    metadata: {
      source: 'demo',
      importance: 'high'
    }
  };

  try {
    const storeResult = await tool.execute(storeInput);
    
    if (storeResult.success) {
      console.log('✅ Data stored successfully!');
      console.log('🔑 Total keys:', storeResult.data.metadata.totalKeys);
      
      // Retrieve the data
      console.log('\n📖 Retrieving stored data...');
      const retrieveInput = {
        operation: 'retrieve' as const,
        key: 'user_preferences',
        namespace: 'demo',
        options: {
          includeMetadata: true
        }
      };

      const retrieveResult = await tool.execute(retrieveInput);
      
      if (retrieveResult.success) {
        console.log('✅ Data retrieved successfully!');
        console.log('📊 Value:', JSON.stringify(retrieveResult.data.result.value, null, 2));
        console.log('⏰ Stored at:', retrieveResult.data.result.metadata.timestamp);
        console.log('⏳ Expires at:', retrieveResult.data.result.metadata.expiresAt);
      }

      // List all data in namespace
      console.log('\n📋 Listing all data in namespace...');
      const listInput = {
        operation: 'list' as const,
        namespace: 'demo',
        options: {
          includeMetadata: false,
          sortBy: 'timestamp' as const
        }
      };

      const listResult = await tool.execute(listInput);
      
      if (listResult.success) {
        console.log(`✅ Found ${listResult.data.result.length} items`);
        listResult.data.result.forEach((item: any) => {
          console.log(`   • ${item.key} (${item.size} bytes)`);
        });
      }
    } else {
      console.log('❌ Storage failed:', storeResult.error);
    }
  } catch (error) {
    console.log('❌ Error:', error);
  }
}

async function demonstrateTaskManagement() {
  console.log('\n📋 Task Management Tool Demo');
  console.log('=============================');

  const tool = mcpToolsRegistry.getTool('task_management');
  if (!tool) {
    console.log('❌ Task management tool not found');
    return;
  }

  console.log('📝 Creating a new task...');

  // Create a task
  const createInput = {
    operation: 'create' as const,
    task: {
      title: 'Implement MCP tools integration',
      description: 'Integrate the new MCP tools into the AG3NTIC framework',
      priority: 'high' as const,
      project: 'AG3NTIC',
      estimatedHours: 8,
      tags: ['development', 'mcp', 'integration'],
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 1 week from now
    }
  };

  try {
    const createResult = await tool.execute(createInput);
    
    if (createResult.success) {
      console.log('✅ Task created successfully!');
      console.log('🆔 Task ID:', createResult.data.id);
      console.log('📊 Priority:', createResult.data.priority);
      console.log('📅 Due date:', new Date(createResult.data.dueDate).toLocaleDateString());

      const taskId = createResult.data.id;

      // Update task status
      console.log('\n🔄 Updating task status...');
      const updateInput = {
        operation: 'update' as const,
        task: {
          id: taskId,
          status: 'in_progress' as const
        }
      };

      const updateResult = await tool.execute(updateInput);
      
      if (updateResult.success) {
        console.log('✅ Task updated successfully!');
        console.log('📊 New status:', updateResult.data.status);
        console.log('📈 Progress:', updateResult.data.progress + '%');
      }

      // List tasks
      console.log('\n📋 Listing all tasks...');
      const listInput = {
        operation: 'list' as const,
        options: {
          includeCompleted: false,
          sortBy: 'priority' as const,
          limit: 5
        }
      };

      const listResult = await tool.execute(listInput);
      
      if (listResult.success) {
        console.log(`✅ Found ${listResult.data.length} active tasks`);
        listResult.data.forEach((task: any, index: number) => {
          console.log(`\n${index + 1}. ${task.title}`);
          console.log(`   Status: ${task.status} | Priority: ${task.priority}`);
          console.log(`   Progress: ${task.progress}%`);
          if (task.dueDate) {
            console.log(`   Due: ${new Date(task.dueDate).toLocaleDateString()}`);
          }
        });
      }
    } else {
      console.log('❌ Task creation failed:', createResult.error);
    }
  } catch (error) {
    console.log('❌ Error:', error);
  }
}

async function demonstrateToolCollections() {
  console.log('\n🛠️  Tool Collections Demo');
  console.log('==========================');

  console.log('📦 Available Collections:');
  
  // Thinking toolkit
  const thinkingToolkit = createThinkingToolkit();
  console.log(`\n🧠 ${thinkingToolkit.name}`);
  console.log(`   Description: ${thinkingToolkit.description}`);
  console.log(`   Tools: ${thinkingToolkit.tools.length}`);
  console.log(`   Tools: ${thinkingToolkit.tools.map(t => t.config.name).join(', ')}`);

  // Research toolkit
  const researchToolkit = createResearchToolkit();
  console.log(`\n🔍 ${researchToolkit.name}`);
  console.log(`   Description: ${researchToolkit.description}`);
  console.log(`   Tools: ${researchToolkit.tools.length}`);
  console.log(`   Tools: ${researchToolkit.tools.map(t => t.config.name).join(', ')}`);

  // Development toolkit
  const devToolkit = createDevelopmentToolkit();
  console.log(`\n💻 ${devToolkit.name}`);
  console.log(`   Description: ${devToolkit.description}`);
  console.log(`   Tools: ${devToolkit.tools.length}`);
  console.log(`   Tools: ${devToolkit.tools.map(t => t.config.name).join(', ')}`);

  // AI Assistant toolkit
  const aiToolkit = createAIAssistantToolkit();
  console.log(`\n🤖 ${aiToolkit.name}`);
  console.log(`   Description: ${aiToolkit.description}`);
  console.log(`   Tools: ${aiToolkit.tools.length}`);
  console.log(`   Capabilities: ${Object.keys(aiToolkit.capabilities).filter(k => aiToolkit.capabilities[k as keyof typeof aiToolkit.capabilities]).join(', ')}`);
}

async function demonstrateRegistryFeatures() {
  console.log('\n📊 Registry Features Demo');
  console.log('=========================');

  // Get statistics
  const stats = MCPUtils.getStatistics();
  console.log('📈 Registry Statistics:');
  console.log(`   Total tools: ${stats.totalTools}`);
  console.log(`   Collections: ${stats.collections}`);
  
  console.log('\n📊 Tools by category:');
  Object.entries(stats.toolsByCategory).forEach(([category, count]) => {
    console.log(`   ${category}: ${count}`);
  });

  console.log('\n🔧 Tools by capability:');
  Object.entries(stats.toolsByCapability).forEach(([capability, count]) => {
    console.log(`   ${capability}: ${count}`);
  });

  // Search tools
  console.log('\n🔍 Search Results for "analysis":');
  const searchResults = MCPUtils.searchTools('analysis');
  searchResults.forEach(tool => {
    console.log(`   • ${tool.config.title} (${tool.config.name})`);
  });

  // Get tools by category
  console.log('\n🧠 Thinking Tools:');
  const thinkingTools = MCPUtils.getToolsByCategory(MCPToolCategory.THINKING);
  thinkingTools.forEach(tool => {
    console.log(`   • ${tool.config.title}`);
  });
}

async function main() {
  console.log('🚀 AG3NTIC MCP Tools Comprehensive Demo');
  console.log('=========================================');

  try {
    await demonstrateSequentialThinking();
    await demonstrateContext7();
    await demonstrateWebSearch();
    await demonstrateMemoryManagement();
    await demonstrateTaskManagement();
    await demonstrateToolCollections();
    await demonstrateRegistryFeatures();

    console.log('\n✅ All demos completed successfully!');
    console.log('\n🎉 The MCP tools library provides a comprehensive set of');
    console.log('   capabilities for building intelligent AI agents with');
    console.log('   thinking, memory, search, and productivity features.');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

// Run the demo if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export {
  demonstrateSequentialThinking,
  demonstrateContext7,
  demonstrateWebSearch,
  demonstrateMemoryManagement,
  demonstrateTaskManagement,
  demonstrateToolCollections,
  demonstrateRegistryFeatures
};
