#!/usr/bin/env node

/**
 * Build script for AG3NTIC npm package
 * 
 * Handles dual CommonJS/ESM builds and package preparation
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building AG3NTIC package...\n');

// Clean previous builds
console.log('🧹 Cleaning previous builds...');
try {
  execSync('rimraf dist dist-esm', { stdio: 'inherit' });
} catch (error) {
  console.warn('Warning: Could not clean previous builds');
}

// Build CommonJS version
console.log('📦 Building CommonJS version...');
try {
  execSync('tsc -p tsconfig.build.json', { stdio: 'inherit' });
  console.log('✅ CommonJS build complete');
} catch (error) {
  console.error('❌ CommonJS build failed');
  process.exit(1);
}

// Build ESM version
console.log('📦 Building ESM version...');
try {
  execSync('tsc -p tsconfig.esm.json', { stdio: 'inherit' });
  console.log('✅ ESM build complete');
} catch (error) {
  console.error('❌ ESM build failed');
  process.exit(1);
}

// Copy ESM files to dist with .esm.js extension
console.log('🔄 Processing ESM files...');
try {
  copyEsmFiles('dist-esm', 'dist');
  execSync('rimraf dist-esm', { stdio: 'inherit' });
  console.log('✅ ESM files processed');
} catch (error) {
  console.error('❌ ESM processing failed');
  process.exit(1);
}

// Create package.json files for submodules
console.log('📄 Creating submodule package.json files...');
createSubmodulePackageJsons();

console.log('\n🎉 Build complete! Package ready for publishing.');

/**
 * Copy ESM files and rename them with .esm.js extension
 */
function copyEsmFiles(srcDir, destDir) {
  if (!fs.existsSync(srcDir)) return;
  
  const files = fs.readdirSync(srcDir, { withFileTypes: true });
  
  for (const file of files) {
    const srcPath = path.join(srcDir, file.name);
    
    if (file.isDirectory()) {
      copyEsmFiles(srcPath, path.join(destDir, file.name));
    } else if (file.name.endsWith('.js')) {
      const destPath = path.join(destDir, file.name.replace('.js', '.esm.js'));
      fs.mkdirSync(path.dirname(destPath), { recursive: true });
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

/**
 * Create package.json files for submodules to support exports
 */
function createSubmodulePackageJsons() {
  const submodules = ['core', 'tools', 'orchestration'];
  
  for (const submodule of submodules) {
    const submoduleDir = path.join('dist', submodule);
    if (fs.existsSync(submoduleDir)) {
      const packageJson = {
        "main": "./index.js",
        "module": "./index.esm.js",
        "types": "./index.d.ts",
        "exports": {
          ".": {
            "import": "./index.esm.js",
            "require": "./index.js",
            "types": "./index.d.ts"
          }
        }
      };
      
      fs.writeFileSync(
        path.join(submoduleDir, 'package.json'),
        JSON.stringify(packageJson, null, 2)
      );
    }
  }
}
