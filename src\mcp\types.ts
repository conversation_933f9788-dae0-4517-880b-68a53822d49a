import type { Tool } from '../tools/types.js';

// Simple type definitions for AG3NTIC integration
export type NodeFunction<T> = (state: T) => Promise<T>;
export type AgentState = {
  messages?: Array<{
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string;
    tool_calls?: any;
  }>;
  [key: string]: any;
};

/**
 * MCP transport types
 */
export type MCPTransportType = 'stdio' | 'http' | 'sse' | 'streamable-http';

/**
 * MCP server configuration
 */
export interface MCPServerConfig {
  /** Server name */
  name: string;
  /** Server version */
  version: string;
  /** Server description */
  description?: string;
  /** Transport configuration */
  transport: MCPTransportConfig;
  /** Server capabilities */
  capabilities?: {
    tools?: boolean;
    resources?: boolean;
    prompts?: boolean;
    logging?: boolean;
  };
  /** Authentication configuration */
  auth?: MCPAuthConfig;
}

/**
 * MCP client configuration
 */
export interface MCPClientConfig {
  /** Client name */
  name: string;
  /** Client version */
  version: string;
  /** Transport configuration */
  transport: MCPTransportConfig;
  /** Connection timeout */
  timeout?: number;
  /** Retry configuration */
  retry?: {
    maxAttempts: number;
    delay: number;
  };
}

/**
 * MCP transport configuration
 */
export interface MCPTransportConfig {
  /** Transport type */
  type: MCPTransportType;
  /** Transport-specific options */
  options?: MCPTransportOptions;
}

/**
 * Transport-specific options
 */
export interface MCPTransportOptions {
  // Stdio options
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  
  // HTTP/SSE options
  url?: string;
  headers?: Record<string, string>;
  
  // Streamable HTTP options
  sessionIdGenerator?: () => string;
  enableDnsRebindingProtection?: boolean;
  allowedHosts?: string[];
  allowedOrigins?: string[];
}

/**
 * MCP authentication configuration
 */
export interface MCPAuthConfig {
  type: 'oauth' | 'bearer' | 'basic' | 'none';
  credentials?: {
    token?: string;
    username?: string;
    password?: string;
    clientId?: string;
    clientSecret?: string;
  };
}

/**
 * MCP tool definition
 */
export interface MCPToolDefinition {
  /** Tool name */
  name: string;
  /** Tool description */
  description: string;
  /** Input schema */
  inputSchema: any;
  /** Tool handler */
  handler: MCPToolHandler;
}

/**
 * MCP tool handler function
 */
export type MCPToolHandler = (args: any) => Promise<MCPToolResult>;

/**
 * MCP tool result
 */
export interface MCPToolResult {
  /** Result content */
  content: Array<{
    type: 'text' | 'image' | 'resource_link';
    text?: string;
    uri?: string;
    name?: string;
    mimeType?: string;
    description?: string;
  }>;
  /** Whether this is an error */
  isError?: boolean;
}

/**
 * MCP resource definition
 */
export interface MCPResourceDefinition {
  /** Resource URI or template */
  uri: string;
  /** Resource name */
  name: string;
  /** Resource description */
  description: string;
  /** MIME type */
  mimeType?: string;
  /** Resource handler */
  handler: MCPResourceHandler;
}

/**
 * MCP resource handler function
 */
export type MCPResourceHandler = (uri: string, params?: any) => Promise<MCPResourceResult>;

/**
 * MCP resource result
 */
export interface MCPResourceResult {
  /** Resource contents */
  contents: Array<{
    uri: string;
    text?: string;
    blob?: Uint8Array;
    mimeType?: string;
  }>;
}

/**
 * MCP prompt definition
 */
export interface MCPPromptDefinition {
  /** Prompt name */
  name: string;
  /** Prompt description */
  description: string;
  /** Prompt arguments schema */
  argsSchema?: any;
  /** Prompt handler */
  handler: MCPPromptHandler;
}

/**
 * MCP prompt handler function
 */
export type MCPPromptHandler = (args: any) => Promise<MCPPromptResult>;

/**
 * MCP prompt result
 */
export interface MCPPromptResult {
  /** Prompt description */
  description?: string;
  /** Prompt messages */
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: {
      type: 'text' | 'image';
      text?: string;
      uri?: string;
    };
  }>;
}

/**
 * MCP server interface
 */
export interface MCPServerInterface {
  /** Underlying MCP server instance */
  server: any;
  /** Server configuration */
  config: MCPServerConfig;
  /** Register a tool */
  registerTool(tool: MCPToolDefinition): void;
  /** Register a resource */
  registerResource(resource: MCPResourceDefinition): void;
  /** Register a prompt */
  registerPrompt(prompt: MCPPromptDefinition): void;
  /** Start the server */
  start(): Promise<void>;
  /** Stop the server */
  stop(): Promise<void>;
  /** Check if server is running */
  isRunning(): boolean;
}

/**
 * MCP client interface
 */
export interface MCPClientInterface {
  /** Underlying MCP client instance */
  client: any;
  /** Client configuration */
  config: MCPClientConfig;
  /** Connect to server */
  connect(): Promise<void>;
  /** Disconnect from server */
  disconnect(): Promise<void>;
  /** Check if connected */
  isConnected(): boolean;
  /** List available tools */
  listTools(): Promise<Array<{ name: string; description: string; inputSchema: any }>>;
  /** Call a tool */
  callTool(name: string, args: any): Promise<MCPToolResult>;
  /** List available resources */
  listResources(): Promise<Array<{ uri: string; name: string; description: string; mimeType?: string }>>;
  /** Read a resource */
  readResource(uri: string): Promise<MCPResourceResult>;
  /** List available prompts */
  listPrompts(): Promise<Array<{ name: string; description: string; argsSchema?: any }>>;
  /** Get a prompt */
  getPrompt(name: string, args?: any): Promise<MCPPromptResult>;
}

/**
 * AG3NTIC to MCP adapter interface
 */
export interface AG3NTICToMCPAdapter {
  /** Convert AG3NTIC tool to MCP tool */
  convertTool(tool: Tool): MCPToolDefinition;
  /** Convert AG3NTIC graph node to MCP tool */
  convertGraphNode(name: string, node: NodeFunction<AgentState>): MCPToolDefinition;
  /** Create MCP server from AG3NTIC tools */
  createServerFromTools(tools: Tool[], config: MCPServerConfig): MCPServerInterface;
}

/**
 * MCP to AG3NTIC adapter interface
 */
export interface MCPToAG3NTICAdapter {
  /** Convert MCP tool to AG3NTIC graph node */
  convertToolToNode(client: MCPClientInterface, toolName: string): NodeFunction<AgentState>;
  /** Convert MCP resource to AG3NTIC graph node */
  convertResourceToNode(client: MCPClientInterface, resourceUri: string): NodeFunction<AgentState>;
  /** Create AG3NTIC tools from MCP client */
  createToolsFromClient(client: MCPClientInterface): Promise<Tool[]>;
}

/**
 * MCP session state
 */
export interface MCPSessionState {
  /** Session ID */
  sessionId: string;
  /** Connected clients */
  clients: Set<string>;
  /** Session metadata */
  metadata: Record<string, any>;
  /** Session start time */
  startTime: Date;
  /** Last activity time */
  lastActivity: Date;
}

/**
 * MCP event types
 */
export type MCPEventType = 
  | 'server_started'
  | 'server_stopped'
  | 'client_connected'
  | 'client_disconnected'
  | 'tool_called'
  | 'resource_accessed'
  | 'prompt_requested'
  | 'error';

/**
 * MCP event data
 */
export interface MCPEvent {
  /** Event type */
  type: MCPEventType;
  /** Event timestamp */
  timestamp: Date;
  /** Event data */
  data: any;
  /** Session ID (if applicable) */
  sessionId?: string;
  /** Client ID (if applicable) */
  clientId?: string;
}

/**
 * MCP event listener
 */
export type MCPEventListener = (event: MCPEvent) => void | Promise<void>;

/**
 * MCP registry for managing servers and clients
 */
export interface MCPRegistry {
  /** Register a server */
  registerServer(id: string, server: MCPServerInterface): void;
  /** Unregister a server */
  unregisterServer(id: string): void;
  /** Get a server */
  getServer(id: string): MCPServerInterface | undefined;
  /** List all servers */
  listServers(): Array<{ id: string; server: MCPServerInterface }>;
  /** Register a client */
  registerClient(id: string, client: MCPClientInterface): void;
  /** Unregister a client */
  unregisterClient(id: string): void;
  /** Get a client */
  getClient(id: string): MCPClientInterface | undefined;
  /** List all clients */
  listClients(): Array<{ id: string; client: MCPClientInterface }>;
}
