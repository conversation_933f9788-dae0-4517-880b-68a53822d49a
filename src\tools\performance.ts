/**
 * Performance optimization utilities for AG3NTIC Tool Library
 */

import { Tool, ToolExecutionResult, ToolContext } from './types.js';

/**
 * Tool execution cache
 */
export class ToolCache {
  private cache = new Map<string, {
    result: ToolExecutionResult;
    timestamp: number;
    ttl: number;
  }>();

  private defaultTTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get cached result if available and not expired
   */
  get(key: string): ToolExecutionResult | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.result;
  }

  /**
   * Set cached result
   */
  set(key: string, result: ToolExecutionResult, ttl?: number): void {
    this.cache.set(key, {
      result,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    });
  }

  /**
   * Clear expired entries
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, cached] of this.cache.entries()) {
      if (now - cached.timestamp > cached.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    hitRate: number;
    totalRequests: number;
    cacheHits: number;
  } {
    // In a real implementation, you'd track hit/miss statistics
    return {
      size: this.cache.size,
      hitRate: 0,
      totalRequests: 0,
      cacheHits: 0
    };
  }
}

/**
 * Tool execution pool for managing concurrent executions
 */
export class ToolExecutionPool {
  private activeExecutions = new Map<string, Promise<ToolExecutionResult>>();
  private maxConcurrent: number;
  private queue: Array<{
    tool: Tool;
    input: any;
    context: ToolContext | undefined;
    resolve: (result: ToolExecutionResult) => void;
    reject: (error: Error) => void;
  }> = [];

  constructor(maxConcurrent: number = 10) {
    this.maxConcurrent = maxConcurrent;
  }

  /**
   * Execute tool with concurrency control
   */
  async execute(tool: Tool, input: any, context?: ToolContext): Promise<ToolExecutionResult> {
    const executionKey = `${tool.config.name}_${Date.now()}_${Math.random()}`;

    // If we're at max capacity, queue the execution
    if (this.activeExecutions.size >= this.maxConcurrent) {
      return new Promise((resolve, reject) => {
        this.queue.push({ tool, input, context, resolve, reject });
      });
    }

    return this.executeNow(tool, input, context, executionKey);
  }

  private async executeNow(
    tool: Tool,
    input: any,
    context?: ToolContext,
    executionKey?: string
  ): Promise<ToolExecutionResult> {
    const key = executionKey || `${tool.config.name}_${Date.now()}_${Math.random()}`;
    
    const execution = tool.execute(input, context);
    this.activeExecutions.set(key, execution);

    try {
      const result = await execution;
      return result;
    } finally {
      this.activeExecutions.delete(key);
      this.processQueue();
    }
  }

  private processQueue(): void {
    if (this.queue.length === 0 || this.activeExecutions.size >= this.maxConcurrent) {
      return;
    }

    const next = this.queue.shift();
    if (next) {
      this.executeNow(next.tool, next.input, next.context)
        .then(next.resolve)
        .catch(next.reject);
    }
  }

  /**
   * Get pool statistics
   */
  getStats(): {
    activeExecutions: number;
    queuedExecutions: number;
    maxConcurrent: number;
  } {
    return {
      activeExecutions: this.activeExecutions.size,
      queuedExecutions: this.queue.length,
      maxConcurrent: this.maxConcurrent
    };
  }
}

/**
 * Performance monitoring for tools
 */
export class ToolPerformanceMonitor {
  private metrics = new Map<string, {
    totalExecutions: number;
    totalTime: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    errors: number;
    lastExecution: number;
  }>();

  /**
   * Record tool execution metrics
   */
  recordExecution(
    toolName: string,
    executionTime: number,
    success: boolean
  ): void {
    const existing = this.metrics.get(toolName) || {
      totalExecutions: 0,
      totalTime: 0,
      averageTime: 0,
      minTime: Infinity,
      maxTime: 0,
      errors: 0,
      lastExecution: 0
    };

    existing.totalExecutions++;
    existing.totalTime += executionTime;
    existing.averageTime = existing.totalTime / existing.totalExecutions;
    existing.minTime = Math.min(existing.minTime, executionTime);
    existing.maxTime = Math.max(existing.maxTime, executionTime);
    existing.lastExecution = Date.now();

    if (!success) {
      existing.errors++;
    }

    this.metrics.set(toolName, existing);
  }

  /**
   * Get performance metrics for a tool
   */
  getMetrics(toolName: string) {
    return this.metrics.get(toolName);
  }

  /**
   * Get all performance metrics
   */
  getAllMetrics() {
    return Object.fromEntries(this.metrics.entries());
  }

  /**
   * Get performance summary
   */
  getSummary(): {
    totalTools: number;
    totalExecutions: number;
    averageExecutionTime: number;
    errorRate: number;
    slowestTool: string | null;
    fastestTool: string | null;
  } {
    const allMetrics = Array.from(this.metrics.values());
    
    if (allMetrics.length === 0) {
      return {
        totalTools: 0,
        totalExecutions: 0,
        averageExecutionTime: 0,
        errorRate: 0,
        slowestTool: null,
        fastestTool: null
      };
    }

    const totalExecutions = allMetrics.reduce((sum, m) => sum + m.totalExecutions, 0);
    const totalTime = allMetrics.reduce((sum, m) => sum + m.totalTime, 0);
    const totalErrors = allMetrics.reduce((sum, m) => sum + m.errors, 0);

    let slowestTool: string | null = null;
    let fastestTool: string | null = null;
    let maxAvgTime = 0;
    let minAvgTime = Infinity;

    for (const [toolName, metrics] of this.metrics.entries()) {
      if (metrics.averageTime > maxAvgTime) {
        maxAvgTime = metrics.averageTime;
        slowestTool = toolName;
      }
      if (metrics.averageTime < minAvgTime) {
        minAvgTime = metrics.averageTime;
        fastestTool = toolName;
      }
    }

    return {
      totalTools: this.metrics.size,
      totalExecutions,
      averageExecutionTime: totalExecutions > 0 ? totalTime / totalExecutions : 0,
      errorRate: totalExecutions > 0 ? totalErrors / totalExecutions : 0,
      slowestTool,
      fastestTool
    };
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.metrics.clear();
  }
}

/**
 * Optimized tool wrapper that adds caching and monitoring
 */
export class OptimizedTool implements Tool {
  private tool: Tool;
  private cache: ToolCache;
  private monitor: ToolPerformanceMonitor;
  private cacheEnabled: boolean;

  constructor(
    tool: Tool,
    options: {
      cache?: ToolCache;
      monitor?: ToolPerformanceMonitor;
      cacheEnabled?: boolean;
    } = {}
  ) {
    this.tool = tool;
    this.cache = options.cache || new ToolCache();
    this.monitor = options.monitor || new ToolPerformanceMonitor();
    this.cacheEnabled = options.cacheEnabled ?? true;
  }

  get config() {
    return this.tool.config;
  }

  get inputSchema() {
    return this.tool.inputSchema;
  }

  get outputSchema() {
    return this.tool.outputSchema;
  }

  validateInput(input: any) {
    return this.tool.validateInput(input);
  }

  getMetadata() {
    return this.tool.getMetadata();
  }

  async execute(input: any, context?: ToolContext): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    // Generate cache key
    const cacheKey = this.generateCacheKey(input, context);
    
    // Check cache first
    if (this.cacheEnabled) {
      const cached = this.cache.get(cacheKey);
      if (cached) {
        this.monitor.recordExecution(this.tool.config.name, Date.now() - startTime, cached.success);
        return cached;
      }
    }

    try {
      // Execute the tool
      const result = await this.tool.execute(input, context);
      
      // Cache successful results
      if (this.cacheEnabled && result.success) {
        this.cache.set(cacheKey, result);
      }

      // Record metrics
      this.monitor.recordExecution(this.tool.config.name, Date.now() - startTime, result.success);
      
      return result;
    } catch (error) {
      // Record error metrics
      this.monitor.recordExecution(this.tool.config.name, Date.now() - startTime, false);
      throw error;
    }
  }

  private generateCacheKey(input: any, context?: ToolContext): string {
    // Simple cache key generation - in production, you'd want a more sophisticated approach
    const inputHash = JSON.stringify(input);
    const contextHash = context ? JSON.stringify(context) : '';
    return `${this.tool.config.name}:${inputHash}:${contextHash}`;
  }

  /**
   * Get performance metrics for this tool
   */
  getPerformanceMetrics() {
    return this.monitor.getMetrics(this.tool.config.name);
  }

  /**
   * Clear cache for this tool
   */
  clearCache(): void {
    this.cache.clear();
  }
}

/**
 * Global performance optimization utilities
 */
export const PerformanceUtils = {
  cache: new ToolCache(),
  monitor: new ToolPerformanceMonitor(),
  pool: new ToolExecutionPool(),

  /**
   * Wrap a tool with performance optimizations
   */
  optimize(tool: Tool, options?: {
    cacheEnabled?: boolean;
    cacheTTL?: number;
  }): OptimizedTool {
    return new OptimizedTool(tool, {
      cache: this.cache,
      monitor: this.monitor,
      cacheEnabled: options?.cacheEnabled || false
    });
  },

  /**
   * Get global performance summary
   */
  getGlobalSummary() {
    return {
      cache: this.cache.getStats(),
      monitor: this.monitor.getSummary(),
      pool: this.pool.getStats()
    };
  },

  /**
   * Cleanup expired cache entries
   */
  cleanup() {
    this.cache.cleanup();
  },

  /**
   * Reset all performance data
   */
  reset() {
    this.cache.clear();
    this.monitor.reset();
  }
};
