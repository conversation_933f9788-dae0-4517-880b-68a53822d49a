# Contributing to AG3NTIC

Thank you for your interest in contributing to AG3NTIC! This document provides guidelines and information for contributors.

## 🎯 Our Mission

AG3NTIC aims to provide **Powerful Simplicity** for agentic workflows. Every contribution should align with our core principles:

1. **Clarity Over Cleverness** - Code should be explicit and easy to understand
2. **Minimize Boilerplate** - Reduce repetitive code for common patterns
3. **Strongly-Typed** - Leverage TypeScript's type system fully
4. **Unopinionated Core** - Keep the core flexible and extensible
5. **No Hidden State** - Everything should be transparent and debuggable
6. **Stateless Nodes** - Promote pure, testable functions
7. **Fail Fast and Loud** - Provide clear, actionable error messages

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- TypeScript knowledge

### Setup

1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/your-username/ag3ntic.git
   cd ag3ntic
   ```
3. Install dependencies:
   ```bash
   npm install
   ```
4. Run tests to ensure everything works:
   ```bash
   npm test
   ```

## 🛠️ Development Workflow

### Running Examples
```bash
npm run example:weather      # Simple weather agent
npm run example:advanced     # Multi-tool agent
```

### Building
```bash
npm run build
```

### Testing
```bash
npm test                     # Run all tests
npm test -- --watch         # Run tests in watch mode
npm test -- --coverage      # Run tests with coverage
```

### Linting and Formatting
```bash
npm run lint                 # Check code style
npm run format               # Format code
```

## 📝 Code Style

### TypeScript Guidelines

- Use strict TypeScript settings
- Prefer explicit types over `any`
- Use generics for reusable components
- Document public APIs with JSDoc comments

### Naming Conventions

- **Files**: kebab-case (`my-file.ts`)
- **Classes**: PascalCase (`MyClass`)
- **Functions/Variables**: camelCase (`myFunction`)
- **Constants**: UPPER_SNAKE_CASE (`MY_CONSTANT`)
- **Interfaces**: PascalCase with descriptive names (`AgentState`)

### Code Organization

- Keep files focused and small
- Export from index files for clean imports
- Group related functionality together
- Separate core framework from examples/helpers

## 🧪 Testing Guidelines

### Test Structure

- Use Jest for testing
- Place tests in `__tests__` directories
- Name test files with `.test.ts` suffix
- Test both happy paths and error cases

### Writing Good Tests

```typescript
describe('FeatureName', () => {
  describe('specific behavior', () => {
    it('should do something specific', () => {
      // Arrange
      const input = createTestInput();
      
      // Act
      const result = functionUnderTest(input);
      
      // Assert
      expect(result).toEqual(expectedOutput);
    });
  });
});
```

### Test Coverage

- Aim for high test coverage on core functionality
- Focus on testing public APIs
- Test error conditions and edge cases
- Examples don't need exhaustive testing

## 📚 Documentation

### Code Documentation

- Use JSDoc for public APIs
- Include parameter descriptions and return types
- Provide usage examples for complex functions
- Document error conditions

### README Updates

- Update README.md for new features
- Include code examples
- Update the table of contents if needed

## 🔄 Pull Request Process

### Before Submitting

1. Ensure all tests pass
2. Add tests for new functionality
3. Update documentation as needed
4. Run linting and fix any issues
5. Verify examples still work

### PR Guidelines

1. **Title**: Use clear, descriptive titles
2. **Description**: Explain what changes and why
3. **Breaking Changes**: Clearly mark any breaking changes
4. **Testing**: Describe how you tested the changes

### Review Process

- All PRs require review from maintainers
- Address feedback promptly
- Keep PRs focused and reasonably sized
- Be responsive to questions and suggestions

## 🐛 Bug Reports

### Before Reporting

1. Check existing issues
2. Try to reproduce with minimal example
3. Test with latest version

### Bug Report Template

```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Create a graph with...
2. Execute with state...
3. See error

**Expected behavior**
What you expected to happen.

**Code Example**
```typescript
// Minimal code example that reproduces the issue
```

**Environment**
- AG3NTIC version:
- Node.js version:
- TypeScript version:
- OS:
```

## 💡 Feature Requests

### Before Requesting

1. Check if feature aligns with AG3NTIC principles
2. Consider if it belongs in core vs. helper library
3. Look for existing similar requests

### Feature Request Template

```markdown
**Feature Description**
Clear description of the proposed feature.

**Use Case**
Describe the problem this solves.

**Proposed API**
```typescript
// Example of how the feature might be used
```

**Alternatives Considered**
Other approaches you've considered.

**Additional Context**
Any other relevant information.
```

## 🏗️ Architecture Guidelines

### Core vs. Helpers

- **Core**: Essential graph execution, state management
- **Helpers**: Convenience functions, common patterns
- **Examples**: Demonstrations, not part of the API

### Adding New Features

1. Start with the core abstractions
2. Ensure it's generic and reusable
3. Add type-safe helpers for common cases
4. Provide clear examples
5. Document thoroughly

### Breaking Changes

- Avoid breaking changes in minor versions
- Provide migration guides for major versions
- Deprecate before removing features
- Consider backwards compatibility

## 📞 Getting Help

- **Discussions**: Use GitHub Discussions for questions
- **Issues**: Use GitHub Issues for bugs and features
- **Discord**: Join our community Discord (link in README)

## 📄 License

By contributing to AG3NTIC, you agree that your contributions will be licensed under the MIT License.

---

Thank you for helping make AG3NTIC better! 🚀
