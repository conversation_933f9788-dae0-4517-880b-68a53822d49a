import { z } from 'zod';

/**
 * Base tool configuration interface
 */
export interface ToolConfig {
  /** Unique identifier for the tool */
  name: string;
  /** Human-readable title */
  title: string;
  /** Description of what the tool does */
  description: string;
  /** Category for organization */
  category?: string;
  /** Tags for searchability */
  tags?: string[];
  /** Whether the tool is enabled */
  enabled?: boolean;
  /** Tool version */
  version?: string;
  /** Author information */
  author?: string;
  /** Usage examples */
  examples?: ToolExample[];
}

/**
 * Tool example for documentation
 */
export interface ToolExample {
  /** Example description */
  description: string;
  /** Input parameters */
  input: Record<string, any>;
  /** Expected output */
  output?: any;
}

/**
 * Tool execution context
 */
export interface ToolContext {
  /** Current state */
  state?: Record<string, any>;
  /** Previous messages */
  messages?: any[];
  /** User ID or session ID */
  userId?: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
}

/**
 * Tool execution result
 */
export interface ToolExecutionResult {
  /** Whether execution was successful */
  success: boolean;
  /** Result data */
  data?: any;
  /** Error message if failed */
  error?: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
  /** Content for LLM response */
  content?: Array<{
    type: 'text' | 'image' | 'resource_link';
    text?: string;
    uri?: string;
    name?: string;
    mimeType?: string;
    description?: string;
  }>;
}

/**
 * Base tool interface that all tools must implement
 */
export interface Tool<TInput = any, TOutput = any> {
  /** Tool configuration */
  config: ToolConfig;
  
  /** Input validation schema */
  inputSchema: z.ZodSchema<TInput>;
  
  /** Output validation schema (optional) */
  outputSchema?: z.ZodSchema<TOutput>;
  
  /**
   * Execute the tool with given input
   */
  execute(input: TInput, context?: ToolContext): Promise<ToolExecutionResult>;
  
  /**
   * Validate input parameters
   */
  validateInput(input: any): { valid: boolean; error?: string; data?: TInput };
  
  /**
   * Get tool metadata for registration
   */
  getMetadata(): ToolMetadata;
}

/**
 * Tool metadata for registration and discovery
 */
export interface ToolMetadata {
  name: string;
  title: string;
  description: string;
  category?: string;
  tags?: string[];
  inputSchema: any;
  outputSchema?: any;
  examples?: ToolExample[];
  version?: string;
  author?: string;
  enabled: boolean;
}

/**
 * Tool registry interface
 */
export interface ToolRegistry {
  /**
   * Register a tool
   */
  register(tool: Tool): void;
  
  /**
   * Unregister a tool
   */
  unregister(name: string): void;
  
  /**
   * Get a tool by name
   */
  get(name: string): Tool | undefined;
  
  /**
   * List all registered tools
   */
  list(): ToolMetadata[];
  
  /**
   * Search tools by category or tags
   */
  search(query: { category?: string; tags?: string[]; enabled?: boolean }): ToolMetadata[];
  
  /**
   * Execute a tool by name
   */
  execute(name: string, input: any, context?: ToolContext): Promise<ToolExecutionResult>;
}

/**
 * Tool factory interface for creating tools
 */
export interface ToolFactory<TInput = any, TOutput = any> {
  /**
   * Create a tool with the given configuration
   */
  create(config: ToolConfig, handler: ToolHandler<TInput, TOutput>): Tool<TInput, TOutput>;
}

/**
 * Tool handler function type
 */
export type ToolHandler<TInput = any, TOutput = any> = (
  input: TInput,
  context?: ToolContext
) => Promise<ToolExecutionResult>;

/**
 * Tool validation result
 */
export interface ToolValidationResult<T = any> {
  valid: boolean;
  error?: string;
  data?: T;
}

/**
 * Common tool categories
 */
export enum ToolCategory {
  WEB = 'web',
  FILE = 'file',
  API = 'api',
  TEXT = 'text',
  DATA = 'data',
  SYSTEM = 'system',
  COMMUNICATION = 'communication',
  UTILITY = 'utility',
  AI = 'ai',
  CUSTOM = 'custom'
}

/**
 * Tool execution options
 */
export interface ToolExecutionOptions {
  /** Timeout in milliseconds */
  timeout?: number;
  /** Maximum retries */
  maxRetries?: number;
  /** Retry delay in milliseconds */
  retryDelay?: number;
  /** Whether to validate input */
  validateInput?: boolean;
  /** Whether to validate output */
  validateOutput?: boolean;
}
