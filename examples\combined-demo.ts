/**
 * AG3NTIC Tool & MCP Libraries Combined Demo
 * 
 * This demonstrates both the tool library and MCP integration
 * working together in the AG3NTIC framework.
 */

import { demonstrateToolLibrary } from './tool-library-demo';
import { demonstrateMCPIntegration } from './mcp-integration-demo';

async function runCombinedDemo() {
  console.log('🚀 AG3NTIC Tool & MCP Libraries Combined Demo');
  console.log('='.repeat(60));
  console.log();

  try {
    // Run tool library demo
    await demonstrateToolLibrary();
    
    console.log('\n' + '='.repeat(60));
    console.log();
    
    // Run MCP integration demo
    await demonstrateMCPIntegration();
    
    console.log('\n' + '='.repeat(60));
    console.log();
    
    // Summary
    console.log('📋 Combined Demo Summary');
    console.log();
    console.log('✅ Tool Library Features:');
    console.log('  • Structured tool definitions with Zod validation');
    console.log('  • Tool registry for discovery and management');
    console.log('  • Extensible tool architecture');
    console.log('  • Error handling and result formatting');
    console.log('  • Category-based organization');
    console.log();
    
    console.log('✅ MCP Integration Features:');
    console.log('  • Model Context Protocol server implementation');
    console.log('  • Client-server communication');
    console.log('  • Tool registration and execution via MCP');
    console.log('  • AG3NTIC to MCP tool conversion');
    console.log('  • Bidirectional integration capabilities');
    console.log();
    
    console.log('🔗 Integration Benefits:');
    console.log('  • AG3NTIC tools can be exposed as MCP tools');
    console.log('  • External MCP tools can be used in AG3NTIC graphs');
    console.log('  • Standardized tool interface across systems');
    console.log('  • Enhanced interoperability with other AI systems');
    console.log('  • Scalable tool ecosystem');
    console.log();
    
    console.log('🎯 Use Cases:');
    console.log('  • Building reusable tool libraries');
    console.log('  • Integrating with external services via MCP');
    console.log('  • Creating tool marketplaces');
    console.log('  • Sharing tools between different AI frameworks');
    console.log('  • Building complex multi-agent systems');
    console.log();
    
    console.log('🚀 Next Steps:');
    console.log('  • Implement real MCP transport layers (stdio, HTTP, SSE)');
    console.log('  • Add more common tools (web search, file ops, APIs)');
    console.log('  • Create tool validation and testing utilities');
    console.log('  • Build tool discovery and marketplace features');
    console.log('  • Add observability and logging capabilities');
    console.log();
    
    console.log('🎉 AG3NTIC Tool & MCP Libraries are ready for use!');
    
  } catch (error) {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  }
}

// Run the combined demo
if (require.main === module) {
  runCombinedDemo().catch(console.error);
}

export { runCombinedDemo };
