import { describe, test, expect, beforeEach } from '@jest/globals';

/**
 * MCP Tools Test Suite
 * 
 * Comprehensive tests for the MCP tools library including individual tools,
 * registry functionality, and tool collections.
 */

import { mcpToolsRegistry, MCPUtils } from '../src/tools/mcp/registry.js';
import { SequentialThinkingTool } from '../src/tools/mcp/sequential-thinking.js';
import { Context7Tool } from '../src/tools/mcp/context7.js';
import { WebSearchTool } from '../src/tools/mcp/web-search.js';
import { FileOperationsTool } from '../src/tools/mcp/file-operations.js';
import { MemoryManagementTool } from '../src/tools/mcp/memory-management.js';
import { CodeAnalysisTool } from '../src/tools/mcp/code-analysis.js';
import { TaskManagementTool } from '../src/tools/mcp/task-management.js';
import { 
  createThinkingToolkit, 
  createResearchToolkit, 
  createDevelopmentToolkit,
  ToolkitFactory 
} from '../src/tools/mcp/collections.js';
import { MCPToolCategory } from '../src/tools/mcp/types.js';

describe('MCP Tools Registry', () => {
  test('should be initialized with default tools', () => {
    expect(mcpToolsRegistry.isInitialized()).toBe(true);
    expect(mcpToolsRegistry.getAllTools().length).toBeGreaterThan(0);
  });

  test('should get tool by name', () => {
    const sequentialThinking = mcpToolsRegistry.getTool('sequential_thinking');
    expect(sequentialThinking).toBeDefined();
    expect(sequentialThinking?.config.name).toBe('sequential_thinking');
  });

  test('should get tools by category', () => {
    const thinkingTools = mcpToolsRegistry.getToolsByCategory(MCPToolCategory.THINKING);
    expect(thinkingTools.length).toBeGreaterThan(0);
    expect(thinkingTools.every(tool => tool.config.category === MCPToolCategory.THINKING)).toBe(true);
  });

  test('should search tools', () => {
    const searchResults = mcpToolsRegistry.searchTools('thinking');
    expect(searchResults.length).toBeGreaterThan(0);
    expect(searchResults.some(tool => tool.config.name.includes('thinking'))).toBe(true);
  });

  test('should get server configuration', () => {
    const serverConfig = mcpToolsRegistry.getServerConfig();
    expect(serverConfig.name).toBe('AG3NTIC MCP Tools Server');
    expect(serverConfig.capabilities.callTool).toBe(true);
    expect(serverConfig.tools.length).toBeGreaterThan(0);
  });

  test('should get statistics', () => {
    const stats = mcpToolsRegistry.getStatistics();
    expect(stats.totalTools).toBeGreaterThan(0);
    expect(stats.toolsByCategory).toBeDefined();
    expect(stats.toolsByCapability).toBeDefined();
  });
});

describe('Sequential Thinking Tool', () => {
  let tool: SequentialThinkingTool;

  beforeEach(() => {
    tool = new SequentialThinkingTool();
  });

  test('should have correct configuration', () => {
    expect(tool.config.name).toBe('sequential_thinking');
    expect(tool.config.category).toBe(MCPToolCategory.THINKING);
    expect(tool.getCapabilities().callTool).toBe(true);
  });

  test('should execute thinking process', async () => {
    const input = {
      problem: 'How to improve team productivity?',
      steps: 3,
      approach: 'analytical' as const
    };

    const result = await tool.execute(input);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.thoughts).toHaveLength(3);
    expect(result.data.synthesis).toBeDefined();
    expect(result.data.metadata.totalSteps).toBe(3);
  });

  test('should handle different approaches', async () => {
    const approaches = ['analytical', 'creative', 'systematic', 'exploratory'] as const;
    
    for (const approach of approaches) {
      const input = {
        problem: 'Test problem',
        steps: 2,
        approach
      };

      const result = await tool.execute(input);
      expect(result.success).toBe(true);
      expect(result.data.metadata.approach).toBe(approach);
    }
  });
});

describe('Context7 Tool', () => {
  let tool: Context7Tool;

  beforeEach(() => {
    tool = new Context7Tool();
  });

  test('should have correct configuration', () => {
    expect(tool.config.name).toBe('context7');
    expect(tool.config.category).toBe(MCPToolCategory.CONTEXT);
    expect(tool.getCapabilities().listResources).toBe(true);
  });

  test('should retrieve library documentation', async () => {
    const input = {
      libraryName: 'react',
      topic: 'hooks',
      tokens: 10000
    };

    const result = await tool.execute(input);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.library.name).toBe('react');
    expect(result.data.documentation.content).toBeDefined();
    expect(result.data.metadata.tokensUsed).toBeGreaterThan(0);
  });

  test('should list resources', async () => {
    const resources = await tool.listResources();
    expect(Array.isArray(resources)).toBe(true);
    expect(resources.length).toBeGreaterThan(0);
  });
});

describe('Web Search Tool', () => {
  let tool: WebSearchTool;

  beforeEach(() => {
    tool = new WebSearchTool();
  });

  test('should have correct configuration', () => {
    expect(tool.config.name).toBe('web_search');
    expect(tool.config.category).toBe(MCPToolCategory.SEARCH);
  });

  test('should perform web search', async () => {
    const input = {
      query: 'artificial intelligence trends',
      maxResults: 5,
      searchType: 'web' as const
    };

    const result = await tool.execute(input);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.results).toHaveLength(5);
    expect(result.data.metadata.searchTime).toBeGreaterThan(0);
  });

  test('should handle different search types', async () => {
    const searchTypes = ['web', 'news', 'academic'] as const;
    
    for (const searchType of searchTypes) {
      const input = {
        query: 'test query',
        searchType,
        maxResults: 3
      };

      const result = await tool.execute(input);
      expect(result.success).toBe(true);
    }
  });
});

describe('File Operations Tool', () => {
  let tool: FileOperationsTool;

  beforeEach(() => {
    tool = new FileOperationsTool();
  });

  test('should have correct configuration', () => {
    expect(tool.config.name).toBe('file_operations');
    expect(tool.config.category).toBe(MCPToolCategory.FILE);
  });

  test('should perform read operation', async () => {
    const input = {
      operation: 'read' as const,
      path: './test.txt',
      encoding: 'utf8' as const
    };

    const result = await tool.execute(input);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.operation).toBe('read');
    expect(typeof result.data.result).toBe('string');
  });

  test('should perform list operation', async () => {
    const input = {
      operation: 'list' as const,
      path: './src',
      recursive: true
    };

    const result = await tool.execute(input);
    
    expect(result.success).toBe(true);
    expect(Array.isArray(result.data.result)).toBe(true);
  });
});

describe('Memory Management Tool', () => {
  let tool: MemoryManagementTool;

  beforeEach(() => {
    tool = new MemoryManagementTool();
  });

  test('should have correct configuration', () => {
    expect(tool.config.name).toBe('memory_management');
    expect(tool.config.category).toBe(MCPToolCategory.MEMORY);
  });

  test('should store and retrieve values', async () => {
    // Store a value
    const storeInput = {
      operation: 'store' as const,
      key: 'test_key',
      value: 'test_value',
      namespace: 'test'
    };

    const storeResult = await tool.execute(storeInput);
    expect(storeResult.success).toBe(true);

    // Retrieve the value
    const retrieveInput = {
      operation: 'retrieve' as const,
      key: 'test_key',
      namespace: 'test'
    };

    const retrieveResult = await tool.execute(retrieveInput);
    expect(retrieveResult.success).toBe(true);
    expect(retrieveResult.data.result).toBe('test_value');
  });

  test('should list stored values', async () => {
    const input = {
      operation: 'list' as const,
      namespace: 'default'
    };

    const result = await tool.execute(input);
    expect(result.success).toBe(true);
    expect(Array.isArray(result.data.result)).toBe(true);
  });
});

describe('Code Analysis Tool', () => {
  let tool: CodeAnalysisTool;

  beforeEach(() => {
    tool = new CodeAnalysisTool();
  });

  test('should have correct configuration', () => {
    expect(tool.config.name).toBe('code_analysis');
    expect(tool.config.category).toBe(MCPToolCategory.CODE);
  });

  test('should analyze JavaScript code', async () => {
    const input = {
      code: 'function test() { var x = 1; return x == 1; }',
      language: 'javascript',
      analysisType: 'all' as const
    };

    const result = await tool.execute(input);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.language).toBe('javascript');
    expect(result.data.results.syntax).toBeDefined();
    expect(result.data.results.complexity).toBeDefined();
    expect(result.data.summary.overallScore).toBeGreaterThanOrEqual(0);
  });

  test('should detect language automatically', async () => {
    const input = {
      code: 'def hello(): print("Hello, World!")',
      analysisType: 'syntax' as const
    };

    const result = await tool.execute(input);
    expect(result.success).toBe(true);
    expect(result.data.language).toBe('python');
  });
});

describe('Task Management Tool', () => {
  let tool: TaskManagementTool;

  beforeEach(() => {
    tool = new TaskManagementTool();
  });

  test('should have correct configuration', () => {
    expect(tool.config.name).toBe('task_management');
    expect(tool.config.category).toBe(MCPToolCategory.TASK);
  });

  test('should create and list tasks', async () => {
    // Create a task
    const createInput = {
      operation: 'create' as const,
      task: {
        title: 'Test Task',
        description: 'A test task',
        priority: 'medium' as const
      }
    };

    const createResult = await tool.execute(createInput);
    expect(createResult.success).toBe(true);
    expect(createResult.data.title).toBe('Test Task');

    // List tasks
    const listInput = {
      operation: 'list' as const
    };

    const listResult = await tool.execute(listInput);
    expect(listResult.success).toBe(true);
    expect(Array.isArray(listResult.data)).toBe(true);
  });

  test('should update task status', async () => {
    // First create a task
    const createInput = {
      operation: 'create' as const,
      task: {
        title: 'Update Test Task',
        priority: 'high' as const
      }
    };

    const createResult = await tool.execute(createInput);
    const taskId = createResult.data.id;

    // Update the task
    const updateInput = {
      operation: 'update' as const,
      task: {
        id: taskId,
        status: 'in_progress' as const
      }
    };

    const updateResult = await tool.execute(updateInput);
    expect(updateResult.success).toBe(true);
    expect(updateResult.data.status).toBe('in_progress');
  });
});

describe('Tool Collections', () => {
  test('should create thinking toolkit', () => {
    const toolkit = createThinkingToolkit();
    expect(toolkit.name).toBe('Thinking & Analysis Toolkit');
    expect(toolkit.tools.length).toBeGreaterThan(0);
    expect(toolkit.capabilities.callTool).toBe(true);
  });

  test('should create research toolkit', () => {
    const toolkit = createResearchToolkit();
    expect(toolkit.name).toBe('Research & Information Toolkit');
    expect(toolkit.tools.length).toBeGreaterThan(0);
    expect(toolkit.capabilities.listResources).toBe(true);
  });

  test('should create development toolkit', () => {
    const toolkit = createDevelopmentToolkit();
    expect(toolkit.name).toBe('Development Toolkit');
    expect(toolkit.tools.length).toBeGreaterThan(0);
  });

  test('should create custom toolkit', () => {
    const toolkit = ToolkitFactory.createCustomToolkit(
      'Custom Test Toolkit',
      'A custom toolkit for testing',
      ['sequential_thinking', 'memory_management']
    );

    expect(toolkit.name).toBe('Custom Test Toolkit');
    expect(toolkit.tools.length).toBe(2);
    expect(toolkit.tools.some(tool => tool.config.name === 'sequential_thinking')).toBe(true);
    expect(toolkit.tools.some(tool => tool.config.name === 'memory_management')).toBe(true);
  });
});

describe('MCP Utils', () => {
  test('should get tools by category', () => {
    const thinkingTools = MCPUtils.getToolsByCategory(MCPToolCategory.THINKING);
    expect(thinkingTools.length).toBeGreaterThan(0);
  });

  test('should search tools', () => {
    const results = MCPUtils.searchTools('memory');
    expect(results.length).toBeGreaterThan(0);
  });

  test('should get statistics', () => {
    const stats = MCPUtils.getStatistics();
    expect(stats.totalTools).toBeGreaterThan(0);
    expect(typeof stats.toolsByCategory).toBe('object');
  });

  test('should export configuration', () => {
    const config = MCPUtils.exportConfig();
    expect(config.tools.length).toBeGreaterThan(0);
    expect(config.collections.length).toBeGreaterThan(0);
    expect(config.serverConfig).toBeDefined();
    expect(config.statistics).toBeDefined();
  });
});
