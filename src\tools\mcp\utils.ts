/**
 * MCP Tools Utilities
 * 
 * Utility functions and helpers for working with MCP tools,
 * including validation, conversion, and common operations.
 */

import { z } from 'zod';
import { MCPTool, MCPToolConfig, MCPToolExecutionResult, MCPError, MCPErrorType, MCPCapabilities } from './types.js';

/**
 * MCP tool validation utilities
 */
export const MCPValidation = {
  /**
   * Validate MCP tool configuration
   */
  validateToolConfig(config: MCPToolConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.name || config.name.trim().length === 0) {
      errors.push('Tool name is required and cannot be empty');
    }

    if (config.name && !/^[a-z][a-z0-9_]*$/.test(config.name)) {
      errors.push('Tool name must start with a letter and contain only lowercase letters, numbers, and underscores');
    }

    if (!config.title || config.title.trim().length === 0) {
      errors.push('Tool title is required and cannot be empty');
    }

    if (!config.description || config.description.trim().length === 0) {
      errors.push('Tool description is required and cannot be empty');
    }

    if (config.version && !/^\d+\.\d+\.\d+$/.test(config.version)) {
      errors.push('Tool version must follow semantic versioning (e.g., 1.0.0)');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Validate input against schema
   */
  validateInput(input: any, schema: z.ZodSchema): { valid: boolean; errors: string[]; data?: any } {
    try {
      const data = schema.parse(input);
      return { valid: true, errors: [], data };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return { valid: false, errors };
      }
      return { valid: false, errors: ['Unknown validation error'] };
    }
  },

  /**
   * Validate MCP capabilities
   */
  validateCapabilities(capabilities: MCPCapabilities): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const validCapabilities = [
      'listResources', 'readResource', 'subscribeResource',
      'listPrompts', 'getPrompt', 'listTools', 'callTool',
      'logging', 'sampling'
    ];

    Object.keys(capabilities).forEach(capability => {
      if (!validCapabilities.includes(capability)) {
        errors.push(`Unknown capability: ${capability}`);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }
};

/**
 * MCP tool conversion utilities
 */
export const MCPConversion = {
  /**
   * Convert tool to MCP-compatible format
   */
  toMCPFormat(tool: MCPTool): any {
    return {
      name: tool.config.name,
      description: tool.config.description,
      inputSchema: this.schemaToMCPSchema(tool.inputSchema),
      outputSchema: tool.outputSchema ? this.schemaToMCPSchema(tool.outputSchema) : undefined
    };
  },

  /**
   * Convert Zod schema to MCP schema format
   */
  schemaToMCPSchema(_schema: z.ZodSchema): any {
    // This is a simplified conversion - in a real implementation,
    // you'd need a more comprehensive Zod to JSON Schema converter
    try {
      return {
        type: 'object',
        properties: {},
        description: 'Tool input schema'
      };
    } catch (error) {
      return {
        type: 'object',
        description: 'Schema conversion failed'
      };
    }
  },

  /**
   * Convert execution result to MCP format
   */
  resultToMCPFormat(result: MCPToolExecutionResult): any {
    return {
      content: result.content || [],
      isError: !result.success,
      _meta: result.metadata
    };
  }
};

/**
 * MCP error handling utilities
 */
export const MCPErrorHandling = {
  /**
   * Create standardized MCP error
   */
  createError(type: MCPErrorType, message: string, code?: number, data?: any): MCPError {
    return new MCPError(type, message, code, data);
  },

  /**
   * Handle and format errors for MCP responses
   */
  handleError(error: any): MCPToolExecutionResult {
    if (error instanceof MCPError) {
      return {
        success: false,
        error: error.message,
        content: [
          {
            type: 'text',
            text: `Error (${error.type}): ${error.message}`
          }
        ],
        metadata: {
          errorType: error.type,
          errorCode: error.code,
          errorData: error.data
        }
      };
    }

    if (error instanceof Error) {
      return {
        success: false,
        error: error.message,
        content: [
          {
            type: 'text',
            text: `Error: ${error.message}`
          }
        ],
        metadata: {
          errorType: 'InternalError',
          stack: error.stack
        }
      };
    }

    return {
      success: false,
      error: 'Unknown error occurred',
      content: [
        {
          type: 'text',
          text: 'An unknown error occurred during tool execution'
        }
      ],
      metadata: {
        errorType: 'InternalError',
        originalError: error
      }
    };
  },

  /**
   * Wrap tool execution with error handling
   */
  async safeExecute<T>(
    operation: () => Promise<T>,
    errorContext?: string
  ): Promise<{ success: boolean; data?: T; error?: MCPError }> {
    try {
      const data = await operation();
      return { success: true, data };
    } catch (error) {
      const context = errorContext ? ` (${errorContext})` : '';
      
      if (error instanceof MCPError) {
        return { success: false, error };
      }
      
      if (error instanceof Error) {
        return {
          success: false,
          error: new MCPError(
            MCPErrorType.INTERNAL_ERROR,
            `${error.message}${context}`,
            undefined,
            { originalError: error.message, stack: error.stack }
          )
        };
      }
      
      return {
        success: false,
        error: new MCPError(
          MCPErrorType.INTERNAL_ERROR,
          `Unknown error${context}`,
          undefined,
          { originalError: error }
        )
      };
    }
  }
};

/**
 * MCP performance utilities
 */
export const MCPPerformance = {
  /**
   * Measure execution time
   */
  async measureTime<T>(operation: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    const result = await operation();
    const duration = Date.now() - startTime;
    return { result, duration };
  },

  /**
   * Create performance metrics
   */
  createMetrics(startTime: number, endTime?: number): {
    duration: number;
    timestamp: string;
    performance: {
      executionTime: number;
      memoryUsage?: NodeJS.MemoryUsage;
    };
  } {
    const end = endTime || Date.now();
    const duration = end - startTime;
    
    return {
      duration,
      timestamp: new Date(startTime).toISOString(),
      performance: {
        executionTime: duration,
        memoryUsage: typeof process !== 'undefined' ? process.memoryUsage() : undefined as any
      }
    };
  },

  /**
   * Add timeout to operation
   */
  async withTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number,
    timeoutMessage = 'Operation timed out'
  ): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new MCPError(
          MCPErrorType.INTERNAL_ERROR,
          timeoutMessage,
          408
        )), timeoutMs)
      )
    ]);
  }
};

/**
 * MCP data utilities
 */
export const MCPDataUtils = {
  /**
   * Sanitize data for MCP transport
   */
  sanitizeData(data: any): any {
    if (data === null || data === undefined) {
      return data;
    }

    if (typeof data === 'function') {
      return '[Function]';
    }

    if (data instanceof Error) {
      return {
        name: data.name,
        message: data.message,
        stack: data.stack
      };
    }

    if (data instanceof Date) {
      return data.toISOString();
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item));
    }

    if (typeof data === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(data)) {
        sanitized[key] = this.sanitizeData(value);
      }
      return sanitized;
    }

    return data;
  },

  /**
   * Truncate large data for transport
   */
  truncateData(data: any, maxSize = 10000): any {
    const serialized = JSON.stringify(data);
    
    if (serialized.length <= maxSize) {
      return data;
    }

    if (typeof data === 'string') {
      return data.substring(0, maxSize - 3) + '...';
    }

    if (Array.isArray(data)) {
      const truncated = [];
      let currentSize = 2; // for []
      
      for (const item of data) {
        const itemSize = JSON.stringify(item).length;
        if (currentSize + itemSize > maxSize) {
          truncated.push(`... ${data.length - truncated.length} more items`);
          break;
        }
        truncated.push(item);
        currentSize += itemSize + 1; // +1 for comma
      }
      
      return truncated;
    }

    return {
      ...data,
      _truncated: true,
      _originalSize: serialized.length,
      _maxSize: maxSize
    };
  },

  /**
   * Deep clone data
   */
  deepClone<T>(data: T): T {
    if (data === null || typeof data !== 'object') {
      return data;
    }

    if (data instanceof Date) {
      return new Date(data.getTime()) as unknown as T;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.deepClone(item)) as unknown as T;
    }

    const cloned = {} as T;
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(data[key]);
      }
    }

    return cloned;
  }
};

/**
 * MCP logging utilities
 */
export const MCPLogging = {
  /**
   * Create structured log entry
   */
  createLogEntry(
    level: 'debug' | 'info' | 'warn' | 'error',
    message: string,
    context?: any
  ): any {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: context ? MCPDataUtils.sanitizeData(context) : undefined
    };
  },

  /**
   * Log tool execution
   */
  logExecution(
    toolName: string,
    operation: string,
    duration: number,
    success: boolean,
    error?: string
  ): any {
    return this.createLogEntry(
      success ? 'info' : 'error',
      `Tool execution: ${toolName}.${operation}`,
      {
        toolName,
        operation,
        duration,
        success,
        error
      }
    );
  }
};

/**
 * Common MCP utility functions
 */
export const MCPUtils = {
  validation: MCPValidation,
  conversion: MCPConversion,
  errorHandling: MCPErrorHandling,
  performance: MCPPerformance,
  data: MCPDataUtils,
  logging: MCPLogging,

  /**
   * Generate unique ID
   */
  generateId(): string {
    return `mcp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * Check if value is MCP tool
   */
  isMCPTool(value: any): value is MCPTool {
    return value &&
           typeof value === 'object' &&
           typeof value.execute === 'function' &&
           typeof value.getCapabilities === 'function' &&
           value.config &&
           typeof value.config.name === 'string';
  },

  /**
   * Merge capabilities
   */
  mergeCapabilities(...capabilities: MCPCapabilities[]): MCPCapabilities {
    const merged: MCPCapabilities = {};
    
    for (const cap of capabilities) {
      Object.entries(cap).forEach(([key, value]) => {
        if (value) {
          merged[key as keyof MCPCapabilities] = true;
        }
      });
    }
    
    return merged;
  }
};
