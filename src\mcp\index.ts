// Core MCP types and interfaces
export * from './types.js';

// MCP server and client implementations
export * from './server.js';
export * from './client.js';

// Adapters for AG3NTIC integration
export * from './adapters.js';

// Re-export commonly used MCP SDK types for convenience
// Note: These would be imported from @modelcontextprotocol/sdk in a real implementation
export type McpServer = any;
export type MCPClient = any;

// Convenience imports
import { AG3NTICMCPServer, createMCPServer, createHTTPMCPServer } from './server.js';
import { AG3NTICMCPClient, createMCPClient, createHTTPMCPClient } from './client.js';
import {
  createAG3NTICToMCPAdapter,
  createMCPToAG3NTICAdapter,
  createMCPServerFromAG3NTICTools,
  createAG3NTICToolsFromMCPClient,
  createGraphNodesFromMCPClient
} from './adapters.js';

/**
 * MCP registry for managing servers and clients
 */
class MCPRegistry {
  private servers = new Map<string, AG3NTICMCPServer>();
  private clients = new Map<string, AG3NTICMCPClient>();

  /**
   * Register a server
   */
  registerServer(id: string, server: AG3NTICMCPServer): void {
    this.servers.set(id, server);
  }

  /**
   * Unregister a server
   */
  unregisterServer(id: string): void {
    const server = this.servers.get(id);
    if (server && server.isRunning()) {
      server.stop().catch(console.error);
    }
    this.servers.delete(id);
  }

  /**
   * Get a server
   */
  getServer(id: string): AG3NTICMCPServer | undefined {
    return this.servers.get(id);
  }

  /**
   * List all servers
   */
  listServers(): Array<{ id: string; server: AG3NTICMCPServer }> {
    return Array.from(this.servers.entries()).map(([id, server]) => ({ id, server }));
  }

  /**
   * Register a client
   */
  registerClient(id: string, client: AG3NTICMCPClient): void {
    this.clients.set(id, client);
  }

  /**
   * Unregister a client
   */
  unregisterClient(id: string): void {
    const client = this.clients.get(id);
    if (client && client.isConnected()) {
      client.disconnect().catch(console.error);
    }
    this.clients.delete(id);
  }

  /**
   * Get a client
   */
  getClient(id: string): AG3NTICMCPClient | undefined {
    return this.clients.get(id);
  }

  /**
   * List all clients
   */
  listClients(): Array<{ id: string; client: AG3NTICMCPClient }> {
    return Array.from(this.clients.entries()).map(([id, client]) => ({ id, client }));
  }

  /**
   * Shutdown all servers and clients
   */
  async shutdown(): Promise<void> {
    const shutdownPromises: Promise<void>[] = [];

    // Stop all servers
    for (const server of this.servers.values()) {
      if (server.isRunning()) {
        shutdownPromises.push(server.stop());
      }
    }

    // Disconnect all clients
    for (const client of this.clients.values()) {
      if (client.isConnected()) {
        shutdownPromises.push(client.disconnect());
      }
    }

    await Promise.all(shutdownPromises);
    
    this.servers.clear();
    this.clients.clear();
  }

  /**
   * Get registry statistics
   */
  getStats(): {
    servers: { total: number; running: number };
    clients: { total: number; connected: number };
  } {
    const runningServers = Array.from(this.servers.values()).filter(s => s.isRunning()).length;
    const connectedClients = Array.from(this.clients.values()).filter(c => c.isConnected()).length;

    return {
      servers: {
        total: this.servers.size,
        running: runningServers
      },
      clients: {
        total: this.clients.size,
        connected: connectedClients
      }
    };
  }
}

/**
 * Global MCP registry instance
 */
export const globalMCPRegistry = new MCPRegistry();

/**
 * High-level MCP utilities
 */
export const MCP = {
  // Server creation
  createServer: createMCPServer,
  createHTTPServer: createHTTPMCPServer,
  
  // Client creation
  createClient: createMCPClient,
  createHTTPClient: createHTTPMCPClient,
  
  // Adapters
  createAG3NTICToMCPAdapter,
  createMCPToAG3NTICAdapter,
  
  // Integration utilities
  createServerFromAG3NTICTools: createMCPServerFromAG3NTICTools,
  createAG3NTICToolsFromClient: createAG3NTICToolsFromMCPClient,
  createGraphNodesFromClient: createGraphNodesFromMCPClient,
  
  // Registry
  registry: globalMCPRegistry,
  
  // Convenience methods
  registerServer: (id: string, server: AG3NTICMCPServer) => globalMCPRegistry.registerServer(id, server),
  registerClient: (id: string, client: AG3NTICMCPClient) => globalMCPRegistry.registerClient(id, client),
  getServer: (id: string) => globalMCPRegistry.getServer(id),
  getClient: (id: string) => globalMCPRegistry.getClient(id),
  
  // Lifecycle management
  shutdown: () => globalMCPRegistry.shutdown(),
  getStats: () => globalMCPRegistry.getStats()
};

/**
 * Quick setup function for common MCP scenarios
 */
export async function setupMCPIntegration(options: {
  // Server options
  server?: {
    name: string;
    version?: string;
    tools?: import('../tools/types.js').Tool[];
    transport?: 'stdio' | 'streamable-http';
    autoStart?: boolean;
  };
  
  // Client options
  clients?: Array<{
    id: string;
    name: string;
    command?: string;
    args?: string[];
    url?: string;
    transport?: 'stdio' | 'streamable-http' | 'sse';
    autoConnect?: boolean;
  }>;
}): Promise<{
  server?: AG3NTICMCPServer;
  clients: Record<string, AG3NTICMCPClient>;
}> {
  const result: {
    server?: AG3NTICMCPServer;
    clients: Record<string, AG3NTICMCPClient>;
  } = {
    clients: {}
  };

  // Setup server if requested
  if (options.server) {
    const { name, version = '1.0.0', tools = [], transport = 'stdio', autoStart = true } = options.server;
    
    if (tools.length > 0) {
      result.server = await createMCPServerFromAG3NTICTools(tools, name, version, transport);
    } else {
      result.server = transport === 'stdio' 
        ? createMCPServer(name, version, transport)
        : createHTTPMCPServer(name, version);
    }
    
    globalMCPRegistry.registerServer('default', result.server);
    
    if (autoStart) {
      await result.server.start();
    }
  }

  // Setup clients if requested
  if (options.clients) {
    for (const clientConfig of options.clients) {
      const { id, name, command, args = [], url, transport = 'stdio', autoConnect = true } = clientConfig;
      
      let client: AG3NTICMCPClient;
      
      if (transport === 'stdio' && command) {
        client = createMCPClient(name, command, args);
      } else if ((transport === 'streamable-http' || transport === 'sse') && url) {
        client = createHTTPMCPClient(name, url);
      } else {
        throw new Error(`Invalid client configuration for ${id}: missing required parameters`);
      }
      
      result.clients[id] = client;
      globalMCPRegistry.registerClient(id, client);
      
      if (autoConnect) {
        await client.connect();
      }
    }
  }

  return result;
}

/**
 * Default export with all MCP functionality
 */
export default MCP;
