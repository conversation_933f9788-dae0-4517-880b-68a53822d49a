import { describe, test, expect, beforeEach } from '@jest/globals';

/**
 * Universal Runnable Interface Tests
 * 
 * Tests the new LangChain-inspired Runnable interface and composition system
 */

import {
  BaseRunnable,
  RunnableConfig,
  CallbackHandler,
  RunInfo
} from '../src/core/runnable.js';

import {
  RunnableUtils,
  pipe
} from '../src/core/composition.js';

import {
  CallbackUtils
} from '../src/core/callbacks.js';

import { Graph } from '../src/core/graph.js';
import { AgentState } from '../src/core/types.js';

// Test implementations
class TestRunnable extends BaseRunnable<string, string> {
  constructor(private transform: (input: string) => string, name?: string) {
    super(name || 'TestRunnable');
  }

  async invoke(input: string, config?: RunnableConfig): Promise<string> {
    return this.executeWithCallbacks(async () => {
      return this.transform(input);
    }, input, config);
  }
}

class AsyncTestRunnable extends BaseRunnable<number, number> {
  constructor(private delay: number = 100, name?: string) {
    super(name || 'AsyncTestRunnable');
  }

  async invoke(input: number, config?: RunnableConfig): Promise<number> {
    return this.executeWithCallbacks(async () => {
      await new Promise(resolve => setTimeout(resolve, this.delay));
      return input * 2;
    }, input, config);
  }

  override async *stream(input: number, _config?: RunnableConfig): AsyncGenerator<number, void, unknown> {
    // Simulate streaming by yielding intermediate values
    for (let i = 1; i <= input; i++) {
      await new Promise(resolve => setTimeout(resolve, 10));
      yield i * 2;
    }
  }
}

describe('Universal Runnable Interface', () => {
  let testCallback: CallbackHandler;
  let callbackEvents: Array<{ method: string; run: RunInfo; error?: Error }>;

  beforeEach(() => {
    callbackEvents = [];
    testCallback = {
      onStart: (run) => { callbackEvents.push({ method: 'onStart', run }); },
      onEnd: (run) => { callbackEvents.push({ method: 'onEnd', run }); },
      onError: (run, error) => { callbackEvents.push({ method: 'onError', run, error }); }
    };
  });

  test('should execute basic runnable', async () => {
    const runnable = new TestRunnable(input => input.toUpperCase(), 'UpperCase');
    const result = await runnable.invoke('hello');
    
    expect(result).toBe('HELLO');
  });

  test('should support callbacks', async () => {
    const runnable = new TestRunnable(input => input.toUpperCase(), 'UpperCase');
    const result = await runnable.invoke('hello', { callbacks: [testCallback] });
    
    expect(result).toBe('HELLO');
    expect(callbackEvents).toHaveLength(2);
    expect(callbackEvents[0].method).toBe('onStart');
    expect(callbackEvents[1].method).toBe('onEnd');
    expect(callbackEvents[0].run.name).toBe('UpperCase');
  });

  test('should handle errors with callbacks', async () => {
    const runnable = new TestRunnable(() => {
      throw new Error('Test error');
    }, 'ErrorRunnable');

    await expect(runnable.invoke('test', { callbacks: [testCallback] }))
      .rejects.toThrow('Test error');
    
    expect(callbackEvents).toHaveLength(2);
    expect(callbackEvents[0].method).toBe('onStart');
    expect(callbackEvents[1].method).toBe('onError');
    expect(callbackEvents[1].error?.message).toBe('Test error');
  });

  test('should support configuration merging', async () => {
    const runnable = new TestRunnable(input => input.toUpperCase())
      .withTags(['test', 'transform'])
      .withMetadata({ version: '1.0' });

    const result = await runnable.invoke('hello', { 
      callbacks: [testCallback],
      tags: ['runtime'],
      metadata: { user: 'test' }
    });
    
    expect(result).toBe('HELLO');
    expect(callbackEvents[0].run.tags).toEqual(['test', 'transform', 'runtime']);
    expect(callbackEvents[0].run.metadata).toEqual({ version: '1.0', user: 'test' });
  });

  test('should support batch processing', async () => {
    const runnable = new TestRunnable(input => input.toUpperCase());
    const results = await runnable.batch(['hello', 'world', 'test']);
    
    expect(results).toEqual(['HELLO', 'WORLD', 'TEST']);
  });

  test('should support streaming', async () => {
    const runnable = new AsyncTestRunnable(10, 'StreamTest');
    const chunks: number[] = [];
    
    for await (const chunk of runnable.stream(3)) {
      chunks.push(chunk);
    }
    
    expect(chunks).toEqual([2, 4, 6]);
  });
});

describe('Runnable Composition', () => {
  test('should compose runnables with pipe', async () => {
    const step1 = new TestRunnable(input => input.toUpperCase(), 'Step1');
    const step2 = new TestRunnable(input => `[${input}]`, 'Step2');
    
    const chain = step1.pipe(step2);
    const result = await chain.invoke('hello');
    
    expect(result).toBe('[HELLO]');
    expect(chain.name).toBe('RunnableSequence[Step1 | Step2]');
  });

  test('should compose with pipe function', async () => {
    const step1 = new TestRunnable(input => input.toUpperCase());
    const step2 = new TestRunnable(input => `[${input}]`);
    const step3 = new TestRunnable(input => `${input}!`);
    
    const chain = pipe(step1, step2, step3);
    const result = await chain.invoke('hello');
    
    expect(result).toBe('[HELLO]!');
  });

  test('should support lambda runnables', async () => {
    const lambda = RunnableUtils.lambda((input: string) => input.split('').reverse().join(''), 'Reverse');
    const result = await lambda.invoke('hello');
    
    expect(result).toBe('olleh');
  });

  test('should support parallel execution', async () => {
    const upper = RunnableUtils.lambda((input: string) => input.toUpperCase(), 'Upper');
    const lower = RunnableUtils.lambda((input: string) => input.toLowerCase(), 'Lower');
    const length = RunnableUtils.lambda((input: string) => input.length, 'Length');
    
    const parallel = RunnableUtils.parallel({
      upper,
      lower,
      length
    });
    
    const result = await parallel.invoke('Hello');
    
    expect(result).toEqual({
      upper: 'HELLO',
      lower: 'hello',
      length: 5
    });
  });

  test('should support conditional branching', async () => {
    const isLong = RunnableUtils.lambda((input: string) => `Long: ${input}`, 'LongHandler');
    const isShort = RunnableUtils.lambda((input: string) => `Short: ${input}`, 'ShortHandler');
    
    const branch = RunnableUtils.branch([
      {
        condition: (input: string) => input.length > 5,
        runnable: isLong
      },
      {
        condition: (input: string) => input.length <= 5,
        runnable: isShort
      }
    ]);
    
    const longResult = await branch.invoke('hello world');
    const shortResult = await branch.invoke('hi');
    
    expect(longResult).toBe('Long: hello world');
    expect(shortResult).toBe('Short: hi');
  });

  test('should support passthrough', async () => {
    const passthrough = RunnableUtils.passthrough<string>();
    const result = await passthrough.invoke('test');
    
    expect(result).toBe('test');
  });

  test('should support assignment', async () => {
    interface TestInput {
      text: string;
    }
    
    const assign = RunnableUtils.assign<TestInput, { upper: string; length: number }>({
      upper: (input) => input.text.toUpperCase(),
      length: (input) => input.text.length
    });
    
    const result = await assign.invoke({ text: 'hello' });
    
    expect(result).toEqual({
      text: 'hello',
      upper: 'HELLO',
      length: 5
    });
  });
});

describe('Callback System', () => {
  test('should support console callbacks', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    const consoleCallback = CallbackUtils.console({ verbose: true, colors: false });
    
    const runnable = new TestRunnable(input => input.toUpperCase(), 'TestRunnable');
    await runnable.invoke('hello', { callbacks: [consoleCallback] });
    
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('▶ Starting TestRunnable'));
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('✓ Completed TestRunnable'));
    
    consoleSpy.mockRestore();
  });

  test('should collect metrics', async () => {
    const metricsCallback = CallbackUtils.metrics();
    const runnable = new TestRunnable(input => input.toUpperCase(), 'MetricsTest');
    
    await runnable.invoke('hello', { callbacks: [metricsCallback] });
    await runnable.invoke('world', { callbacks: [metricsCallback] });
    
    const metrics = metricsCallback.getMetrics();
    
    expect(metrics.MetricsTest).toBeDefined();
    expect(metrics.MetricsTest.count).toBe(2);
    expect(metrics.MetricsTest.errors).toBe(0);
    expect(metrics.MetricsTest.averageDuration).toBeGreaterThanOrEqual(0);
  });

  test('should stream events', async () => {
    const eventCallback = CallbackUtils.eventStream();
    const events: any[] = [];
    
    eventCallback.addEventListener((event) => {
      events.push(event);
    });
    
    const runnable = new TestRunnable(input => input.toUpperCase(), 'EventTest');
    await runnable.invoke('hello', { callbacks: [eventCallback] });
    
    expect(events).toHaveLength(2);
    expect(events[0].type).toBe('on_start');
    expect(events[1].type).toBe('on_end');
    expect(events[0].name).toBe('EventTest');
  });
});

describe('Graph Integration', () => {
  test('should work with graph as runnable', async () => {
    interface TestState extends AgentState {
      value: number;
    }

    const graph = new Graph<TestState>('TestGraph');
    
    graph.addNode('start', async (state) => ({
      value: (state.value || 0) + 1
    }));
    
    graph.addNode('double', async (state) => ({
      value: state.value * 2
    }));
    
    graph.addEdge('start', 'double');
    graph.addEdge('double', '__END__');
    
    const initialState: TestState = {
      messages: [],
      value: 5
    };
    
    const result = await graph.invoke(initialState);
    
    expect(result.value).toBe(12); // (5 + 1) * 2
  });

  test('should support graph streaming', async () => {
    interface TestState extends AgentState {
      counter: number;
    }

    const graph = new Graph<TestState>('StreamGraph');
    
    graph.addNode('increment', async (state) => ({
      counter: (state.counter || 0) + 1
    }));
    
    graph.addEdge('increment', '__END__');
    
    const initialState: TestState = {
      messages: [],
      counter: 0
    };
    
    const states: TestState[] = [];
    for await (const state of graph.stream(initialState)) {
      states.push(state);
    }
    
    expect(states).toHaveLength(1);
    expect(states[0].counter).toBe(1);
  });
});
