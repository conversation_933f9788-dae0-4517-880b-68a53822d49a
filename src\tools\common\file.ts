import { z } from 'zod';
import { promises as fs } from 'fs';
import { join, dirname, basename, extname } from 'path';
import { BaseTool } from '../base.js';
import { ToolConfig, ToolContext, ToolExecutionResult, ToolCategory } from '../types.js';

/**
 * File read tool input schema
 */
const FileReadInputSchema = z.object({
  path: z.string().min(1, 'File path is required'),
  encoding: z.enum(['utf8', 'ascii', 'base64', 'hex']).optional().default('utf8'),
  maxSize: z.number().min(1).max(10 * 1024 * 1024).optional().default(1024 * 1024) // 1MB default
});

type FileReadInput = z.infer<typeof FileReadInputSchema>;

/**
 * File read tool
 */
export class FileReadTool extends BaseTool<FileReadInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'file_read',
      title: 'File Read',
      description: 'Read content from a file',
      category: ToolCategory.FILE,
      tags: ['file', 'read', 'content', 'filesystem'],
      examples: [
        {
          description: 'Read a text file',
          input: { path: './README.md' }
        },
        {
          description: 'Read with specific encoding',
          input: { path: './data.txt', encoding: 'utf8' }
        }
      ]
    };

    super(config, FileReadInputSchema, undefined);
  }

  async execute(input: FileReadInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      // Check if file exists and get stats
      const stats = await fs.stat(input.path);
      
      if (!stats.isFile()) {
        return this.createErrorResult(`Path '${input.path}' is not a file`);
      }

      if (stats.size > input.maxSize) {
        return this.createErrorResult(
          `File size (${stats.size} bytes) exceeds maximum allowed size (${input.maxSize} bytes)`
        );
      }

      const content = await fs.readFile(input.path, input.encoding as BufferEncoding);
      
      const result = {
        path: input.path,
        content,
        size: stats.size,
        encoding: input.encoding,
        lastModified: stats.mtime.toISOString(),
        extension: extname(input.path),
        basename: basename(input.path)
      };

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `Successfully read file: ${input.path}\nSize: ${stats.size} bytes\nLast modified: ${stats.mtime.toISOString()}\n\nContent:\n${content}`
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}

/**
 * File write tool input schema
 */
const FileWriteInputSchema = z.object({
  path: z.string().min(1, 'File path is required'),
  content: z.string(),
  encoding: z.enum(['utf8', 'ascii', 'base64', 'hex']).optional().default('utf8'),
  createDirectories: z.boolean().optional().default(true),
  overwrite: z.boolean().optional().default(false)
});

type FileWriteInput = z.infer<typeof FileWriteInputSchema>;

/**
 * File write tool
 */
export class FileWriteTool extends BaseTool<FileWriteInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'file_write',
      title: 'File Write',
      description: 'Write content to a file',
      category: ToolCategory.FILE,
      tags: ['file', 'write', 'create', 'filesystem'],
      examples: [
        {
          description: 'Write text to a file',
          input: { path: './output.txt', content: 'Hello, World!' }
        },
        {
          description: 'Write with overwrite protection',
          input: { 
            path: './important.txt', 
            content: 'Important data',
            overwrite: false
          }
        }
      ]
    };

    super(config, FileWriteInputSchema, undefined);
  }

  async execute(input: FileWriteInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      // Check if file exists and overwrite is disabled
      if (!input.overwrite) {
        try {
          await fs.access(input.path);
          return this.createErrorResult(
            `File '${input.path}' already exists and overwrite is disabled`
          );
        } catch {
          // File doesn't exist, which is what we want
        }
      }

      // Create directories if needed
      if (input.createDirectories) {
        const dir = dirname(input.path);
        await fs.mkdir(dir, { recursive: true });
      }

      await fs.writeFile(input.path, input.content, input.encoding as BufferEncoding);
      
      // Get file stats after writing
      const stats = await fs.stat(input.path);

      const result = {
        path: input.path,
        size: stats.size,
        encoding: input.encoding,
        created: stats.birthtime.toISOString(),
        lastModified: stats.mtime.toISOString(),
        basename: basename(input.path)
      };

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `Successfully wrote file: ${input.path}\nSize: ${stats.size} bytes\nContent length: ${input.content.length} characters`
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `Failed to write file: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}

/**
 * Directory list tool input schema
 */
const DirectoryListInputSchema = z.object({
  path: z.string().min(1, 'Directory path is required'),
  recursive: z.boolean().optional().default(false),
  includeHidden: z.boolean().optional().default(false),
  pattern: z.string().optional(),
  maxDepth: z.number().min(1).max(10).optional().default(5)
});

type DirectoryListInput = z.infer<typeof DirectoryListInputSchema>;

/**
 * Directory list tool
 */
export class DirectoryListTool extends BaseTool<DirectoryListInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'directory_list',
      title: 'Directory List',
      description: 'List files and directories in a path',
      category: ToolCategory.FILE,
      tags: ['directory', 'list', 'files', 'filesystem'],
      examples: [
        {
          description: 'List current directory',
          input: { path: '.' }
        },
        {
          description: 'Recursive listing with pattern',
          input: { 
            path: './src',
            recursive: true,
            pattern: '*.ts'
          }
        }
      ]
    };

    super(config, DirectoryListInputSchema, undefined);
  }

  async execute(input: DirectoryListInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      const stats = await fs.stat(input.path);
      
      if (!stats.isDirectory()) {
        return this.createErrorResult(`Path '${input.path}' is not a directory`);
      }

      const files = await this.listDirectory(input.path, input, 0);
      
      const result = {
        path: input.path,
        files,
        totalFiles: files.length,
        recursive: input.recursive
      };

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `Directory listing for: ${input.path}\nTotal items: ${files.length}\n\n${
            files.map(file => 
              `${file.type === 'directory' ? '📁' : '📄'} ${file.name} (${file.size} bytes)`
            ).join('\n')
          }`
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `Failed to list directory: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async listDirectory(
    dirPath: string, 
    input: DirectoryListInput, 
    currentDepth: number
  ): Promise<Array<{
    name: string;
    path: string;
    type: 'file' | 'directory';
    size: number;
    lastModified: string;
    extension?: string;
  }>> {
    if (currentDepth >= input.maxDepth) {
      return [];
    }

    const entries = await fs.readdir(dirPath);
    const files = [];

    for (const entry of entries) {
      // Skip hidden files if not included
      if (!input.includeHidden && entry.startsWith('.')) {
        continue;
      }

      // Apply pattern filter if specified
      if (input.pattern && !this.matchesPattern(entry, input.pattern)) {
        continue;
      }

      const fullPath = join(dirPath, entry);
      const stats = await fs.stat(fullPath);

      const fileInfo = {
        name: entry,
        path: fullPath,
        type: stats.isDirectory() ? 'directory' as const : 'file' as const,
        size: stats.size,
        lastModified: stats.mtime.toISOString(),
        extension: stats.isFile() ? extname(entry) : undefined
      };

      files.push(fileInfo);

      // Recurse into subdirectories if requested
      if (input.recursive && stats.isDirectory()) {
        const subFiles = await this.listDirectory(fullPath, input, currentDepth + 1);
        files.push(...subFiles);
      }
    }

    return files;
  }

  private matchesPattern(filename: string, pattern: string): boolean {
    // Simple glob pattern matching (supports * and ?)
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');
    
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(filename);
  }
}
