import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import {
  MCPServerInterface,
  MCPServerConfig,
  MCPToolDefinition,
  MCPResourceDefinition,
  MCPPromptDefinition,
  MCPEvent,
  MCPEventListener,
  MCPEventType
} from './types.js';

/**
 * AG3NTIC MCP Server implementation
 */
export class AG3NTICMCPServer implements MCPServerInterface {
  public readonly server: McpServer;
  public readonly config: MCPServerConfig;
  private transport: any;
  private running = false;
  private eventListeners = new Map<MCPEventType, Set<MCPEventListener>>();

  constructor(config: MCPServerConfig) {
    this.config = config;
    
    // Initialize MCP server
    this.server = new McpServer({
      name: config.name,
      version: config.version,
      description: config.description
    }, {
      capabilities: {
        tools: config.capabilities?.tools ?? true,
        resources: config.capabilities?.resources ?? true,
        prompts: config.capabilities?.prompts ?? true,
        logging: config.capabilities?.logging ?? false
      }
    });

    this.setupTransport();
  }

  /**
   * Setup transport based on configuration
   */
  private setupTransport(): void {
    const { type, options = {} } = this.config.transport;

    switch (type) {
      case 'stdio':
        this.transport = new StdioServerTransport();
        break;

      case 'streamable-http':
        this.transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: options.sessionIdGenerator,
          enableDnsRebindingProtection: options.enableDnsRebindingProtection,
          allowedHosts: options.allowedHosts,
          allowedOrigins: options.allowedOrigins
        });
        break;

      case 'sse':
        if (!options.url) {
          throw new Error('SSE transport requires URL in options');
        }
        // SSE transport setup would go here
        throw new Error('SSE transport not yet implemented');

      case 'http':
        throw new Error('HTTP transport not yet implemented');

      default:
        throw new Error(`Unsupported transport type: ${type}`);
    }
  }

  /**
   * Register a tool with the MCP server
   */
  registerTool(tool: MCPToolDefinition): void {
    this.server.registerTool(
      tool.name,
      {
        title: tool.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        description: tool.description,
        inputSchema: tool.inputSchema
      },
      async (args) => {
        try {
          this.emitEvent('tool_called', { toolName: tool.name, args });
          const result = await tool.handler(args);
          return result;
        } catch (error) {
          this.emitEvent('error', { 
            type: 'tool_execution_error', 
            toolName: tool.name, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          });
          return {
            content: [{
              type: 'text',
              text: `Error executing tool: ${error instanceof Error ? error.message : 'Unknown error'}`
            }],
            isError: true
          };
        }
      }
    );
  }

  /**
   * Register a resource with the MCP server
   */
  registerResource(resource: MCPResourceDefinition): void {
    this.server.registerResource(
      resource.name,
      resource.uri,
      {
        title: resource.name,
        description: resource.description,
        mimeType: resource.mimeType
      },
      async (uri, params) => {
        try {
          this.emitEvent('resource_accessed', { resourceName: resource.name, uri, params });
          const result = await resource.handler(uri, params);
          return result;
        } catch (error) {
          this.emitEvent('error', { 
            type: 'resource_access_error', 
            resourceName: resource.name, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          });
          throw error;
        }
      }
    );
  }

  /**
   * Register a prompt with the MCP server
   */
  registerPrompt(prompt: MCPPromptDefinition): void {
    this.server.registerPrompt(
      prompt.name,
      {
        title: prompt.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        description: prompt.description,
        argsSchema: prompt.argsSchema
      },
      async (args) => {
        try {
          this.emitEvent('prompt_requested', { promptName: prompt.name, args });
          const result = await prompt.handler(args);
          return result;
        } catch (error) {
          this.emitEvent('error', { 
            type: 'prompt_execution_error', 
            promptName: prompt.name, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          });
          throw error;
        }
      }
    );
  }

  /**
   * Start the MCP server
   */
  async start(): Promise<void> {
    if (this.running) {
      throw new Error('Server is already running');
    }

    try {
      await this.server.connect(this.transport);
      this.running = true;
      this.emitEvent('server_started', { config: this.config });
    } catch (error) {
      this.emitEvent('error', { 
        type: 'server_start_error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Stop the MCP server
   */
  async stop(): Promise<void> {
    if (!this.running) {
      return;
    }

    try {
      // Close transport if it has a close method
      if (this.transport && typeof this.transport.close === 'function') {
        await this.transport.close();
      }
      
      this.running = false;
      this.emitEvent('server_stopped', { config: this.config });
    } catch (error) {
      this.emitEvent('error', { 
        type: 'server_stop_error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Check if server is running
   */
  isRunning(): boolean {
    return this.running;
  }

  /**
   * Add event listener
   */
  addEventListener(type: MCPEventType, listener: MCPEventListener): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(type: MCPEventType, listener: MCPEventListener): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * Emit an event to all listeners
   */
  private emitEvent(type: MCPEventType, data: any): void {
    const event: MCPEvent = {
      type,
      timestamp: new Date(),
      data
    };

    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          const result = listener(event);
          if (result instanceof Promise) {
            result.catch(error => {
              console.error(`Error in event listener for ${type}:`, error);
            });
          }
        } catch (error) {
          console.error(`Error in event listener for ${type}:`, error);
        }
      });
    }
  }

  /**
   * Get server statistics
   */
  getStats(): {
    running: boolean;
    uptime: number;
    toolsRegistered: number;
    resourcesRegistered: number;
    promptsRegistered: number;
  } {
    // Note: The MCP SDK doesn't expose internal registries directly,
    // so we'd need to track these ourselves or use reflection
    return {
      running: this.running,
      uptime: this.running ? Date.now() - (this.server as any).startTime || 0 : 0,
      toolsRegistered: 0, // Would need to track this
      resourcesRegistered: 0, // Would need to track this
      promptsRegistered: 0 // Would need to track this
    };
  }
}

/**
 * Create an MCP server with default configuration
 */
export function createMCPServer(
  name: string,
  version: string = '1.0.0',
  transportType: 'stdio' | 'streamable-http' = 'stdio'
): AG3NTICMCPServer {
  const config: MCPServerConfig = {
    name,
    version,
    transport: {
      type: transportType
    },
    capabilities: {
      tools: true,
      resources: true,
      prompts: true,
      logging: false
    }
  };

  return new AG3NTICMCPServer(config);
}

/**
 * Create an HTTP MCP server for web integration
 */
export function createHTTPMCPServer(
  name: string,
  version: string = '1.0.0',
  options: {
    sessionIdGenerator?: () => string;
    enableDnsRebindingProtection?: boolean;
    allowedHosts?: string[];
    allowedOrigins?: string[];
  } = {}
): AG3NTICMCPServer {
  const config: MCPServerConfig = {
    name,
    version,
    transport: {
      type: 'streamable-http',
      options
    },
    capabilities: {
      tools: true,
      resources: true,
      prompts: true,
      logging: false
    }
  };

  return new AG3NTICMCPServer(config);
}
