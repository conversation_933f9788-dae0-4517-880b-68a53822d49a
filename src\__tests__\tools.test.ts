import { describe, it, expect, beforeEach } from '@jest/globals';
import { z } from 'zod';
import {
  BaseTool,
  SimpleTool,
  ToolFactory,
  DefaultToolRegistry,
  globalToolRegistry,
  ToolCategory,
  initializeCommonTools
} from '../tools/index.js';
import type { ToolConfig, ToolContext, ToolExecutionResult } from '../tools/types.js';

describe('Tool Library', () => {
  describe('BaseTool', () => {
    class TestTool extends BaseTool<{ input: string }, { output: string }> {
      async execute(input: { input: string }, _context?: ToolContext): Promise<ToolExecutionResult> {
        return this.createSuccessResult({ output: `Processed: ${input.input}` });
      }
    }

    it('should create a tool with proper configuration', () => {
      const config: ToolConfig = {
        name: 'test_tool',
        title: 'Test Tool',
        description: 'A test tool',
        category: ToolCategory.UTILITY
      };

      const inputSchema = z.object({ input: z.string() });
      const tool = new TestTool(config, inputSchema, undefined);

      expect(tool.config.name).toBe('test_tool');
      expect(tool.config.title).toBe('Test Tool');
      expect(tool.config.enabled).toBe(true);
    });

    it('should validate input correctly', () => {
      const config: ToolConfig = {
        name: 'test_tool',
        title: 'Test Tool',
        description: 'A test tool'
      };

      const inputSchema = z.object({ input: z.string() });
      const tool = new TestTool(config, inputSchema, undefined);

      const validResult = tool.validateInput({ input: 'test' });
      expect(validResult.valid).toBe(true);
      expect(validResult.data).toEqual({ input: 'test' });

      const invalidResult = tool.validateInput({ input: 123 });
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.error).toContain('validation failed');
    });

    it('should execute successfully', async () => {
      const config: ToolConfig = {
        name: 'test_tool',
        title: 'Test Tool',
        description: 'A test tool'
      };

      const inputSchema = z.object({ input: z.string() });
      const tool = new TestTool(config, inputSchema, undefined);

      const result = await tool.execute({ input: 'hello' });
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ output: 'Processed: hello' });
    });
  });

  describe('SimpleTool', () => {
    it('should create and execute a simple tool', async () => {
      const config: ToolConfig = {
        name: 'simple_tool',
        title: 'Simple Tool',
        description: 'A simple tool'
      };

      const inputSchema = z.object({ value: z.number() });
      const handler = async (input: { value: number }) => ({
        success: true,
        data: { doubled: input.value * 2 },
        content: [{ type: 'text' as const, text: `Result: ${input.value * 2}` }]
      });

      const tool = new SimpleTool(config, inputSchema, undefined, handler);
      const result = await tool.execute({ value: 5 });

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ doubled: 10 });
    });
  });

  describe('ToolFactory', () => {
    it('should create tools using factory', () => {
      const config: ToolConfig = {
        name: 'factory_tool',
        title: 'Factory Tool',
        description: 'A tool created by factory'
      };

      const inputSchema = z.object({ text: z.string() });
      const handler = async (input: { text: string }) => ({
        success: true,
        data: { result: input.text.toUpperCase() },
        content: [{ type: 'text' as const, text: input.text.toUpperCase() }]
      });

      const tool = ToolFactory.create(config, inputSchema, undefined, handler);
      expect(tool.config.name).toBe('factory_tool');
    });
  });

  describe('ToolRegistry', () => {
    let registry: DefaultToolRegistry;

    beforeEach(() => {
      registry = new DefaultToolRegistry();
    });

    it('should register and retrieve tools', () => {
      const config: ToolConfig = {
        name: 'registry_tool',
        title: 'Registry Tool',
        description: 'A tool for registry testing'
      };

      const inputSchema = z.object({ input: z.string() });
      const handler = async () => ({ success: true, data: {} });
      const tool = ToolFactory.create(config, inputSchema, undefined, handler);

      registry.register(tool);
      
      const retrieved = registry.get('registry_tool');
      expect(retrieved).toBeDefined();
      expect(retrieved?.config.name).toBe('registry_tool');
    });

    it('should list all tools', () => {
      const tool1 = ToolFactory.create(
        { name: 'tool1', title: 'Tool 1', description: 'First tool' },
        z.object({}),
        undefined,
        async () => ({ success: true, data: {} })
      );

      const tool2 = ToolFactory.create(
        { name: 'tool2', title: 'Tool 2', description: 'Second tool' },
        z.object({}),
        undefined,
        async () => ({ success: true, data: {} })
      );

      registry.register(tool1);
      registry.register(tool2);

      const tools = registry.list();
      expect(tools).toHaveLength(2);
      expect(tools.map(t => t.name)).toContain('tool1');
      expect(tools.map(t => t.name)).toContain('tool2');
    });

    it('should search tools by category', () => {
      const webTool = ToolFactory.create(
        { name: 'web_tool', title: 'Web Tool', description: 'Web tool', category: ToolCategory.WEB },
        z.object({}),
        undefined,
        async () => ({ success: true, data: {} })
      );

      const fileTool = ToolFactory.create(
        { name: 'file_tool', title: 'File Tool', description: 'File tool', category: ToolCategory.FILE },
        z.object({}),
        undefined,
        async () => ({ success: true, data: {} })
      );

      registry.register(webTool);
      registry.register(fileTool);

      const webTools = registry.search({ category: ToolCategory.WEB });
      expect(webTools).toHaveLength(1);
      expect(webTools[0].name).toBe('web_tool');
    });

    it('should execute tools by name', async () => {
      const tool = ToolFactory.create(
        { name: 'exec_tool', title: 'Exec Tool', description: 'Executable tool' },
        z.object({ value: z.string() }),
        undefined,
        async (input: { value: string }) => ({
          success: true,
          data: { result: `Executed with: ${input.value}` },
          content: [{ type: 'text' as const, text: `Result: ${input.value}` }]
        })
      );

      registry.register(tool);

      const result = await registry.execute('exec_tool', { value: 'test' });
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ result: 'Executed with: test' });
    });

    it('should handle non-existent tools', async () => {
      const result = await registry.execute('non_existent', {});
      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });
  });

  describe('Common Tools', () => {
    beforeEach(() => {
      globalToolRegistry.clear();
      initializeCommonTools();
    });

    it('should initialize common tools', () => {
      const tools = globalToolRegistry.list();
      expect(tools.length).toBeGreaterThan(0);
      
      const toolNames = tools.map(t => t.name);
      expect(toolNames).toContain('web_search');
      expect(toolNames).toContain('file_read');
      expect(toolNames).toContain('text_analysis');
    });

    it('should execute web search tool', async () => {
      const result = await globalToolRegistry.execute('web_search', {
        query: 'TypeScript tutorial',
        maxResults: 3
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.query).toBe('TypeScript tutorial');
    });

    it('should execute text analysis tool', async () => {
      const result = await globalToolRegistry.execute('text_analysis', {
        text: 'This is a test sentence. It has multiple words and sentences.',
        includeWordCount: true,
        includeSentenceCount: true
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.wordCount).toBeGreaterThan(0);
      expect(result.data.sentenceCount).toBeGreaterThan(0);
    });
  });

  describe('Tool Integration with AG3NTIC', () => {
    it('should create tool node from tool', async () => {
      const { createToolNodeFromTool } = await import('../tools/index.js');
      
      const tool = ToolFactory.create(
        { name: 'integration_tool', title: 'Integration Tool', description: 'Tool for integration testing' },
        z.object({ input: z.string() }),
        undefined,
        async (input: { input: string }) => ({
          success: true,
          data: { output: input.input.toUpperCase() },
          content: [{ type: 'text' as const, text: input.input.toUpperCase() }]
        })
      );

      const node = createToolNodeFromTool(tool);
      expect(typeof node).toBe('function');
    });
  });
});
