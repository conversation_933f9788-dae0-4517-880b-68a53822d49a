/**
 * MCP Tool Collections
 * 
 * Pre-configured collections of MCP tools for common use cases
 * and workflows.
 */

import { MCPTool, MCPToolCollection, MCPCapabilities } from './types.js';
import { mcpToolsRegistry } from './registry.js';

/**
 * Create a thinking and analysis toolkit
 */
export function createThinkingToolkit(): MCPToolCollection {
  const tools = [
    mcpToolsRegistry.getTool('sequential_thinking'),
    mcpToolsRegistry.getTool('code_analysis'),
    mcpToolsRegistry.getTool('text_processing')
  ].filter(Boolean) as MCPTool[];

  return {
    name: 'Thinking & Analysis Toolkit',
    description: 'Comprehensive toolkit for structured thinking, analysis, and problem-solving',
    tools,
    capabilities: {
      callTool: true,
      logging: true,
      listTools: true
    },
    version: '1.0.0'
  };
}

/**
 * Create a research and information toolkit
 */
export function createResearchToolkit(): MCPToolCollection {
  const tools = [
    mcpToolsRegistry.getTool('context7'),
    mcpToolsRegistry.getTool('web_search'),
    mcpToolsRegistry.getTool('memory_management'),
    mcpToolsRegistry.getTool('text_processing')
  ].filter(Boolean) as MCPTool[];

  return {
    name: 'Research & Information Toolkit',
    description: 'Tools for comprehensive research, information gathering, and knowledge management',
    tools,
    capabilities: {
      callTool: true,
      listResources: true,
      readResource: true,
      logging: true
    },
    version: '1.0.0'
  };
}

/**
 * Create a development toolkit
 */
export function createDevelopmentToolkit(): MCPToolCollection {
  const tools = [
    mcpToolsRegistry.getTool('file_operations'),
    mcpToolsRegistry.getTool('code_analysis'),
    mcpToolsRegistry.getTool('api_client'),
    mcpToolsRegistry.getTool('data_processing'),
    mcpToolsRegistry.getTool('task_management')
  ].filter(Boolean) as MCPTool[];

  return {
    name: 'Development Toolkit',
    description: 'Essential tools for software development, code analysis, and project management',
    tools,
    capabilities: {
      callTool: true,
      listResources: true,
      readResource: true,
      logging: true
    },
    version: '1.0.0'
  };
}

/**
 * Create a productivity toolkit
 */
export function createProductivityToolkit(): MCPToolCollection {
  const tools = [
    mcpToolsRegistry.getTool('task_management'),
    mcpToolsRegistry.getTool('memory_management'),
    mcpToolsRegistry.getTool('file_operations'),
    mcpToolsRegistry.getTool('text_processing'),
    mcpToolsRegistry.getTool('data_processing')
  ].filter(Boolean) as MCPTool[];

  return {
    name: 'Productivity Toolkit',
    description: 'Tools for task management, organization, and productivity enhancement',
    tools,
    capabilities: {
      callTool: true,
      listResources: true,
      logging: true
    },
    version: '1.0.0'
  };
}

/**
 * Create an AI assistant toolkit
 */
export function createAIAssistantToolkit(): MCPToolCollection {
  const tools = [
    mcpToolsRegistry.getTool('sequential_thinking'),
    mcpToolsRegistry.getTool('context7'),
    mcpToolsRegistry.getTool('web_search'),
    mcpToolsRegistry.getTool('memory_management'),
    mcpToolsRegistry.getTool('text_processing'),
    mcpToolsRegistry.getTool('task_management')
  ].filter(Boolean) as MCPTool[];

  return {
    name: 'AI Assistant Toolkit',
    description: 'Core tools for building intelligent AI assistants with thinking, memory, and task capabilities',
    tools,
    capabilities: {
      callTool: true,
      listResources: true,
      readResource: true,
      logging: true,
      sampling: true
    },
    version: '1.0.0'
  };
}

/**
 * Create a data science toolkit
 */
export function createDataScienceToolkit(): MCPToolCollection {
  const tools = [
    mcpToolsRegistry.getTool('data_processing'),
    mcpToolsRegistry.getTool('text_processing'),
    mcpToolsRegistry.getTool('code_analysis'),
    mcpToolsRegistry.getTool('api_client'),
    mcpToolsRegistry.getTool('file_operations')
  ].filter(Boolean) as MCPTool[];

  return {
    name: 'Data Science Toolkit',
    description: 'Tools for data processing, analysis, and scientific computing workflows',
    tools,
    capabilities: {
      callTool: true,
      listResources: true,
      logging: true
    },
    version: '1.0.0'
  };
}

/**
 * Create a content creation toolkit
 */
export function createContentCreationToolkit(): MCPToolCollection {
  const tools = [
    mcpToolsRegistry.getTool('text_processing'),
    mcpToolsRegistry.getTool('web_search'),
    mcpToolsRegistry.getTool('context7'),
    mcpToolsRegistry.getTool('memory_management'),
    mcpToolsRegistry.getTool('file_operations')
  ].filter(Boolean) as MCPTool[];

  return {
    name: 'Content Creation Toolkit',
    description: 'Tools for content research, writing, editing, and publishing workflows',
    tools,
    capabilities: {
      callTool: true,
      listResources: true,
      readResource: true,
      logging: true
    },
    version: '1.0.0'
  };
}

/**
 * Create a minimal toolkit for basic AI functionality
 */
export function createMinimalToolkit(): MCPToolCollection {
  const tools = [
    mcpToolsRegistry.getTool('sequential_thinking'),
    mcpToolsRegistry.getTool('memory_management'),
    mcpToolsRegistry.getTool('text_processing')
  ].filter(Boolean) as MCPTool[];

  return {
    name: 'Minimal Toolkit',
    description: 'Essential tools for basic AI functionality with thinking and memory capabilities',
    tools,
    capabilities: {
      callTool: true,
      logging: true
    },
    version: '1.0.0'
  };
}

/**
 * Create a complete enterprise toolkit
 */
export function createEnterpriseToolkit(): MCPToolCollection {
  const tools = mcpToolsRegistry.getAllTools();

  return {
    name: 'Enterprise Toolkit',
    description: 'Complete collection of all MCP tools for enterprise AI applications',
    tools,
    capabilities: {
      callTool: true,
      listTools: true,
      listResources: true,
      readResource: true,
      subscribeResource: true,
      listPrompts: true,
      getPrompt: true,
      logging: true,
      sampling: true
    },
    version: '1.0.0'
  };
}

/**
 * Toolkit factory for creating custom collections
 */
export class ToolkitFactory {
  /**
   * Create a custom toolkit from tool names
   */
  static createCustomToolkit(
    name: string,
    description: string,
    toolNames: string[],
    version = '1.0.0'
  ): MCPToolCollection {
    const tools = toolNames
      .map(name => mcpToolsRegistry.getTool(name))
      .filter(Boolean) as MCPTool[];

    // Aggregate capabilities from all tools
    const capabilities: MCPCapabilities = {
      callTool: true,
      logging: true
    };

    tools.forEach(tool => {
      const toolCapabilities = tool.getCapabilities();
      Object.entries(toolCapabilities).forEach(([capability, enabled]) => {
        if (enabled) {
          capabilities[capability as keyof MCPCapabilities] = true;
        }
      });
    });

    return {
      name,
      description,
      tools,
      capabilities,
      version
    };
  }

  /**
   * Create a toolkit by category
   */
  static createCategoryToolkit(
    category: string,
    name?: string,
    description?: string
  ): MCPToolCollection {
    const tools = mcpToolsRegistry.getAllTools().filter(
      tool => tool.config.category === category
    );

    return {
      name: name || `${category.charAt(0).toUpperCase() + category.slice(1)} Toolkit`,
      description: description || `Tools for ${category} operations`,
      tools,
      capabilities: {
        callTool: true,
        logging: true,
        listTools: true
      },
      version: '1.0.0'
    };
  }

  /**
   * Create a toolkit by tags
   */
  static createTagToolkit(
    tags: string[],
    name: string,
    description: string
  ): MCPToolCollection {
    const tools = mcpToolsRegistry.getToolsByTags(tags);

    return {
      name,
      description,
      tools,
      capabilities: {
        callTool: true,
        logging: true,
        listTools: true
      },
      version: '1.0.0'
    };
  }

  /**
   * Merge multiple toolkits
   */
  static mergeToolkits(
    toolkits: MCPToolCollection[],
    name: string,
    description: string
  ): MCPToolCollection {
    const allTools = new Map<string, MCPTool>();
    const mergedCapabilities: MCPCapabilities = {};

    toolkits.forEach(toolkit => {
      // Add unique tools
      toolkit.tools.forEach(tool => {
        allTools.set(tool.config.name, tool);
      });

      // Merge capabilities
      Object.entries(toolkit.capabilities).forEach(([capability, enabled]) => {
        if (enabled) {
          mergedCapabilities[capability as keyof MCPCapabilities] = true;
        }
      });
    });

    return {
      name,
      description,
      tools: Array.from(allTools.values()),
      capabilities: mergedCapabilities,
      version: '1.0.0'
    };
  }
}

/**
 * Pre-defined toolkit configurations
 */
export const ToolkitConfigurations = {
  thinking: createThinkingToolkit,
  research: createResearchToolkit,
  development: createDevelopmentToolkit,
  productivity: createProductivityToolkit,
  aiAssistant: createAIAssistantToolkit,
  dataScience: createDataScienceToolkit,
  contentCreation: createContentCreationToolkit,
  minimal: createMinimalToolkit,
  enterprise: createEnterpriseToolkit
};

/**
 * Get all available toolkit configurations
 */
export function getAllToolkitConfigurations(): Record<string, () => MCPToolCollection> {
  return ToolkitConfigurations;
}

/**
 * Get toolkit by name
 */
export function getToolkitByName(name: keyof typeof ToolkitConfigurations): MCPToolCollection {
  const factory = ToolkitConfigurations[name];
  if (!factory) {
    throw new Error(`Unknown toolkit configuration: ${name}`);
  }
  return factory();
}

/**
 * List all available toolkit names
 */
export function listAvailableToolkits(): string[] {
  return Object.keys(ToolkitConfigurations);
}
