import { z } from 'zod';
import { BaseTool } from '../base.js';
import { <PERSON>l<PERSON>onfig, ToolContext, ToolExecutionResult, ToolCategory } from '../types.js';

/**
 * Web search tool input schema
 */
const WebSearchInputSchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  maxResults: z.number().min(1).max(20).optional().default(5),
  language: z.string().optional().default('en'),
  region: z.string().optional(),
  safeSearch: z.enum(['off', 'moderate', 'strict']).optional().default('moderate')
});

type WebSearchInput = z.infer<typeof WebSearchInputSchema>;

/**
 * Web search result
 */
interface WebSearchResult {
  title: string;
  url: string;
  snippet: string;
  displayUrl?: string;
  datePublished?: string;
}

/**
 * Web search tool output schema
 */
const WebSearchOutputSchema = z.object({
  query: z.string(),
  results: z.array(z.object({
    title: z.string(),
    url: z.string(),
    snippet: z.string(),
    displayUrl: z.string().optional(),
    datePublished: z.string().optional()
  })),
  totalResults: z.number(),
  searchTime: z.number()
});

type WebSearchOutput = z.infer<typeof WebSearchOutputSchema>;

/**
 * Web search tool implementation
 */
export class WebSearchTool extends BaseTool<WebSearchInput, WebSearchOutput> {
  constructor() {
    const config: ToolConfig = {
      name: 'web_search',
      title: 'Web Search',
      description: 'Search the web for information using a search engine',
      category: ToolCategory.WEB,
      tags: ['search', 'web', 'internet', 'information'],
      examples: [
        {
          description: 'Search for TypeScript tutorials',
          input: { query: 'TypeScript tutorials for beginners' },
        },
        {
          description: 'Search with specific parameters',
          input: { 
            query: 'climate change research', 
            maxResults: 10,
            language: 'en',
            safeSearch: 'strict'
          }
        }
      ]
    };

    super(config, WebSearchInputSchema, WebSearchOutputSchema);
  }

  async execute(input: WebSearchInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    const startTime = Date.now();

    try {
      // For now, we'll simulate web search results
      // In a real implementation, you would integrate with a search API like:
      // - Google Custom Search API
      // - Bing Search API
      // - DuckDuckGo API
      // - SerpAPI
      
      const mockResults: WebSearchResult[] = [
        {
          title: `Results for "${input.query}"`,
          url: `https://example.com/search?q=${encodeURIComponent(input.query)}`,
          snippet: `This is a simulated search result for the query "${input.query}". In a real implementation, this would be replaced with actual search results from a search engine API.`,
          displayUrl: 'example.com',
          datePublished: new Date().toISOString()
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 100));

      const searchTime = Date.now() - startTime;
      const results = mockResults.slice(0, input.maxResults);

      const output: WebSearchOutput = {
        query: input.query,
        results,
        totalResults: results.length,
        searchTime
      };

      return this.createSuccessResult(output, [
        {
          type: 'text',
          text: `Found ${results.length} results for "${input.query}":\n\n${
            results.map((result, index) => 
              `${index + 1}. **${result.title}**\n   ${result.url}\n   ${result.snippet}\n`
            ).join('\n')
          }`
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `Web search failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { query: input.query, searchTime: Date.now() - startTime }
      );
    }
  }
}

/**
 * URL fetch tool input schema
 */
const UrlFetchInputSchema = z.object({
  url: z.string().url('Must be a valid URL'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).optional().default('GET'),
  headers: z.record(z.string()).optional(),
  body: z.string().optional(),
  timeout: z.number().min(1000).max(30000).optional().default(10000),
  followRedirects: z.boolean().optional().default(true)
});

type UrlFetchInput = z.infer<typeof UrlFetchInputSchema>;

/**
 * URL fetch tool for retrieving web content
 */
export class UrlFetchTool extends BaseTool<UrlFetchInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'url_fetch',
      title: 'URL Fetch',
      description: 'Fetch content from a URL',
      category: ToolCategory.WEB,
      tags: ['fetch', 'http', 'url', 'content'],
      examples: [
        {
          description: 'Fetch a webpage',
          input: { url: 'https://example.com' }
        },
        {
          description: 'Fetch with custom headers',
          input: { 
            url: 'https://api.example.com/data',
            headers: { 'Authorization': 'Bearer token123' }
          }
        }
      ]
    };

    super(config, UrlFetchInputSchema, undefined);
  }

  async execute(input: UrlFetchInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), input.timeout);

      const response = await fetch(input.url, {
        method: input.method,
        headers: input.headers,
        body: input.body,
        signal: controller.signal,
        redirect: input.followRedirects ? 'follow' : 'manual'
      });

      clearTimeout(timeoutId);

      const content = await response.text();
      const contentType = response.headers.get('content-type') || 'text/plain';

      const headers: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headers[key] = value;
      });

      const result = {
        url: input.url,
        status: response.status,
        statusText: response.statusText,
        headers,
        content,
        contentType,
        size: content.length
      };

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `Successfully fetched ${input.url}\nStatus: ${response.status} ${response.statusText}\nContent-Type: ${contentType}\nSize: ${content.length} bytes\n\nContent:\n${content.slice(0, 1000)}${content.length > 1000 ? '...' : ''}`
        }
      ]);

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return this.createErrorResult(`Request timed out after ${input.timeout}ms`);
      }
      
      return this.createErrorResult(
        `Failed to fetch URL: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}
