/**
 * Base Memory System for AG3NTIC
 * 
 * Provides memory management capabilities inspired by <PERSON><PERSON><PERSON><PERSON>'s memory system,
 * with support for conversation history, summarization, and persistence.
 */

import { MCPMessage, AgentState } from '../core/types.js';
import { RunnableConfig } from '../core/runnable.js';

/**
 * Base memory interface
 */
export interface Memory {
  /** Add a message to memory */
  addMessage(message: MCPMessage): Promise<void>;
  
  /** Get all messages from memory */
  getMessages(): Promise<MCPMessage[]>;
  
  /** Clear all messages from memory */
  clear(): Promise<void>;
  
  /** Get memory variables for injection into prompts */
  getMemoryVariables(): Promise<Record<string, any>>;
  
  /** Save context from agent state */
  saveContext(state: AgentState): Promise<void>;
  
  /** Load context into agent state */
  loadContext(): Promise<Partial<AgentState>>;
}

/**
 * Memory configuration options
 */
export interface MemoryConfig {
  /** Maximum number of messages to keep */
  maxMessages?: number;
  
  /** Whether to return messages in chronological order */
  returnMessages?: boolean;
  
  /** Key for storing messages in memory variables */
  memoryKey?: string;
  
  /** Key for input in memory variables */
  inputKey?: string;
  
  /** Key for output in memory variables */
  outputKey?: string;
  
  /** Whether to include system messages */
  includeSystemMessages?: boolean;
  
  /** Custom message filter function */
  messageFilter?: (message: MCPMessage) => boolean;
}

/**
 * Base memory implementation
 */
export abstract class BaseMemory implements Memory {
  protected config: MemoryConfig;
  protected messages: MCPMessage[] = [];

  constructor(config: MemoryConfig = {}) {
    this.config = {
      maxMessages: 100,
      returnMessages: true,
      memoryKey: 'history',
      inputKey: 'input',
      outputKey: 'output',
      includeSystemMessages: true,
      ...config
    };
  }

  async addMessage(message: MCPMessage): Promise<void> {
    // Apply message filter if configured
    if (this.config.messageFilter && !this.config.messageFilter(message)) {
      return;
    }

    // Skip system messages if configured
    if (!this.config.includeSystemMessages && message.role === 'system') {
      return;
    }

    this.messages.push(message);

    // Trim messages if we exceed the limit
    if (this.config.maxMessages && this.messages.length > this.config.maxMessages) {
      await this.trimMessages();
    }
  }

  async getMessages(): Promise<MCPMessage[]> {
    return [...this.messages];
  }

  async clear(): Promise<void> {
    this.messages = [];
  }

  async getMemoryVariables(): Promise<Record<string, any>> {
    const messages = await this.getMessages();
    
    if (this.config.returnMessages) {
      return {
        [this.config.memoryKey!]: messages
      };
    } else {
      // Convert messages to string format
      const historyString = messages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');
      
      return {
        [this.config.memoryKey!]: historyString
      };
    }
  }

  async saveContext(state: AgentState): Promise<void> {
    // Add all new messages from state
    for (const message of state.messages) {
      await this.addMessage(message);
    }
  }

  async loadContext(): Promise<Partial<AgentState>> {
    const messages = await this.getMessages();
    return { messages };
  }

  protected async trimMessages(): Promise<void> {
    // Default implementation: remove oldest messages
    const excess = this.messages.length - this.config.maxMessages!;
    if (excess > 0) {
      this.messages = this.messages.slice(excess);
    }
  }
}

/**
 * Simple conversation buffer memory
 */
export class ConversationBufferMemory extends BaseMemory {
  constructor(config: MemoryConfig = {}) {
    super(config);
  }
}

/**
 * Conversation buffer window memory (keeps only last N messages)
 */
export class ConversationBufferWindowMemory extends BaseMemory {
  private windowSize: number;

  constructor(windowSize: number = 10, config: MemoryConfig = {}) {
    super({ ...config, maxMessages: windowSize });
    this.windowSize = windowSize;
  }

  protected async trimMessages(): Promise<void> {
    if (this.messages.length > this.windowSize) {
      this.messages = this.messages.slice(-this.windowSize);
    }
  }
}

/**
 * Conversation summary memory (summarizes old messages)
 */
export class ConversationSummaryMemory extends BaseMemory {
  private summary: string = '';
  private summaryMessageCount: number = 0;
  private summarizeThreshold: number;
  private summarizer?: (messages: MCPMessage[]) => Promise<string>;

  constructor(
    summarizeThreshold: number = 20,
    summarizer?: (messages: MCPMessage[]) => Promise<string>,
    config: MemoryConfig = {}
  ) {
    super(config);
    this.summarizeThreshold = summarizeThreshold;
    this.summarizer = summarizer;
  }

  async addMessage(message: MCPMessage): Promise<void> {
    await super.addMessage(message);

    // Check if we need to summarize
    if (this.messages.length >= this.summarizeThreshold) {
      await this.summarizeMessages();
    }
  }

  async getMemoryVariables(): Promise<Record<string, any>> {
    const variables = await super.getMemoryVariables();
    
    if (this.summary) {
      variables.summary = this.summary;
    }
    
    return variables;
  }

  private async summarizeMessages(): Promise<void> {
    if (!this.summarizer) {
      // Default summarizer (simple truncation)
      this.summary = `Previous conversation summary: ${this.messages.length} messages exchanged.`;
      this.summaryMessageCount = this.messages.length;
      this.messages = this.messages.slice(-5); // Keep last 5 messages
      return;
    }

    try {
      // Get messages to summarize (all but the last few)
      const messagesToSummarize = this.messages.slice(0, -5);
      const newSummary = await this.summarizer(messagesToSummarize);
      
      // Update summary
      if (this.summary) {
        this.summary = `${this.summary}\n\n${newSummary}`;
      } else {
        this.summary = newSummary;
      }
      
      this.summaryMessageCount += messagesToSummarize.length;
      
      // Keep only the recent messages
      this.messages = this.messages.slice(-5);
    } catch (error) {
      console.error('Error summarizing messages:', error);
      // Fallback to simple truncation
      this.messages = this.messages.slice(-10);
    }
  }

  getSummary(): string {
    return this.summary;
  }

  getSummaryMessageCount(): number {
    return this.summaryMessageCount;
  }
}

/**
 * Read-only shared memory (prevents modifications)
 */
export class ReadOnlySharedMemory implements Memory {
  constructor(private sourceMemory: Memory) {}

  async addMessage(message: MCPMessage): Promise<void> {
    // Read-only: do nothing
    console.warn('Attempted to add message to read-only memory');
  }

  async getMessages(): Promise<MCPMessage[]> {
    return this.sourceMemory.getMessages();
  }

  async clear(): Promise<void> {
    // Read-only: do nothing
    console.warn('Attempted to clear read-only memory');
  }

  async getMemoryVariables(): Promise<Record<string, any>> {
    return this.sourceMemory.getMemoryVariables();
  }

  async saveContext(state: AgentState): Promise<void> {
    // Read-only: do nothing
    console.warn('Attempted to save context to read-only memory');
  }

  async loadContext(): Promise<Partial<AgentState>> {
    return this.sourceMemory.loadContext();
  }
}

/**
 * Memory store interface for cross-thread persistence
 */
export interface MemoryStore {
  get(threadId: string): Promise<Memory | null>;
  set(threadId: string, memory: Memory): Promise<void>;
  delete(threadId: string): Promise<void>;
  list(): Promise<string[]>;
  clear(): Promise<void>;
}

/**
 * In-memory store implementation
 */
export class InMemoryStore implements MemoryStore {
  private store = new Map<string, Memory>();

  async get(threadId: string): Promise<Memory | null> {
    return this.store.get(threadId) || null;
  }

  async set(threadId: string, memory: Memory): Promise<void> {
    this.store.set(threadId, memory);
  }

  async delete(threadId: string): Promise<void> {
    this.store.delete(threadId);
  }

  async list(): Promise<string[]> {
    return Array.from(this.store.keys());
  }

  async clear(): Promise<void> {
    this.store.clear();
  }
}

/**
 * Memory manager for handling multiple threads
 */
export class MemoryManager {
  private store: MemoryStore;
  private memoryFactory: () => Memory;

  constructor(
    store: MemoryStore = new InMemoryStore(),
    memoryFactory: () => Memory = () => new ConversationBufferMemory()
  ) {
    this.store = store;
    this.memoryFactory = memoryFactory;
  }

  async getMemory(threadId: string): Promise<Memory> {
    let memory = await this.store.get(threadId);
    
    if (!memory) {
      memory = this.memoryFactory();
      await this.store.set(threadId, memory);
    }
    
    return memory;
  }

  async deleteMemory(threadId: string): Promise<void> {
    await this.store.delete(threadId);
  }

  async listThreads(): Promise<string[]> {
    return this.store.list();
  }

  async clearAllMemories(): Promise<void> {
    await this.store.clear();
  }
}

/**
 * Utility functions for memory management
 */
export const MemoryUtils = {
  /**
   * Create a conversation buffer memory
   */
  buffer(config?: MemoryConfig): ConversationBufferMemory {
    return new ConversationBufferMemory(config);
  },

  /**
   * Create a conversation buffer window memory
   */
  bufferWindow(windowSize: number = 10, config?: MemoryConfig): ConversationBufferWindowMemory {
    return new ConversationBufferWindowMemory(windowSize, config);
  },

  /**
   * Create a conversation summary memory
   */
  summary(
    summarizeThreshold: number = 20,
    summarizer?: (messages: MCPMessage[]) => Promise<string>,
    config?: MemoryConfig
  ): ConversationSummaryMemory {
    return new ConversationSummaryMemory(summarizeThreshold, summarizer, config);
  },

  /**
   * Create a read-only wrapper around existing memory
   */
  readOnly(memory: Memory): ReadOnlySharedMemory {
    return new ReadOnlySharedMemory(memory);
  },

  /**
   * Create a memory manager
   */
  manager(
    store?: MemoryStore,
    memoryFactory?: () => Memory
  ): MemoryManager {
    return new MemoryManager(store, memoryFactory);
  },

  /**
   * Extract conversation history as a formatted string
   */
  formatHistory(messages: MCPMessage[]): string {
    return messages
      .map(msg => {
        if (msg.role === 'assistant' && msg.tool_calls) {
          const toolCalls = msg.tool_calls
            .map(tc => `[Tool: ${tc.function.name}]`)
            .join(' ');
          return `${msg.role}: ${msg.content || ''} ${toolCalls}`;
        }
        return `${msg.role}: ${msg.content}`;
      })
      .join('\n');
  },

  /**
   * Create a simple message summarizer
   */
  createSimpleSummarizer(): (messages: MCPMessage[]) => Promise<string> {
    return async (messages: MCPMessage[]) => {
      const messageCount = messages.length;
      const userMessages = messages.filter(m => m.role === 'user').length;
      const assistantMessages = messages.filter(m => m.role === 'assistant').length;
      const toolCalls = messages
        .filter(m => m.role === 'assistant' && m.tool_calls)
        .reduce((count, m) => count + (m.tool_calls?.length || 0), 0);

      return `Conversation summary: ${messageCount} total messages (${userMessages} from user, ${assistantMessages} from assistant), ${toolCalls} tool calls made.`;
    };
  }
};
