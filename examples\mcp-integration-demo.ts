/**
 * AG3NTIC MCP Integration Demo
 * 
 * This demonstrates the Model Context Protocol (MCP) integration
 * that has been added to AG3NTIC framework.
 */

// Simple MCP types for demonstration
interface MCPToolDefinition {
  name: string;
  description: string;
  inputSchema: any;
  handler: (args: any) => Promise<MCPToolResult>;
}

interface MCPToolResult {
  content: Array<{
    type: 'text' | 'image' | 'resource_link';
    text?: string;
    uri?: string;
  }>;
  isError?: boolean;
}

interface MCPServerConfig {
  name: string;
  version: string;
  description?: string;
  transport: {
    type: 'stdio' | 'http';
  };
}

// Simple MCP Server implementation for demo
class DemoMCPServer {
  private tools = new Map<string, MCPToolDefinition>();
  
  constructor(public config: MCPServerConfig) {}

  registerTool(tool: MCPToolDefinition): void {
    this.tools.set(tool.name, tool);
    console.log(`  📝 Registered MCP tool: ${tool.name}`);
  }

  async callTool(name: string, args: any): Promise<MCPToolResult> {
    const tool = this.tools.get(name);
    if (!tool) {
      return {
        content: [{ type: 'text', text: `Tool '${name}' not found` }],
        isError: true
      };
    }

    try {
      return await tool.handler(args);
    } catch (error) {
      return {
        content: [{ 
          type: 'text', 
          text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` 
        }],
        isError: true
      };
    }
  }

  listTools(): Array<{ name: string; description: string }> {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description
    }));
  }
}

// Simple MCP Client implementation for demo
class DemoMCPClient {
  constructor(private server: DemoMCPServer) {}

  async listTools(): Promise<Array<{ name: string; description: string }>> {
    return this.server.listTools();
  }

  async callTool(name: string, args: any): Promise<MCPToolResult> {
    return this.server.callTool(name, args);
  }
}

// AG3NTIC to MCP adapter
class AG3NTICToMCPAdapter {
  convertToolToMCP(ag3nticTool: any): MCPToolDefinition {
    return {
      name: ag3nticTool.config.name,
      description: ag3nticTool.config.description,
      inputSchema: {
        type: 'object',
        properties: {},
        additionalProperties: true
      },
      handler: async (args: any) => {
        try {
          const result = await ag3nticTool.execute(args);
          return {
            content: result.content || [{ 
              type: 'text', 
              text: JSON.stringify(result.data, null, 2) 
            }],
            isError: !result.success
          };
        } catch (error) {
          return {
            content: [{ 
              type: 'text', 
              text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` 
            }],
            isError: true
          };
        }
      }
    };
  }
}

// Demo function
async function demonstrateMCPIntegration() {
  console.log('🌐 AG3NTIC MCP Integration Demo\n');

  // Create MCP server
  const server = new DemoMCPServer({
    name: 'ag3ntic-demo-server',
    version: '1.0.0',
    description: 'Demo MCP server for AG3NTIC integration',
    transport: { type: 'stdio' }
  });

  console.log('🖥️  Created MCP Server:');
  console.log(`  Name: ${server.config.name}`);
  console.log(`  Version: ${server.config.version}`);
  console.log(`  Transport: ${server.config.transport.type}\n`);

  // Register some MCP tools
  console.log('📝 Registering MCP Tools:');
  
  server.registerTool({
    name: 'weather_lookup',
    description: 'Look up weather information for a location',
    inputSchema: {
      type: 'object',
      properties: {
        location: { type: 'string' }
      },
      required: ['location']
    },
    handler: async (args) => ({
      content: [{
        type: 'text',
        text: `Weather in ${args.location}: Sunny, 72°F (simulated data)`
      }]
    })
  });

  server.registerTool({
    name: 'code_search',
    description: 'Search for code examples',
    inputSchema: {
      type: 'object',
      properties: {
        query: { type: 'string' },
        language: { type: 'string' }
      },
      required: ['query']
    },
    handler: async (args) => ({
      content: [{
        type: 'text',
        text: `Found code examples for "${args.query}" in ${args.language || 'any language'} (simulated results)`
      }]
    })
  });

  server.registerTool({
    name: 'data_analysis',
    description: 'Analyze data and provide insights',
    inputSchema: {
      type: 'object',
      properties: {
        data: { type: 'array' },
        analysis_type: { type: 'string' }
      },
      required: ['data']
    },
    handler: async (args) => ({
      content: [{
        type: 'text',
        text: `Analysis of ${args.data.length} data points using ${args.analysis_type || 'default'} method: Average = ${
          args.data.reduce((a: number, b: number) => a + b, 0) / args.data.length
        } (simulated analysis)`
      }]
    })
  });

  console.log();

  // Create MCP client
  const client = new DemoMCPClient(server);

  // List available tools
  console.log('📋 Available MCP Tools:');
  const tools = await client.listTools();
  tools.forEach(tool => {
    console.log(`  • ${tool.name}: ${tool.description}`);
  });
  console.log();

  // Test tool execution
  console.log('🔧 Testing MCP Tool Execution:\n');

  // Test weather lookup
  console.log('🌤️  Weather Lookup:');
  const weatherResult = await client.callTool('weather_lookup', { location: 'San Francisco' });
  if (!weatherResult.isError) {
    console.log(`  ✅ ${weatherResult.content[0]?.text}`);
  } else {
    console.log(`  ❌ Error: ${weatherResult.content[0]?.text}`);
  }

  // Test code search
  console.log('\n🔍 Code Search:');
  const codeResult = await client.callTool('code_search', { 
    query: 'async function', 
    language: 'typescript' 
  });
  if (!codeResult.isError) {
    console.log(`  ✅ ${codeResult.content[0]?.text}`);
  } else {
    console.log(`  ❌ Error: ${codeResult.content[0]?.text}`);
  }

  // Test data analysis
  console.log('\n📊 Data Analysis:');
  const dataResult = await client.callTool('data_analysis', { 
    data: [10, 20, 30, 40, 50], 
    analysis_type: 'statistical' 
  });
  if (!dataResult.isError) {
    console.log(`  ✅ ${dataResult.content[0]?.text}`);
  } else {
    console.log(`  ❌ Error: ${dataResult.content[0]?.text}`);
  }

  // Test error handling
  console.log('\n⚠️  Testing Error Handling:');
  const errorResult = await client.callTool('non_existent_tool', {});
  if (errorResult.isError) {
    console.log(`  ✅ Correctly caught error: ${errorResult.content[0]?.text}`);
  }

  // Demonstrate AG3NTIC to MCP conversion
  console.log('\n🔄 AG3NTIC to MCP Conversion Demo:');
  
  const adapter = new AG3NTICToMCPAdapter();
  
  // Simulate an AG3NTIC tool
  const ag3nticTool = {
    config: {
      name: 'text_processor',
      description: 'Process text with various operations'
    },
    execute: async (input: any) => ({
      success: true,
      data: { processed: input.text?.toUpperCase() || 'NO TEXT' },
      content: [{ type: 'text', text: `Processed: ${input.text?.toUpperCase() || 'NO TEXT'}` }]
    })
  };

  const mcpTool = adapter.convertToolToMCP(ag3nticTool);
  server.registerTool(mcpTool);

  const conversionResult = await client.callTool('text_processor', { text: 'hello world' });
  if (!conversionResult.isError) {
    console.log(`  ✅ Converted AG3NTIC tool result: ${conversionResult.content[0]?.text}`);
  }

  console.log('\n🎉 MCP Integration Demo Complete!');
  console.log('\nKey Features Demonstrated:');
  console.log('  • MCP server creation and configuration');
  console.log('  • Tool registration with MCP protocol');
  console.log('  • Client-server communication');
  console.log('  • Structured tool execution');
  console.log('  • Error handling and reporting');
  console.log('  • AG3NTIC to MCP tool conversion');
  console.log('  • Bidirectional integration capabilities');
}

// Run the demo
if (require.main === module) {
  demonstrateMCPIntegration().catch(console.error);
}

export { demonstrateMCPIntegration };
