/**
 * MCP Tool Types and Interfaces
 * 
 * Defines types specific to MCP (Model Context Protocol) tools
 */

import { z } from 'zod';
import { Tool, Tool<PERSON>onfig, Tool<PERSON>ontext, ToolExecutionResult } from '../types.js';

/**
 * MCP Tool configuration with additional MCP-specific properties
 */
export interface MCPToolConfig extends ToolConfig {
  /** MCP server information */
  server?: {
    name: string;
    version: string;
    url?: string;
  };
  /** MCP capabilities */
  capabilities?: MCPCapabilities;
  /** MCP resource patterns */
  resources?: MCPResource[];
  /** MCP prompts */
  prompts?: MCPPrompt[];
}

/**
 * MCP capabilities that a tool supports
 */
export interface MCPCapabilities {
  /** Supports listing resources */
  listResources?: boolean;
  /** Supports reading resources */
  readResource?: boolean;
  /** Supports subscribing to resources */
  subscribeResource?: boolean;
  /** Supports listing prompts */
  listPrompts?: boolean;
  /** Supports getting prompts */
  getPrompt?: boolean;
  /** Supports listing tools */
  listTools?: boolean;
  /** Supports calling tools */
  callTool?: boolean;
  /** Supports logging */
  logging?: boolean;
  /** Supports sampling */
  sampling?: boolean;
}

/**
 * MCP resource definition
 */
export interface MCPResource {
  /** Resource URI pattern */
  uri: string;
  /** Resource name */
  name: string;
  /** Resource description */
  description?: string;
  /** MIME type */
  mimeType?: string;
  /** Resource annotations */
  annotations?: {
    audience?: ('user' | 'assistant')[];
    priority?: number;
  };
}

/**
 * MCP prompt definition
 */
export interface MCPPrompt {
  /** Prompt name */
  name: string;
  /** Prompt description */
  description?: string;
  /** Prompt arguments */
  arguments?: MCPPromptArgument[];
}

/**
 * MCP prompt argument
 */
export interface MCPPromptArgument {
  /** Argument name */
  name: string;
  /** Argument description */
  description?: string;
  /** Whether argument is required */
  required?: boolean;
}

/**
 * MCP tool execution context with additional MCP-specific data
 */
export interface MCPToolContext extends ToolContext {
  /** MCP client information */
  client?: {
    name: string;
    version: string;
  };
  /** MCP server information */
  server?: {
    name: string;
    version: string;
  };
  /** MCP session ID */
  sessionId?: string;
  /** MCP request ID */
  requestId?: string;
  /** Available resources */
  resources?: MCPResource[];
  /** Available prompts */
  prompts?: MCPPrompt[];
}

/**
 * MCP tool execution result with additional MCP-specific content
 */
export interface MCPToolExecutionResult extends ToolExecutionResult {
  /** MCP-specific content types */
  content?: Array<{
    type: 'text' | 'image' | 'resource' | 'tool_result' | 'progress';
    text?: string;
    uri?: string;
    name?: string;
    mimeType?: string;
    description?: string;
    /** Progress information for long-running operations */
    progress?: {
      current: number;
      total: number;
      message?: string;
    };
    /** Tool result metadata */
    toolResult?: {
      toolName: string;
      isError: boolean;
    };
  }>;
  /** MCP resources created or modified */
  resources?: MCPResource[];
  /** MCP prompts created or modified */
  prompts?: MCPPrompt[];
}

/**
 * Base MCP tool interface
 */
export interface MCPTool<TInput = any, TOutput = any> extends Tool<TInput, TOutput> {
  /** MCP-specific configuration */
  config: MCPToolConfig;
  
  /** Execute with MCP context */
  execute(input: TInput, context?: MCPToolContext): Promise<MCPToolExecutionResult>;
  
  /** Get MCP capabilities */
  getCapabilities(): MCPCapabilities;
  
  /** List available resources */
  listResources?(): Promise<MCPResource[]>;
  
  /** Read a specific resource */
  readResource?(uri: string): Promise<{ contents: any; mimeType?: string }>;
  
  /** List available prompts */
  listPrompts?(): Promise<MCPPrompt[]>;
  
  /** Get a specific prompt */
  getPrompt?(name: string, arguments?: Record<string, any>): Promise<{ messages: any[] }>;
}

/**
 * MCP tool categories
 */
export enum MCPToolCategory {
  THINKING = 'thinking',
  CONTEXT = 'context',
  SEARCH = 'search',
  FILE = 'file',
  CODE = 'code',
  MEMORY = 'memory',
  TASK = 'task',
  API = 'api',
  DATA = 'data',
  TEXT = 'text',
  WEB = 'web',
  SYSTEM = 'system',
  AI = 'ai',
  UTILITY = 'utility'
}

/**
 * Common MCP tool input schemas
 */
export const MCPSchemas = {
  /** Basic text input */
  textInput: z.object({
    text: z.string().describe('Input text to process')
  }),

  /** Query input with optional parameters */
  queryInput: z.object({
    query: z.string().describe('Search or query string'),
    limit: z.number().optional().describe('Maximum number of results'),
    offset: z.number().optional().describe('Offset for pagination')
  }),

  /** File path input */
  fileInput: z.object({
    path: z.string().describe('File path'),
    encoding: z.string().optional().describe('File encoding (default: utf-8)')
  }),

  /** URL input */
  urlInput: z.object({
    url: z.string().url().describe('URL to process'),
    timeout: z.number().optional().describe('Request timeout in milliseconds')
  }),

  /** Code input */
  codeInput: z.object({
    code: z.string().describe('Code to analyze or process'),
    language: z.string().optional().describe('Programming language'),
    filename: z.string().optional().describe('Filename for context')
  }),

  /** Thinking input */
  thinkingInput: z.object({
    problem: z.string().describe('Problem or question to think about'),
    context: z.string().optional().describe('Additional context'),
    steps: z.number().optional().describe('Number of thinking steps (default: 5)')
  }),

  /** Memory input */
  memoryInput: z.object({
    key: z.string().describe('Memory key'),
    value: z.any().optional().describe('Value to store (omit for retrieval)'),
    namespace: z.string().optional().describe('Memory namespace')
  }),

  /** Task input */
  taskInput: z.object({
    title: z.string().describe('Task title'),
    description: z.string().optional().describe('Task description'),
    priority: z.enum(['low', 'medium', 'high']).optional().describe('Task priority'),
    dueDate: z.string().optional().describe('Due date (ISO string)')
  })
};

/**
 * Common MCP tool output schemas
 */
export const MCPOutputSchemas = {
  /** Basic result output */
  basicResult: z.object({
    result: z.any().describe('Operation result'),
    metadata: z.record(z.any()).optional().describe('Additional metadata')
  }),

  /** Search results output */
  searchResults: z.object({
    results: z.array(z.object({
      title: z.string(),
      description: z.string().optional(),
      url: z.string().optional(),
      score: z.number().optional()
    })),
    total: z.number().optional(),
    hasMore: z.boolean().optional()
  }),

  /** File operation result */
  fileResult: z.object({
    path: z.string(),
    size: z.number().optional(),
    modified: z.string().optional(),
    content: z.string().optional()
  }),

  /** Thinking result */
  thinkingResult: z.object({
    thoughts: z.array(z.object({
      step: z.number(),
      thought: z.string(),
      reasoning: z.string().optional()
    })),
    conclusion: z.string(),
    confidence: z.number().optional()
  }),

  /** Task result */
  taskResult: z.object({
    id: z.string(),
    title: z.string(),
    status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']),
    created: z.string(),
    updated: z.string().optional()
  })
};

/**
 * MCP tool error types
 */
export enum MCPErrorType {
  INVALID_REQUEST = 'InvalidRequest',
  METHOD_NOT_FOUND = 'MethodNotFound',
  INVALID_PARAMS = 'InvalidParams',
  INTERNAL_ERROR = 'InternalError',
  PARSE_ERROR = 'ParseError',
  RESOURCE_NOT_FOUND = 'ResourceNotFound',
  TOOL_NOT_FOUND = 'ToolNotFound',
  PROMPT_NOT_FOUND = 'PromptNotFound',
  UNAUTHORIZED = 'Unauthorized',
  RATE_LIMITED = 'RateLimited'
}

/**
 * MCP error class
 */
export class MCPError extends Error {
  constructor(
    public type: MCPErrorType,
    message: string,
    public code?: number,
    public data?: any
  ) {
    super(message);
    this.name = 'MCPError';
  }
}

/**
 * MCP tool metadata with additional MCP-specific information
 */
export interface MCPToolMetadata {
  name: string;
  title: string;
  description: string;
  category: MCPToolCategory;
  capabilities: MCPCapabilities;
  inputSchema: z.ZodSchema;
  outputSchema?: z.ZodSchema;
  resources?: MCPResource[];
  prompts?: MCPPrompt[];
  version: string;
  author?: string;
  server?: {
    name: string;
    version: string;
    url?: string;
  };
}

/**
 * MCP tool collection interface
 */
export interface MCPToolCollection {
  name: string;
  description: string;
  tools: MCPTool[];
  capabilities: MCPCapabilities;
  version: string;
}

/**
 * MCP server configuration
 */
export interface MCPServerConfig {
  name: string;
  version: string;
  url?: string;
  capabilities: MCPCapabilities;
  tools: string[];
  resources?: MCPResource[];
  prompts?: MCPPrompt[];
}

/**
 * MCP client configuration
 */
export interface MCPClientConfig {
  name: string;
  version: string;
  capabilities: MCPCapabilities;
  timeout?: number;
  retries?: number;
}
