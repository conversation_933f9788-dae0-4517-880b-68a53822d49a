# 📦 AG3NTIC NPM Package Publishing Guide

This guide covers how to publish AG3NTIC to npm and maintain the package.

## 🚀 Quick Publishing Steps

### 1. Prerequisites

```bash
# Install dependencies
npm install

# Ensure you're logged into npm
npm whoami
# If not logged in:
npm login
```

### 2. Pre-publish Checklist

- [ ] Update version in `package.json`
- [ ] Update `CHANGELOG.md` with new features/fixes
- [ ] Run tests: `npm test`
- [ ] Run linting: `npm run lint`
- [ ] Build package: `npm run build`
- [ ] Test package locally: `npm pack` and test the tarball

### 3. Publish to npm

```bash
# For stable releases
npm publish

# For beta releases
npm publish --tag beta

# For alpha releases  
npm publish --tag alpha
```

## 📋 Detailed Publishing Process

### Version Management

AG3NTIC follows [Semantic Versioning](https://semver.org/):

- **MAJOR** (1.0.0 → 2.0.0): Breaking changes
- **MINOR** (1.0.0 → 1.1.0): New features, backwards compatible
- **PATCH** (1.0.0 → 1.0.1): Bug fixes, backwards compatible

```bash
# Update version automatically
npm version patch   # 1.0.0 → 1.0.1
npm version minor   # 1.0.0 → 1.1.0
npm version major   # 1.0.0 → 2.0.0

# Or update manually in package.json
```

### Build Process

The build process creates both CommonJS and ESM versions:

```bash
npm run build
```

This will:
1. Clean previous builds
2. Build CommonJS version (`dist/*.js`)
3. Build ESM version (`dist/*.esm.js`)
4. Create submodule package.json files
5. Generate TypeScript declarations

### Testing Before Publishing

```bash
# Run all tests
npm test

# Run linting
npm run lint

# Build and test locally
npm run build
npm pack

# Test the generated tarball
npm install ./ag3ntic-1.0.0.tgz
```

### Publishing Workflow

1. **Development**
   ```bash
   # Work on features
   git checkout -b feature/new-feature
   # ... make changes ...
   git commit -m "feat: add new feature"
   git push origin feature/new-feature
   # Create PR, review, merge
   ```

2. **Release Preparation**
   ```bash
   git checkout main
   git pull origin main
   
   # Update version
   npm version minor
   
   # Update CHANGELOG.md
   # ... document changes ...
   
   git add CHANGELOG.md
   git commit -m "docs: update changelog for v1.1.0"
   git push origin main
   ```

3. **Publishing**
   ```bash
   # Final checks
   npm run build
   npm test
   npm run lint
   
   # Publish
   npm publish
   
   # Push version tag
   git push origin --tags
   ```

## 🏷️ Release Tags

### Stable Releases
```bash
npm publish
# Publishes to 'latest' tag
```

### Pre-releases
```bash
# Beta releases
npm version prerelease --preid=beta
npm publish --tag beta

# Alpha releases  
npm version prerelease --preid=alpha
npm publish --tag alpha

# Release candidates
npm version prerelease --preid=rc
npm publish --tag rc
```

### Installing Pre-releases
```bash
# Install beta version
npm install ag3ntic@beta

# Install specific pre-release
npm install ag3ntic@1.1.0-beta.1
```

## 📊 Package Analytics

Monitor package performance:

- **npm stats**: https://www.npmjs.com/package/ag3ntic
- **Download stats**: https://npm-stat.com/charts.html?package=ag3ntic
- **Bundle size**: https://bundlephobia.com/package/ag3ntic

## 🔧 Package Configuration

### Key Files

- **`package.json`** - Main package configuration
- **`tsconfig.build.json`** - TypeScript build config for CommonJS
- **`tsconfig.esm.json`** - TypeScript build config for ESM
- **`.npmignore`** - Files to exclude from npm package
- **`scripts/build.js`** - Custom build script

### Exports Configuration

The package supports multiple export patterns:

```json
{
  "exports": {
    ".": {
      "import": "./dist/index.esm.js",
      "require": "./dist/index.js", 
      "types": "./dist/index.d.ts"
    },
    "./core": {
      "import": "./dist/core/index.esm.js",
      "require": "./dist/core/index.js",
      "types": "./dist/core/index.d.ts"
    },
    "./tools": {
      "import": "./dist/tools/index.esm.js", 
      "require": "./dist/tools/index.js",
      "types": "./dist/tools/index.d.ts"
    },
    "./orchestration": {
      "import": "./dist/orchestration/index.esm.js",
      "require": "./dist/orchestration/index.js", 
      "types": "./dist/orchestration/index.d.ts"
    }
  }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clean and rebuild
   npm run build:clean
   npm run build
   ```

2. **Type Declaration Issues**
   ```bash
   # Check TypeScript config
   npx tsc --noEmit
   ```

3. **Package Size Issues**
   ```bash
   # Analyze bundle size
   npm pack --dry-run
   ```

4. **Publishing Permissions**
   ```bash
   # Check npm access
   npm whoami
   npm access list packages
   ```

### Recovery Steps

If a publish fails:

1. **Fix the issue**
2. **Increment patch version**: `npm version patch`
3. **Republish**: `npm publish`

## 📈 Maintenance

### Regular Tasks

- **Weekly**: Monitor download stats and issues
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Review and update documentation

### Dependency Updates

```bash
# Check for outdated dependencies
npm outdated

# Update dependencies
npm update

# Update dev dependencies
npm update --dev
```

### Security

```bash
# Check for vulnerabilities
npm audit

# Fix vulnerabilities
npm audit fix
```

## 🌟 Best Practices

1. **Always test before publishing**
2. **Use semantic versioning consistently**
3. **Keep CHANGELOG.md updated**
4. **Monitor package performance**
5. **Respond to issues promptly**
6. **Maintain backwards compatibility**
7. **Document breaking changes clearly**

## 📞 Support

For publishing issues:
- **GitHub Issues**: https://github.com/ag3ntic/ag3ntic/issues
- **npm Support**: https://www.npmjs.com/support
- **Community**: Discord/Slack channels
