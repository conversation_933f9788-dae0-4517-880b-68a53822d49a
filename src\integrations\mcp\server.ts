// src/integrations/mcp/server.ts

import {
  MCPTool,
  MCPToolResult,
  MCPResource,
  MCPResourceContents,
  MCPPrompt,
  MCPPromptResult,
  MCPContent
} from '../../core/types';

/**
 * Check if MCP SDK is available
 */
async function checkMCPSDK(): Promise<void> {
  try {
    await import('@modelcontextprotocol/sdk/server/mcp.js');
  } catch (error) {
    throw new Error(
      'MCP SDK not found. Install with: npm install @modelcontextprotocol/sdk\n' +
      'Or use AG3NTIC without MCP server capabilities.'
    );
  }
}

/**
 * MCP Server Configuration
 */
export interface MCPServerConfig {
  name: string;
  version: string;
  port?: number;
  host?: string;
  enableCORS?: boolean;
  enableAuth?: boolean;
  capabilities?: {
    tools?: boolean;
    resources?: boolean;
    prompts?: boolean;
    logging?: boolean;
  };
}

/**
 * Tool handler function type
 */
export type MCPToolHandler = (args: Record<string, unknown>) => Promise<MCPToolResult>;

/**
 * Resource handler function type
 */
export type MCPResourceHandler = (uri: string) => Promise<MCPResourceContents>;

/**
 * Prompt handler function type
 */
export type MCPPromptHandler = (args: Record<string, unknown>) => Promise<MCPPromptResult>;

/**
 * MCP Server for exposing AG3NTIC capabilities as an MCP server
 * 
 * This allows AG3NTIC to:
 * - Expose its tools, resources, and prompts via MCP protocol
 * - Be consumed by other AI systems and MCP clients
 * - Provide standardized access to AG3NTIC capabilities
 */
export class MCPServer {
  private config: MCPServerConfig;
  private server: any; // Will be the actual MCP SDK server
  private tools: Map<string, { definition: MCPTool; handler: MCPToolHandler }> = new Map();
  private resources: Map<string, { definition: MCPResource; handler: MCPResourceHandler }> = new Map();
  private prompts: Map<string, { definition: MCPPrompt; handler: MCPPromptHandler }> = new Map();
  private running: boolean = false;

  constructor(config: MCPServerConfig) {
    this.config = {
      port: 3000,
      host: 'localhost',
      enableCORS: true,
      enableAuth: false,
      capabilities: {
        tools: true,
        resources: true,
        prompts: true,
        logging: true
      },
      ...config
    };
  }

  /**
   * Register a tool with the MCP server
   */
  registerTool(name: string, definition: MCPTool, handler: MCPToolHandler): void {
    this.tools.set(name, { definition, handler });
    console.log(`🔧 Registered MCP tool: ${name}`);
  }

  /**
   * Register a resource with the MCP server
   */
  registerResource(name: string, definition: MCPResource, handler: MCPResourceHandler): void {
    this.resources.set(name, { definition, handler });
    console.log(`📄 Registered MCP resource: ${name}`);
  }

  /**
   * Register a prompt with the MCP server
   */
  registerPrompt(name: string, definition: MCPPrompt, handler: MCPPromptHandler): void {
    this.prompts.set(name, { definition, handler });
    console.log(`💬 Registered MCP prompt: ${name}`);
  }

  /**
   * Start the MCP server
   */
  async start(): Promise<void> {
    try {
      // Check if MCP SDK is available
      await checkMCPSDK();

      // Dynamic import to avoid requiring MCP SDK if not used
      const { McpServer } = await import('@modelcontextprotocol/sdk/server/mcp.js');
      
      this.server = new McpServer({
        name: this.config.name,
        version: this.config.version
      });

      // Register all tools
      for (const [name, { definition, handler }] of this.tools) {
        this.server.registerTool(
          name,
          {
            title: definition.name,
            description: definition.description,
            inputSchema: definition.inputSchema
          },
          async (args: Record<string, unknown>) => {
            try {
              const result = await handler(args);
              return result;
            } catch (error) {
              console.error(`❌ Tool ${name} failed:`, error);
              return {
                content: [{
                  type: "text",
                  text: `Error: ${error instanceof Error ? error.message : String(error)}`
                }],
                isError: true
              };
            }
          }
        );
      }

      // Register all resources
      for (const [name, { definition, handler }] of this.resources) {
        this.server.registerResource(
          name,
          definition.uri,
          {
            title: definition.name,
            description: definition.description,
            mimeType: definition.mimeType
          },
          async (uri: string) => {
            try {
              const result = await handler(uri);
              return result;
            } catch (error) {
              console.error(`❌ Resource ${name} failed:`, error);
              return {
                contents: [{
                  uri,
                  text: `Error: ${error instanceof Error ? error.message : String(error)}`,
                  mimeType: 'text/plain'
                }]
              };
            }
          }
        );
      }

      // Register all prompts
      for (const [name, { definition, handler }] of this.prompts) {
        this.server.registerPrompt(
          name,
          {
            title: definition.name,
            description: definition.description,
            argsSchema: definition.arguments ? 
              definition.arguments.reduce((schema, arg) => {
                schema[arg.name] = { type: 'string', description: arg.description };
                return schema;
              }, {} as Record<string, any>) : {}
          },
          async (args: Record<string, unknown>) => {
            try {
              const result = await handler(args);
              return result;
            } catch (error) {
              console.error(`❌ Prompt ${name} failed:`, error);
              return {
                description: `Error in prompt ${name}`,
                messages: [{
                  role: 'assistant' as const,
                  content: {
                    type: 'text',
                    text: `Error: ${error instanceof Error ? error.message : String(error)}`
                  }
                }]
              };
            }
          }
        );
      }

      // Start with stdio transport by default
      const { StdioServerTransport } = await import('@modelcontextprotocol/sdk/server/stdio.js');
      const transport = new StdioServerTransport();
      await this.server.connect(transport);
      
      this.running = true;
      console.log(`🚀 MCP Server "${this.config.name}" started with stdio transport`);
      
    } catch (error) {
      console.error('❌ Failed to start MCP server:', error);
      throw error;
    }
  }

  /**
   * Start HTTP server (for web-based clients)
   */
  async startHTTP(): Promise<void> {
    try {
      // Check if MCP SDK is available
      await checkMCPSDK();

      // Dynamic import Express and MCP HTTP transport
      const express = await import('express') as any;
      const { StreamableHTTPServerTransport } = await import('@modelcontextprotocol/sdk/server/streamableHttp.js') as any;
      
      const app = express.default();
      app.use(express.default.json());

      // Enable CORS if configured
      if (this.config.enableCORS) {
        try {
          const cors = await import('cors') as any;
          app.use(cors.default({
            origin: '*',
            exposedHeaders: ['Mcp-Session-Id'],
            allowedHeaders: ['Content-Type', 'mcp-session-id'],
          }));
        } catch (error) {
          console.warn('⚠️  CORS package not found. Install with: npm install cors @types/cors');
          console.warn('⚠️  Continuing without CORS support...');
        }
      }

      // Map to store transports by session ID
      const transports: { [sessionId: string]: any } = {};

      // Handle MCP requests
      app.all('/mcp', async (req: any, res: any) => {
        const sessionId = req.headers['mcp-session-id'] as string | undefined;
        let transport: any;

        if (sessionId && transports[sessionId]) {
          transport = transports[sessionId];
        } else {
          const { randomUUID } = await import('node:crypto');
          transport = new StreamableHTTPServerTransport({
            sessionIdGenerator: () => randomUUID(),
            onsessioninitialized: (sessionId: string) => {
              transports[sessionId] = transport;
            }
          });

          transport.onclose = () => {
            if (transport.sessionId) {
              delete transports[transport.sessionId];
            }
          };

          await this.server.connect(transport);
        }

        await transport.handleRequest(req, res, req.body);
      });

      // Start the HTTP server
      const server = app.listen(this.config.port!, this.config.host!, () => {
        console.log(`🌐 MCP HTTP Server listening on http://${this.config.host}:${this.config.port}/mcp`);
      });

      this.running = true;
      return server;
      
    } catch (error) {
      console.error('❌ Failed to start MCP HTTP server:', error);
      throw error;
    }
  }

  /**
   * Stop the MCP server
   */
  async stop(): Promise<void> {
    if (this.server && this.running) {
      await this.server.close();
      this.running = false;
      console.log('🛑 MCP Server stopped');
    }
  }

  /**
   * Check if server is running
   */
  isRunning(): boolean {
    return this.running;
  }

  /**
   * Get server statistics
   */
  getStats() {
    return {
      name: this.config.name,
      version: this.config.version,
      running: this.running,
      tools: this.tools.size,
      resources: this.resources.size,
      prompts: this.prompts.size,
      capabilities: this.config.capabilities
    };
  }

  /**
   * Register AG3NTIC tool functions as MCP tools
   */
  registerAG3NTICTools(tools: Record<string, (args: any) => any>): void {
    for (const [name, toolFunction] of Object.entries(tools)) {
      this.registerTool(
        name,
        {
          name,
          description: `AG3NTIC tool: ${name}`,
          inputSchema: {
            type: "object",
            properties: {},
            required: []
          }
        },
        async (args: Record<string, unknown>) => {
          try {
            const result = await Promise.resolve(toolFunction(args));
            
            // Convert result to MCP format
            const content: MCPContent[] = [{
              type: "text",
              text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
            }];
            
            return { content };
          } catch (error) {
            return {
              content: [{
                type: "text",
                text: `Error: ${error instanceof Error ? error.message : String(error)}`
              }],
              isError: true
            };
          }
        }
      );
    }
  }
}

/**
 * Create and start an MCP server
 */
export async function createMCPServer(config: MCPServerConfig): Promise<MCPServer> {
  const server = new MCPServer(config);
  await server.start();
  return server;
}

/**
 * Create and start an MCP HTTP server
 */
export async function createMCPHTTPServer(config: MCPServerConfig): Promise<MCPServer> {
  const server = new MCPServer(config);
  await server.startHTTP();
  return server;
}
