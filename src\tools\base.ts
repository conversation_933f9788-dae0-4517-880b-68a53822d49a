import { z } from 'zod';
import {
  <PERSON><PERSON>,
  <PERSON>l<PERSON>onfig,
  ToolContext,
  ToolExecutionResult,
  ToolHandler,
  ToolMetadata,
  ToolValidationResult,
  ToolExecutionOptions
} from './types.js';

/**
 * Base tool implementation that provides common functionality
 */
export abstract class BaseTool<TInput = any, TOutput = any> implements Tool<TInput, TOutput> {
  public readonly config: ToolConfig;
  public readonly inputSchema: z.ZodSchema<TInput>;
  public readonly outputSchema: z.ZodSchema<TOutput> | undefined;

  constructor(
    config: ToolConfig,
    inputSchema: z.ZodSchema<TInput>,
    outputSchema: z.ZodSchema<TOutput> | undefined
  ) {
    this.config = {
      enabled: true,
      version: '1.0.0',
      ...config
    };
    this.inputSchema = inputSchema;
    this.outputSchema = outputSchema;
  }

  /**
   * Abstract method that must be implemented by concrete tools
   */
  abstract execute(input: TInput, context?: ToolContext): Promise<ToolExecutionResult>;

  /**
   * Validate input parameters using the tool's schema
   */
  validateInput(input: any): ToolValidationResult<TInput> {
    try {
      const data = this.inputSchema.parse(input);
      return { valid: true, data };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          error: `Input validation failed: ${error.errors.map(e => e.message).join(', ')}`
        };
      }
      return {
        valid: false,
        error: `Input validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Validate output using the tool's output schema (if defined)
   */
  validateOutput(output: any): ToolValidationResult<TOutput> {
    if (!this.outputSchema) {
      return { valid: true, data: output };
    }

    try {
      const data = this.outputSchema.parse(output);
      return { valid: true, data };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          error: `Output validation failed: ${error.errors.map(e => e.message).join(', ')}`
        };
      }
      return {
        valid: false,
        error: `Output validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get tool metadata for registration
   */
  getMetadata(): ToolMetadata {
    return {
      name: this.config.name,
      title: this.config.title,
      description: this.config.description,
      category: this.config.category,
      tags: this.config.tags,
      inputSchema: this.inputSchema,
      outputSchema: this.outputSchema,
      examples: this.config.examples,
      version: this.config.version,
      author: this.config.author,
      enabled: this.config.enabled ?? true
    };
  }

  /**
   * Execute the tool with validation and error handling
   */
  async executeWithValidation(
    input: any,
    context?: ToolContext,
    options?: ToolExecutionOptions
  ): Promise<ToolExecutionResult> {
    // Check if tool is enabled
    if (!this.config.enabled) {
      return {
        success: false,
        error: `Tool '${this.config.name}' is disabled`
      };
    }

    // Validate input if requested
    if (options?.validateInput !== false) {
      const validation = this.validateInput(input);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        };
      }
      input = validation.data;
    }

    try {
      // Execute with timeout if specified
      let result: ToolExecutionResult;
      if (options?.timeout) {
        result = await this.executeWithTimeout(input, options.timeout, context);
      } else {
        result = await this.execute(input, context);
      }

      // Validate output if requested and schema is defined
      if (options?.validateOutput && this.outputSchema && result.success) {
        const outputValidation = this.validateOutput(result.data);
        if (!outputValidation.valid) {
          return {
            success: false,
            error: outputValidation.error,
            metadata: result.metadata
          };
        }
        result.data = outputValidation.data;
      }

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: { originalError: error }
      };
    }
  }

  /**
   * Execute the tool with a timeout
   */
  private async executeWithTimeout(
    input: TInput,
    timeout: number,
    context?: ToolContext
  ): Promise<ToolExecutionResult> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Tool execution timed out after ${timeout}ms`));
      }, timeout);

      this.execute(input, context)
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Create a simple success result
   */
  protected createSuccessResult(
    data: any,
    contentOrMetadata?: ToolExecutionResult['content'] | Record<string, any>,
    metadata?: Record<string, any>
  ): ToolExecutionResult {
    // Handle overloaded parameters
    let content: ToolExecutionResult['content'];
    let meta: Record<string, any> | undefined;

    if (Array.isArray(contentOrMetadata)) {
      content = contentOrMetadata;
      meta = metadata;
    } else {
      content = [{ type: 'text', text: JSON.stringify(data, null, 2) }];
      meta = contentOrMetadata;
    }

    return {
      success: true,
      data,
      content,
      metadata: meta
    };
  }

  /**
   * Create a simple error result
   */
  protected createErrorResult(error: string, metadata?: Record<string, any>): ToolExecutionResult {
    return {
      success: false,
      error,
      metadata,
      content: [{ type: 'text', text: `Error: ${error}` }]
    };
  }


}

/**
 * Simple tool implementation using a handler function
 */
export class SimpleTool<TInput = any, TOutput = any> extends BaseTool<TInput, TOutput> {
  private handler: ToolHandler<TInput, TOutput>;

  constructor(
    config: ToolConfig,
    inputSchema: z.ZodSchema<TInput>,
    handler: ToolHandler<TInput, TOutput>,
    outputSchema: z.ZodSchema<TOutput> | undefined
  ) {
    super(config, inputSchema, outputSchema);
    this.handler = handler;
  }

  async execute(input: TInput, context?: ToolContext): Promise<ToolExecutionResult> {
    return this.handler(input, context);
  }
}

/**
 * Tool factory for creating simple tools
 */
export class ToolFactory {
  /**
   * Create a simple tool with a handler function
   */
  static create<TInput = any, TOutput = any>(
    config: ToolConfig,
    inputSchema: z.ZodSchema<TInput>,
    handler: ToolHandler<TInput, TOutput>,
    outputSchema: z.ZodSchema<TOutput> | undefined
  ): Tool<TInput, TOutput> {
    return new SimpleTool(config, inputSchema, handler, outputSchema);
  }

  /**
   * Create a tool from a class that extends BaseTool
   */
  static createFromClass<T extends BaseTool>(
    ToolClass: new (...args: any[]) => T,
    ...args: any[]
  ): T {
    return new ToolClass(...args);
  }
}
