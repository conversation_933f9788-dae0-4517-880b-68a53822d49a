name: Publish to NPM

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Release tag (latest, beta, alpha)'
        required: true
        default: 'latest'
        type: choice
        options:
          - latest
          - beta
          - alpha

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20, 21]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run linting
        run: npm run lint
        
      - name: Run tests
        run: npm test
        
      - name: Build package
        run: npm run build

  publish:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build package
        run: npm run build
        
      - name: Determine release tag
        id: tag
        run: |
          if [[ ${{ github.ref }} =~ refs/tags/v.*-beta.* ]]; then
            echo "tag=beta" >> $GITHUB_OUTPUT
          elif [[ ${{ github.ref }} =~ refs/tags/v.*-alpha.* ]]; then
            echo "tag=alpha" >> $GITHUB_OUTPUT
          elif [[ ${{ github.ref }} =~ refs/tags/v.*-rc.* ]]; then
            echo "tag=rc" >> $GITHUB_OUTPUT
          else
            echo "tag=latest" >> $GITHUB_OUTPUT
          fi
          
      - name: Publish to NPM
        run: |
          if [ "${{ steps.tag.outputs.tag }}" = "latest" ]; then
            npm publish
          else
            npm publish --tag ${{ steps.tag.outputs.tag }}
          fi
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          
      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: ${{ steps.tag.outputs.tag != 'latest' }}

  publish-manual:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build package
        run: npm run build
        
      - name: Publish to NPM
        run: |
          if [ "${{ github.event.inputs.tag }}" = "latest" ]; then
            npm publish
          else
            npm publish --tag ${{ github.event.inputs.tag }}
          fi
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
