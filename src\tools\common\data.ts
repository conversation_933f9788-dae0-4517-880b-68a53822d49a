import { z } from 'zod';
import { BaseTool } from '../base.js';
import { ToolConfig, ToolContext, ToolExecutionResult, ToolCategory } from '../types.js';

/**
 * JSON processor tool input schema
 */
const JsonProcessorInputSchema = z.object({
  data: z.union([z.string(), z.array(z.any()), z.object({}).passthrough()]),
  operation: z.enum([
    'parse', 'stringify', 'validate', 'transform', 'filter', 'sort', 'group', 'aggregate'
  ]),
  options: z.object({
    path: z.string().optional(),
    filter: z.string().optional(),
    sortBy: z.string().optional(),
    groupBy: z.string().optional(),
    aggregateBy: z.string().optional(),
    aggregateFunction: z.enum(['sum', 'avg', 'count', 'min', 'max']).optional(),
    schema: z.any().optional()
  }).optional()
});

type JsonProcessorInput = z.infer<typeof JsonProcessorInputSchema>;

/**
 * JSON processor tool
 */
export class JsonProcessorTool extends BaseTool<JsonProcessorInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'json_processor',
      title: 'JSON Processor',
      description: 'Process, transform, and analyze JSON data',
      category: ToolCategory.DATA,
      tags: ['json', 'data', 'transform', 'filter', 'sort'],
      examples: [
        {
          description: 'Parse JSON string',
          input: {
            data: '{"name": "John", "age": 30}',
            operation: 'parse'
          }
        },
        {
          description: 'Filter array of objects',
          input: {
            data: [{ name: 'John', age: 30 }, { name: 'Jane', age: 25 }],
            operation: 'filter',
            options: { filter: 'age > 25' }
          }
        }
      ]
    };

    super(config, JsonProcessorInputSchema, undefined);
  }

  async execute(input: JsonProcessorInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      let data = input.data;
      const options = input.options || {};

      // Parse string data if needed
      if (typeof data === 'string' && input.operation !== 'stringify') {
        try {
          data = JSON.parse(data);
        } catch {
          return this.createErrorResult('Invalid JSON string provided');
        }
      }

      let result: any;

      switch (input.operation) {
        case 'parse':
          if (typeof input.data !== 'string') {
            return this.createErrorResult('Parse operation requires string input');
          }
          result = JSON.parse(input.data);
          break;

        case 'stringify':
          result = JSON.stringify(data, null, 2);
          break;

        case 'validate':
          // Simple validation - in real implementation, use JSON Schema
          result = { valid: true, data };
          break;

        case 'transform':
          if (options.path) {
            result = this.getValueByPath(data, options.path);
          } else {
            result = data;
          }
          break;

        case 'filter':
          if (!Array.isArray(data)) {
            return this.createErrorResult('Filter operation requires array input');
          }
          result = this.filterArray(data, options.filter);
          break;

        case 'sort':
          if (!Array.isArray(data)) {
            return this.createErrorResult('Sort operation requires array input');
          }
          result = this.sortArray(data, options.sortBy);
          break;

        case 'group':
          if (!Array.isArray(data)) {
            return this.createErrorResult('Group operation requires array input');
          }
          result = this.groupArray(data, options.groupBy);
          break;

        case 'aggregate':
          if (!Array.isArray(data)) {
            return this.createErrorResult('Aggregate operation requires array input');
          }
          result = this.aggregateArray(data, options.aggregateBy, options.aggregateFunction);
          break;

        default:
          return this.createErrorResult(`Unknown operation: ${input.operation}`);
      }

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `JSON ${input.operation} completed successfully:\n${JSON.stringify(result, null, 2)}`
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `JSON processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private getValueByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private filterArray(array: any[], filterExpr?: string): any[] {
    if (!filterExpr) return array;
    
    // Simple filter implementation - in real use, you'd want a proper expression parser
    return array.filter(item => {
      // Basic comparison support
      if (filterExpr.includes('>')) {
        const [field, value] = filterExpr.split('>').map(s => s.trim());
        return item[field] > parseFloat(value);
      }
      if (filterExpr.includes('<')) {
        const [field, value] = filterExpr.split('<').map(s => s.trim());
        return item[field] < parseFloat(value);
      }
      if (filterExpr.includes('=')) {
        const [field, value] = filterExpr.split('=').map(s => s.trim());
        return item[field] === value.replace(/['"]/g, '');
      }
      return true;
    });
  }

  private sortArray(array: any[], sortBy?: string): any[] {
    if (!sortBy) return array;
    
    return [...array].sort((a, b) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];
      
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return aVal - bVal;
      }
      
      return String(aVal).localeCompare(String(bVal));
    });
  }

  private groupArray(array: any[], groupBy?: string): Record<string, any[]> {
    if (!groupBy) return { all: array };
    
    return array.reduce((groups, item) => {
      const key = item[groupBy] || 'undefined';
      if (!groups[key]) groups[key] = [];
      groups[key].push(item);
      return groups;
    }, {} as Record<string, any[]>);
  }

  private aggregateArray(array: any[], aggregateBy?: string, func?: string): any {
    if (!aggregateBy || !func) return array.length;
    
    const values = array.map(item => item[aggregateBy]).filter(val => typeof val === 'number');
    
    switch (func) {
      case 'sum':
        return values.reduce((sum, val) => sum + val, 0);
      case 'avg':
        return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
      case 'count':
        return values.length;
      case 'min':
        return values.length > 0 ? Math.min(...values) : null;
      case 'max':
        return values.length > 0 ? Math.max(...values) : null;
      default:
        return values.length;
    }
  }
}

/**
 * CSV processor tool input schema
 */
const CsvProcessorInputSchema = z.object({
  data: z.string(),
  operation: z.enum(['parse', 'stringify', 'filter', 'sort', 'transform']),
  options: z.object({
    delimiter: z.string().optional().default(','),
    hasHeader: z.boolean().optional().default(true),
    filter: z.string().optional(),
    sortBy: z.string().optional(),
    columns: z.array(z.string()).optional()
  }).optional()
});

type CsvProcessorInput = z.infer<typeof CsvProcessorInputSchema>;

/**
 * CSV processor tool
 */
export class CsvProcessorTool extends BaseTool<CsvProcessorInput> {
  constructor() {
    const config: ToolConfig = {
      name: 'csv_processor',
      title: 'CSV Processor',
      description: 'Process and transform CSV data',
      category: ToolCategory.DATA,
      tags: ['csv', 'data', 'parse', 'transform'],
      examples: [
        {
          description: 'Parse CSV data',
          input: {
            data: 'name,age,city\nJohn,30,NYC\nJane,25,LA',
            operation: 'parse'
          }
        },
        {
          description: 'Filter CSV rows',
          input: {
            data: 'name,age,city\nJohn,30,NYC\nJane,25,LA',
            operation: 'filter',
            options: { filter: 'age > 25' }
          }
        }
      ]
    };

    super(config, CsvProcessorInputSchema, undefined);
  }

  async execute(input: CsvProcessorInput, _context?: ToolContext): Promise<ToolExecutionResult> {
    try {
      const options = { delimiter: ',', hasHeader: true, ...input.options };
      
      let result: any;

      switch (input.operation) {
        case 'parse':
          result = this.parseCsv(input.data, options);
          break;

        case 'stringify':
          // For stringify, we'd expect the data to be already parsed
          result = input.data; // Simplified
          break;

        case 'filter':
          const parsed = this.parseCsv(input.data, options);
          result = this.filterCsvData(parsed, options.filter);
          break;

        case 'sort':
          const sortParsed = this.parseCsv(input.data, options);
          result = this.sortCsvData(sortParsed, options.sortBy);
          break;

        case 'transform':
          const transformParsed = this.parseCsv(input.data, options);
          result = this.transformCsvData(transformParsed, options.columns);
          break;

        default:
          return this.createErrorResult(`Unknown operation: ${input.operation}`);
      }

      return this.createSuccessResult(result, [
        {
          type: 'text',
          text: `CSV ${input.operation} completed successfully. Processed ${Array.isArray(result) ? result.length : 'N/A'} rows.`
        }
      ]);

    } catch (error) {
      return this.createErrorResult(
        `CSV processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private parseCsv(data: string, options: { delimiter: string; hasHeader: boolean }): any[] {
    const lines = data.trim().split('\n');
    if (lines.length === 0) return [];

    const delimiter = options.delimiter;
    let headers: string[] = [];
    let startIndex = 0;

    if (options.hasHeader) {
      headers = lines[0].split(delimiter).map(h => h.trim());
      startIndex = 1;
    } else {
      // Generate generic headers
      const firstLine = lines[0].split(delimiter);
      headers = firstLine.map((_, i) => `column_${i + 1}`);
    }

    const rows = [];
    for (let i = startIndex; i < lines.length; i++) {
      const values = lines[i].split(delimiter).map(v => v.trim());
      const row: Record<string, string> = {};
      
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      
      rows.push(row);
    }

    return rows;
  }

  private filterCsvData(data: any[], filterExpr?: string): any[] {
    if (!filterExpr) return data;
    
    return data.filter(row => {
      // Simple filter implementation
      if (filterExpr.includes('>')) {
        const [field, value] = filterExpr.split('>').map(s => s.trim());
        return parseFloat(row[field]) > parseFloat(value);
      }
      if (filterExpr.includes('<')) {
        const [field, value] = filterExpr.split('<').map(s => s.trim());
        return parseFloat(row[field]) < parseFloat(value);
      }
      if (filterExpr.includes('=')) {
        const [field, value] = filterExpr.split('=').map(s => s.trim());
        return row[field] === value.replace(/['"]/g, '');
      }
      return true;
    });
  }

  private sortCsvData(data: any[], sortBy?: string): any[] {
    if (!sortBy) return data;
    
    return [...data].sort((a, b) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];
      
      // Try numeric comparison first
      const aNum = parseFloat(aVal);
      const bNum = parseFloat(bVal);
      
      if (!isNaN(aNum) && !isNaN(bNum)) {
        return aNum - bNum;
      }
      
      return String(aVal).localeCompare(String(bVal));
    });
  }

  private transformCsvData(data: any[], columns?: string[]): any[] {
    if (!columns) return data;
    
    return data.map(row => {
      const newRow: Record<string, string> = {};
      columns.forEach(col => {
        newRow[col] = row[col] || '';
      });
      return newRow;
    });
  }
}
